.e-site-editor__content_container {
	flex-direction: column;
	min-height: 100%;
	flex-wrap: nowrap;
}

.e-site-editor__content_container_main {
	flex: 1;
	padding: spacing(30);
}

.e-site-editor__content_container_secondary {
	margin: 0 auto;
	align-items: center;
	border-block-start: 1px solid var(--hr-color);
	padding-block-start: spacing(16);
	padding-block-end: spacing(16);
	padding-inline: spacing(30);
}

.eps-app__content:has(.e-site-editor__content_container_main) {
	padding: 0;
}
