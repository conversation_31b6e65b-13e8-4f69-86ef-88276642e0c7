$e-site-editor-conditions-header-image-button-spacing: spacing(44);
$e-site-editor-conditions-header-image-width: px-to-rem(70);

$e-site-editor-conditions-rows-max-width: px-to-rem(700);
$e-site-editor-conditions-rows-y-spacing: spacing(44);

$e-site-editor-conditions-row-block-start-spacing: spacing(12);

$e-site-editor-conditions-remove-condition-color: tints(400);
$e-site-editor-conditions-remove-condition-font-size: type(size, '18');

$e-site-editor-conditions-row-controls-spacing-end: spacing(10);
$e-site-editor-conditions-row-controls-background: theme-colors(light);
$e-site-editor-conditions-row-controls-dark-background: dark-tints(600);
$e-site-editor-conditions-row-controls-radius: $eps-radius;

$e-site-editor-conditions-row-controls-border: $eps-border-width $eps-border-style tints(100);
$e-site-editor-conditions-row-controls-dark-border: $eps-border-width $eps-border-style tints(700);
//$e-site-editor-conditions-row-controls-dark-border: $eps-border-width $eps-border-style tints(725); //merge after 3.12 is out
$e-site-editor-conditions-row-controls-error-border: $eps-border-width $eps-border-style theme-colors(danger);

$e-site-editor-conditions-conflict-block-start-spacing: spacing(5);
$e-site-editor-conditions-conflict-color: theme-colors(danger);

$e-site-editor-add-button-margin-block-start: spacing(44);
$e-site-editor-add-button-background-color: tints(500);
$e-site-editor-add-button-background-dark-color: dark-tints(500);
$e-site-editor-add-button-color: theme-colors(light);
$e-site-editor-add-button-color-hover-background-color: tints(600);
$e-site-editor-add-button-color-hover-dark-background-color: dark-tints(600);
$e-site-editor-add-button-color-hover-color: theme-colors(light);

$e-site-editor-save-button-container-spacing: spacing(8);
$e-site-editor-save-button-container-margin-top: spacing(16);

$e-site-editor-input-wrapper-border-width: $eps-border-width;
$e-site-editor-input-wrapper-border-style: $eps-border-style;
$e-site-editor-input-wrapper-border-color: tints(100);
$e-site-editor-input-wrapper-border-dark-color: dark-tints(700);
//$e-site-editor-input-wrapper-border-dark-color: dark-tints(725); //merge after 3.12 is out
$e-site-editor-input-wrapper-select-font-size: type(size, "12");
$e-site-editor-input-wrapper-select-height: px-to-rem(40);
$e-site-editor-input-wrapper-select-y-padding: spacing(10);
$e-site-editor-input-wrapper-select-color: tints(700);
//$e-site-editor-input-wrapper-select-color: tints(725); //merge after 3.12 is out
$e-site-editor-input-wrapper-select-dark-color: dark-tints(200);
$e-site-editor-input-wrapper-select-arrow-font-size: type(size, "12");
$e-site-editor-input-wrapper-select-arrow-margin-end: spacing(10);

$e-site-editor-input-wrapper-condition-type-icon-start-spacing: spacing(12);
$e-site-editor-input-wrapper-condition-type-icon-font-size: type(size, "15");
$e-site-editor-input-wrapper-condition-type-icon-color: theme-colors(light);
$e-site-editor-input-wrapper-condition-type-icon-z-index: z-index(dropdown);
$e-site-editor-input-wrapper-condition-type-arrow-color: theme-colors(light);
$e-site-editor-input-wrapper-condition-type-start-padding: px-to-rem(34);
$e-site-editor-input-wrapper-condition-type-width: px-to-rem(120);
$e-site-editor-input-wrapper-condition-type-font-size: type(size, '12');
$e-site-editor-input-wrapper-condition-type-color: theme-colors(light);
$e-site-editor-input-wrapper-condition-include-background-color: tints(500);
$e-site-editor-input-wrapper-condition-include-background-dark-color: dark-tints(600);
$e-site-editor-input-wrapper-condition-exclude-background-color: tints(400);
$e-site-editor-input-wrapper-condition-exclude-background-dark-color: dark-tints(600);

$e-site-editor-input-select2-search-field-color: theme-elements-colors(text-base-color);
$e-site-editor-input-select2-search-field-dark-color: theme-colors(light);

:root {
	--e-site-editor-conditions-row-controls-background: #{$e-site-editor-conditions-row-controls-background};
	--e-site-editor-input-wrapper-border-color: #{$e-site-editor-input-wrapper-border-color};
	--e-site-editor-input-wrapper-select-color: #{$e-site-editor-input-wrapper-select-color};
	--e-site-editor-conditions-row-controls-border: #{$e-site-editor-conditions-row-controls-border};
	--e-site-editor-add-button-background-color: #{$e-site-editor-add-button-background-color};
	--e-site-editor-add-button-color-hover-background-color: #{$e-site-editor-add-button-color-hover-background-color};
	--e-site-editor-input-wrapper-condition-include-background-color:
	#{$e-site-editor-input-wrapper-condition-include-background-color};
	--e-site-editor-input-wrapper-condition-exclude-background-color:
	#{$e-site-editor-input-wrapper-condition-exclude-background-color};

	--e-site-editor-input-select2-search-field-color: #{$e-site-editor-input-select2-search-field-color}
}

.eps-theme-dark {
	--select2-selection-background-color: tints(600);
	--e-site-editor-conditions-row-controls-background: #{$e-site-editor-conditions-row-controls-dark-background};
	--e-site-editor-input-wrapper-border-color: #{$e-site-editor-input-wrapper-border-dark-color};
	--e-site-editor-input-wrapper-select-color: #{$e-site-editor-input-wrapper-select-dark-color};
	--e-site-editor-conditions-row-controls-border: #{$e-site-editor-conditions-row-controls-dark-border};
	--e-site-editor-add-button-background-color: #{$e-site-editor-add-button-background-dark-color};
	--e-site-editor-add-button-color-hover-background-color: #{$e-site-editor-add-button-color-hover-dark-background-color};
	--e-site-editor-input-wrapper-condition-include-background-color:
			#{$e-site-editor-input-wrapper-condition-include-background-dark-color};
	--e-site-editor-input-wrapper-condition-exclude-background-color:
			#{$e-site-editor-input-wrapper-condition-exclude-background-dark-color};
	--e-site-editor-input-select2-search-field-color: #{$e-site-editor-input-select2-search-field-dark-color}

}
