$eps-meta-icon-color: tints(400);
$eps-meta-icon-dark-color: dark-tints(200);
$preview-button-height: spacing(30);
$image-aspect-ratio: var(--card-image-aspect-ratio, #{$ratio-portrait});
$overlay-preview-padding-block-start: calc(#{$image-aspect-ratio} - #{$preview-button-height});
$aspect-ratio-wide: calc(100% * 0.1235);

:root {
	--eps-meta-icon-color: #{$eps-meta-icon-color};
}

.eps-theme-dark {
	--eps-meta-icon-color: #{$eps-meta-icon-dark-color};
}

.e-site-template {
	&__meta-data {
		margin-inline-start: spacing(10);
		@include text-truncate();
		font-size: type(text, xxs);

		&:last-of-type {
			margin-inline-end: auto;
		}

		&:first-of-type {
			margin-inline-start: spacing(16);
		}

		.#{$eps-prefix}icon {
			margin-inline-end: spacing(5);
			color: var(--eps-meta-icon-color);
			font-size: type(text, sm);
		}
	}

	&__placeholder {
		.#{$eps-prefix}card__image {
			filter: var(--placeholder-filter, none);
		}
	}

	&__overlay-preview {
		padding-block-start: $overlay-preview-padding-block-start;
		position: relative;

		&-button {
			font-weight: bold;
			display: flex;
			flex-wrap: wrap;
			height: $preview-button-height;
			width: 100%;
			background-color: var(--card-background-color-hover);
			justify-content: center;
			align-items: center;
			padding-block-start: spacing(10);
			line-height: spacing(20);
			--button-background-color: var(--card-headline-color);

			&::before {
				content: '';
				position: absolute;
				display: block;
				width: 100%;
				top: 0;
				left: 0;
				padding-block-start: $overlay-preview-padding-block-start;
			}

			& > :not(:first-child) {
				margin-inline-start: spacing(5);
			}
		}
	}

	&__edit-btn {
		margin-inline-end: spacing(20);

		.#{$eps-prefix}icon {
			margin-inline-end: spacing(5);
		}
	}

	&__instances {
		.#{$eps-prefix}icon {
			margin-inline-end: spacing(5);
			color: var(--eps-meta-icon-color);
			font-size: type(text, sm)
		}

		&-list {
			@include text-truncate();
		}
	}

	&__edit-conditions {
		margin-inline-start: spacing(16);
		text-decoration: underline;
		font-style: italic;
	}

	&--extended {
		.#{$eps-prefix}card {
			&__figure {
				overflow: auto;
			}

			&__headline {
				flex-grow: 0;
			}
		}
	}

	&--wide {
		--card-image-aspect-ratio: #{$aspect-ratio-wide};
	}
}
