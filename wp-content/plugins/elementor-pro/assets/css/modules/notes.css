#wpadminbar #wp-admin-bar-elementor_notes > .ab-item::before {
  content: "\e918";
  font-family: eicons;
  top: 3px;
  font-size: 18px;
}

.e-route-notes {
  --color-editor-info: #58d0f5;
}
.e-route-notes [data-radix-portal] *:focus {
  outline: none;
}
.e-route-notes.e-route-notes--notable .elementor-element, .e-route-notes.e-route-notes--notable .elementor-element * {
  cursor: url("data:image/svg+xml,%3Csvg width='22' height='25' viewBox='0 0 34 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M20.0048 38.7339L29.0211 29.5523C31.3985 27.1312 33.0175 24.0466 33.6734 20.6885C34.3293 17.3304 33.9926 13.8497 32.7059 10.6865C31.4192 7.52328 29.2402 4.81963 26.4446 2.91746C23.649 1.01528 20.3622 0 17 0C13.6378 0 10.351 1.01528 7.5554 2.91746C4.75979 4.81963 2.58085 7.52328 1.29414 10.6865C0.00742391 13.8497 -0.329284 17.3304 0.326594 20.6885C0.982471 24.0466 2.60148 27.1312 4.97888 29.5523L13.9974 38.7339C14.7943 39.5446 15.8746 40 17.0011 40C18.1275 40 19.2078 39.5446 20.0048 38.7339ZM17 25.9668C21.6944 25.9668 25.5 22.0915 25.5 17.311C25.5 12.5306 21.6944 8.65524 17 8.65524C12.3056 8.65524 8.5 12.5306 8.5 17.311C8.5 22.0915 12.3056 25.9668 17 25.9668Z' fill='%23A4AFB6'/%3E%3C/svg%3E%0A") 0 25, pointer;
}
.e-route-notes.e-route-notes--notable .elementor-element:hover {
  outline: 1px solid var(--color-editor-info);
}
.e-route-notes.e-route-notes--notable .elementor-element iframe {
  pointer-events: none;
}

/*# sourceMappingURL=notes.css.map */