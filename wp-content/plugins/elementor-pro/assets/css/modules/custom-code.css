@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.wrap.elementor-admin-page-license form.elementor-license-box {
  max-width: 600px;
  background: white;
  margin: 20px 0;
  padding: 20px 20px;
}
.wrap.elementor-admin-page-license form.elementor-license-box h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 0;
  padding-block-end: 20px;
  border-block-end: 1px solid #eee;
}
.wrap.elementor-admin-page-license form.elementor-license-box h3 span {
  flex-grow: 1;
  padding-inline-start: 5px;
}
.wrap.elementor-admin-page-license form.elementor-license-box h3 small {
  font-size: 13px;
  font-weight: normal;
}
.wrap.elementor-admin-page-license form.elementor-license-box label {
  display: block;
  font-size: 1.3em;
  font-weight: 600;
  margin: 1em 0;
}
.wrap.elementor-admin-page-license form.elementor-license-box .button {
  height: 30px;
  margin-inline-start: 15px;
  margin-block-end: 0;
}
.wrap.elementor-admin-page-license form.elementor-license-box p.description {
  margin: 10px 0;
}
.wrap.elementor-admin-page-license form.elementor-license-box .e-row-stretch {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.wrap.elementor-admin-page-license form.elementor-license-box .e-row-divider-bottom {
  padding-block-end: 15px;
  border-block-end: 1px solid #eeeeee;
}
.wrap.elementor-admin-page-license .elementor-box-action {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  margin-block-start: 30px;
}
.wrap.elementor-admin-page-license .elementor-box-action .elementor-manually-link {
  color: #72777c;
  margin-inline-end: 15px;
}
.wrap.elementor-admin-page-license .elementor-box-action .elementor-manually-link:hover {
  color: inherit;
}

#adminmenu #toplevel_page_elementor a[href*=elementor_pro_upgrade_license_menu_link] {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.5;
  background-color: #93003f;
  color: #ffffff;
  margin: 3px 10px 0;
  padding: 5px 0px;
  display: block;
  text-align: center;
  border-radius: 3px;
  transition: all 0.3s;
}
#adminmenu #toplevel_page_elementor a[href*=elementor_pro_upgrade_license_menu_link]:hover, #adminmenu #toplevel_page_elementor a[href*=elementor_pro_upgrade_license_menu_link]:focus {
  background-color: rgb(198, 0, 84.**********);
  box-shadow: none;
}

.fixed .column-elementor_library_type,
.fixed .column-instances {
  width: 10%;
}
.fixed .elementor-shortcode-input {
  min-width: 235px;
}
@media (min-width: 768px) and (max-width: 1440px) {
  .fixed .column-shortcode {
    width: 25%;
  }
  .fixed .elementor-shortcode-input {
    min-width: 100%;
  }
}

#available-widgets [class*=elementor-template] .widget-title:before {
  content: "\e801";
  font-family: eicon;
  font-size: 17px;
}

#elementor-widget-template-empty-templates {
  margin-block-start: 15px;
  text-align: center;
}

.elementor-widget-template-empty-templates-title {
  padding: 25px 0 30px;
}

.elementor-widget-template-empty-templates-icon {
  font-size: 96px;
}

.elementor-widget-template-empty-templates-footer {
  color: var(--e-a-color-txt-muted);
  font-size: 13px;
  font-style: italic;
  margin-block-end: 15px;
}

.elementor-button-spinner.error:before {
  content: "\f335";
  color: #ff0000;
}

@media screen and (max-width: 782px) {
  .e-form-submissions-list-table {
    /* Don't judge me... (need to override WordPress style). */
  }
  .e-form-submissions-list-table.wp-list-table tr:not(.inline-edit-row):not(.no-items) > td.bulk-checkbox-column:not(.check-column) {
    width: 2.2em !important;
  }
}
.e-form-submissions-list-table .bulk-checkbox-column {
  padding: 9px 0 0 3px;
  width: 2.2em;
}
.e-form-submissions-list-table .column-actions {
  width: 11%;
}
.e-form-submissions-list-table .column-actions i {
  font-size: 15px;
}
.e-form-submissions-list-table .column-id {
  width: 7%;
}
.e-form-submissions-list-table .column-page {
  width: 17%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.e-form-submissions-list-table .column-date {
  width: 17%;
}
.e-form-submissions-list-table .column-form {
  width: 20%;
}

.e-form-submissions-referer-icon {
  font-size: 18px;
}

.e-form-submissions-main .postbox {
  border: none;
}
.e-form-submissions-main .postbox-header {
  border: 1px solid #D5D8DC;
  border-block-end: none;
  padding: 8px;
}
.e-form-submissions-main__header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#e-form-submissions .eicon-success {
  color: #5cb85c;
}
#e-form-submissions .eicon-error {
  color: #d9534f;
}
#e-form-submissions .misc-pub-section {
  line-height: 1.3rem;
}

#e-form-submissions .e-form-submissions-item-table {
  border: none;
  border-collapse: collapse;
}
#e-form-submissions .e-form-submissions-item-table td:first-child {
  width: 150px;
  background: #F9FAFA;
  font-weight: 700;
}
@media screen and (max-width: 782px) {
  #e-form-submissions .e-form-submissions-item-table td:first-child {
    /* Must set !important because wordpress set also !important. */
    width: 90px !important;
  }
}
#e-form-submissions .e-form-submissions-item-table td {
  border: 1px solid #D5D8DC;
  padding: 15px;
}
#e-form-submissions .e-form-submissions-item-table input:not([type=checkbox]):not([type=radio]), #e-form-submissions .e-form-submissions-item-table textarea, #e-form-submissions .e-form-submissions-item-table select {
  width: 100%;
  max-width: 400px;
}

#e-form-submissions .e-export-button {
  margin-inline-start: 15px;
}
@media screen and (max-width: 782px) {
  #e-form-submissions .e-export-button {
    display: none;
  }
}
#e-form-submissions .tablenav .actions select {
  width: 10rem;
}
#e-form-submissions .tablenav .actions .select2-container {
  float: left;
  margin-right: 6px;
}
#e-form-submissions .tablenav .actions .select2-container .select2-selection__arrow {
  height: 30px;
}
#e-form-submissions .tablenav .actions .select2-container .select2-selection--single {
  height: 30px;
  border: 1px solid #7e8993;
}

input[type=search].select2-search__field {
  line-height: 1;
}

.e-form-submissions-search {
  position: relative;
}
.e-form-submissions-search__spinner {
  position: absolute;
  right: 12px;
  font-size: 11px;
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--wp-admin-theme-color-darker-20, #005a87);
}
.e-form-submissions-search:focus-within .e-form-submissions-search__spinner, .e-form-submissions-search:hover .e-form-submissions-search__spinner {
  right: 30px;
}

.e-form-submissions-action-log--success .e-form-submissions-action-log__message {
  background: #dff0d8;
  border-color: #5cb85c;
}
.e-form-submissions-action-log--error .e-form-submissions-action-log__message {
  background: #f2dede;
  border-color: #d9534f;
}
.e-form-submissions-action-log:not(:last-child) {
  border-block-end: 1px solid #D5D8DC;
}
.e-form-submissions-action-log__label {
  display: inline-block;
  padding-inline-end: 15px;
}
.e-form-submissions-action-log__icon {
  display: inline-block;
  padding-inline-end: 3px;
}
.e-form-submissions-action-log__date {
  font-size: 12px;
  color: #69727D;
}
.e-form-submissions-action-log__message {
  padding: 10px;
  font-size: 12px;
  color: #1f2124;
  background: #F1F2F3;
  border-inline-start: 4px solid #69727D;
}

.e-form-submissions-value-label:not(:last-child) {
  display: block;
  margin-block-end: 10px;
}

span.font-variations-count {
  display: inline-block;
  vertical-align: top;
  margin: 1px 0 0 5px;
  padding: 0 5px;
  min-width: 7px;
  height: 17px;
  border-radius: 11px;
  background-color: #d4dffb;
  color: #4278b2;
  font-size: 9px;
  line-height: 17px;
  text-align: center;
  z-index: 26;
}

.post-type-elementor_font #elementor-font-custommetabox {
  background: none;
  border: 0;
}
.post-type-elementor_font #elementor-font-custommetabox button.handlediv {
  display: none;
}
.post-type-elementor_font #elementor-font-custommetabox .inside {
  margin-block-start: 15px;
}
.post-type-elementor_font #elementor-font-custommetabox h2.hndle {
  padding-inline: 0;
  font-size: 16px;
}
.post-type-elementor_font #elementor-font-custommetabox .handle-actions {
  display: none;
}
.post-type-elementor_font #elementor-font-custommetabox .elementor-metabox-content:not(.has-font-variable):not(.has-font-static) {
  display: flex;
}
.post-type-elementor_font #tagsdiv-elementor_font_type,
.post-type-elementor_font #minor-publishing-actions,
.post-type-elementor_font #misc-publishing-actions {
  display: none;
}

.elementor-metabox-content #add-variable-font {
  margin-block-start: 18px;
  margin-inline-start: 18px;
}
.elementor-metabox-content.has-font-variable .repeater-content-bottom {
  display: flex;
  flex-direction: column;
}
.elementor-metabox-content.has-font-variable .variable-width-wrap,
.elementor-metabox-content.has-font-variable .variable-weight-wrap {
  display: flex;
  gap: 20px;
  margin-block: 10px;
  min-height: 30px;
  order: 1;
}
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_weight_min,
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_weight_max,
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_width_min,
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_width_max,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_weight_min,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_weight_max,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_width_min,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_width_max {
  display: none;
}
.elementor-metabox-content.has-font-variable #add-variable-font,
.elementor-metabox-content.has-font-variable .add-repeater-row,
.elementor-metabox-content.has-font-variable .font_weight,
.elementor-metabox-content.has-font-variable .font_style,
.elementor-metabox-content.has-font-variable .row-font-weight,
.elementor-metabox-content.has-font-variable .row-font-style,
.elementor-metabox-content.has-font-variable .repeater-block .elementor-field-file:not(.ttf) {
  display: none;
}
.elementor-metabox-content.has-font-variable .elementor-field-input {
  display: flex;
  align-items: center;
  min-width: 80px;
}
.elementor-metabox-content.has-font-variable .variable-description {
  order: 1;
  margin-block: 20px 10px;
}
.elementor-metabox-content.has-font-variable .variable_width .elementor-field-label,
.elementor-metabox-content.has-font-variable .variable_weight .elementor-field-label {
  order: 1;
}
.elementor-metabox-content.has-font-variable .variable_width_min label,
.elementor-metabox-content.has-font-variable .variable_width_max label,
.elementor-metabox-content.has-font-variable .variable_weight_min label,
.elementor-metabox-content.has-font-variable .variable_weight_max label {
  min-width: 80px;
}
.elementor-metabox-content.has-font-variable .variable_width_min input,
.elementor-metabox-content.has-font-variable .variable_width_max input,
.elementor-metabox-content.has-font-variable .variable_weight_min input,
.elementor-metabox-content.has-font-variable .variable_weight_max input {
  max-width: 80px;
}
.elementor-metabox-content.has-font-static .variable-width-wrap,
.elementor-metabox-content.has-font-static .variable-weight-wrap,
.elementor-metabox-content.has-font-static .variable-description,
.elementor-metabox-content.has-font-static #add-variable-font {
  display: none;
}
.elementor-metabox-content .repeater-block {
  background: #ffffff;
  color: #3f444b;
  padding: 20px;
  margin-block-end: 2px;
}
.elementor-metabox-content .repeater-block span.elementor-repeater-tool-btn.close-repeater-row {
  display: none;
}
.elementor-metabox-content .repeater-block.block-visible {
  padding-block-end: 0;
  margin-block-end: 0;
}
.elementor-metabox-content .repeater-block.block-visible span.elementor-repeater-tool-btn.toggle-repeater-row {
  display: none;
}
.elementor-metabox-content .repeater-block.block-visible span.elementor-repeater-tool-btn.close-repeater-row {
  display: inline-block;
}
.elementor-metabox-content .repeater-block:not(.block-visible) .close-repeater-row {
  display: none;
}
.elementor-metabox-content .repeater-block .repeater-title {
  cursor: pointer;
}
.elementor-metabox-content .repeater-block .elementor-field-file {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 10px 20px;
  margin-block-end: 10px;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
}
.elementor-metabox-content .repeater-block .elementor-field-file:last-child {
  margin-block-end: 0;
}
.elementor-metabox-content .repeater-block .elementor-field-file p,
.elementor-metabox-content .repeater-block .elementor-field-file input {
  box-sizing: border-box;
  flex-grow: 1;
  width: 100%;
  margin: 0;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-label {
  font-weight: 500;
  max-width: 120px;
  width: 100%;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-input {
  padding: 5px 8px;
  margin: 0 15px;
  border-radius: 3px;
  font-size: 12px;
  width: 100%;
  background: none;
  box-shadow: none;
  color: #0C0D0E;
  border: 1px solid;
  outline: none;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-input:not(:focus) {
  border-color: #D5D8DC;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-input:focus {
  border-color: #9DA5AE;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-btn,
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-clear-btn {
  max-width: 100px;
  font-size: 11px;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-clear-btn {
  transition: all 0.3s;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-clear-btn:hover {
  background-color: #DC2626;
  border-color: #DC2626;
  box-shadow: none;
  color: white;
}
.elementor-metabox-content .row-font-label {
  padding: 0;
  margin: 0;
  display: flex;
  text-transform: capitalize;
  justify-content: space-between;
  align-items: center;
}
.elementor-metabox-content .row-font-label li {
  box-sizing: border-box;
  flex-grow: 1;
  width: 100%;
  margin: 0;
}
.elementor-metabox-content .row-font-label li span.label {
  font-weight: 500;
  padding-inline-end: 10px;
}
.elementor-metabox-content .row-font-label li.row-font-weight, .elementor-metabox-content .row-font-label li.row-font-style {
  max-width: 180px;
}
.elementor-metabox-content .row-font-label li.row-font-actions {
  max-width: 200px;
  text-align: end;
}
.elementor-metabox-content .repeater-content {
  margin: 0;
}
.elementor-metabox-content .repeater-content .repeater-content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-block-end: 20px;
  line-height: 28px;
}
.elementor-metabox-content .repeater-content .repeater-content-top > div {
  box-sizing: border-box;
  flex-grow: 1;
}
.elementor-metabox-content .repeater-content .repeater-content-top p {
  margin: 0;
  display: inline-block;
}
.elementor-metabox-content .repeater-content .repeater-content-top p label {
  font-weight: 500;
  padding-inline-end: 10px;
}
.elementor-metabox-content .repeater-content .repeater-content-top .elementor-field-select {
  max-width: 180px;
}
.elementor-metabox-content .repeater-content .repeater-content-top .elementor-field-toolbar {
  max-width: 200px;
  text-align: end;
}
.elementor-metabox-content .repeater-content .repeater-content-bottom {
  background-color: #F9FAFA;
  padding: 20px 40px;
  margin: 0 -20px;
}
.elementor-metabox-content input.button.add-repeater-row {
  margin-block-start: 18px;
}
.elementor-metabox-content .elementor-repeater-tool-btn {
  cursor: pointer;
  padding: 0 20px;
  font-size: 12px;
  transition: all 0.3s;
}
.elementor-metabox-content .elementor-repeater-tool-btn i {
  padding-inline-end: 5px;
}
.elementor-metabox-content .elementor-repeater-tool-btn:hover {
  color: #3f444b;
}
.elementor-metabox-content .elementor-repeater-tool-btn.remove-repeater-row:hover {
  color: #DC2626;
}

.row-font-preview,
.inline-preview,
.widefat td.column-font_preview {
  font-size: 30px;
  text-transform: capitalize;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 600px;
  line-height: 1.5;
}

.post-type-elementor_icons .elementor-metabox-content .elementor-button:not([disabled]) {
  margin-block-start: 10px;
}
.post-type-elementor_icons div#postbox-container-1 {
  display: none;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox {
  display: none;
  border: 1px solid #F1F2F3;
  border-radius: 1px;
  background-color: #fff;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .inside {
  margin-block-start: 10px;
  margin-block-end: 20px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-metabox-content {
  background-color: #fff;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-custom-icons-metabox {
  padding-block-start: 4px;
  padding-block-end: 10px;
  padding-inline-start: 10px;
  padding-inline-end: 10px;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-custom-icons-metabox {
    padding: 0;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox h4 {
  color: #1f2124;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: 0.7px;
  line-height: 28px;
  margin: 0 0 4px 0;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox h5 {
  color: #9DA5AE;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  line-height: 21px;
  margin: 0;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor--dropzone--upload__icon i {
  font-size: 64px;
  color: #0A875A;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__uploading,
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__success,
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__error,
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__file {
  display: none;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .is-dragover {
  background-color: grey;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__input {
  padding: 180px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-field-dropzone {
  outline: 2px dashed #D5D8DC;
  outline-offset: -3px;
  background-color: #fff;
  display: none;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons {
  background-color: #F9FAFA;
  border: 1px solid #F1F2F3;
  border-radius: 1px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-metabox-content {
  background-color: #F9FAFA;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-metabox-content .elementor-custom-icons-metabox {
  padding-block-start: 4px;
  padding-block-end: 0;
  padding-inline-start: 10px;
  padding-inline-end: 10px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header {
  height: 50px;
  color: #3f444b;
  background-color: #fff;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
  padding: 0 35px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header {
    padding: 0 6px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div {
  padding-inline-end: 10px;
  padding-inline-start: 10px;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div {
    line-height: 1;
  }
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div.remove {
    font-size: 10px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div:nth-of-type(2) {
  border: 1px solid #9DA5AE;
  border-block-start: 0;
  border-block-end: 0;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta {
  color: #1f2124;
  font-size: 14px;
  line-height: 1;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta {
    font-size: 10px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-value {
  font-weight: bold;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-value {
    font-size: 10px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-remove {
  margin-inline-start: auto;
  color: #1f2124;
  opacity: 0.6;
  cursor: pointer;
  transition: all 0.3s;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-remove i {
  color: #3f444b;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-remove:hover {
  opacity: 1;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-footer {
  color: #BABFC5;
  font-family: Roboto, Arial, Helvetica, sans-serif;
  border-block-start: 1px solid #F1F2F3;
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
  text-align: end;
  padding-block-start: 10px;
  padding-block-end: 10px;
  padding-inline-end: 35px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(105px, 1fr));
  grid-gap: 20px;
  padding-block-start: 15px;
  padding-block-end: 0;
  padding-inline-start: 35px;
  padding-inline-end: 35px;
  overflow-y: auto;
  max-height: 575px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li {
  position: relative;
  height: 0;
  padding-block-end: 100%;
  background-color: #fff;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  overflow: hidden;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li div.icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li div.icon-name {
  color: #BABFC5;
  font-size: 11px;
  padding: 18px 20px 0;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 479px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox ul li div.icon-name {
    display: none;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li i {
  font-size: 32px;
}
.post-type-elementor_icons #tagsdiv-elementor_icon_type,
.post-type-elementor_icons #minor-publishing-actions,
.post-type-elementor_icons #misc-publishing-actions {
  display: none;
}

.column-icons_prefix {
  width: 65%;
}

:root {
  --color-box-shadow-color: rgba(0, 0, 0, 0.05);
}

.eps-theme-dark {
  --color-box-shadow-color: rgba(0, 0, 0, 0.1);
}

.eps-grid-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.eps-grid-container--no-wrap {
  flex-wrap: nowrap;
}
.eps-grid-container--wrap-reverse {
  flex-wrap: wrap-reverse;
}
.eps-grid-container--spacing {
  --grid-row-gutter: calc(-1 * calc(var(--grid-spacing-gutter) * 0.625rem / 10));
  width: var(--grid-spacing-width);
  margin: var(--grid-row-gutter);
}
.eps-grid-container--spacing > .eps-grid-item {
  padding: var(--grid-spacing-gutter);
}
.eps-grid--direction-row {
  flex-direction: row;
}
.eps-grid--direction-row-reverse {
  flex-direction: row-reverse;
}
.eps-grid--direction-column {
  flex-direction: column;
}
.eps-grid--direction-column-reverse {
  flex-direction: column-reverse;
}
.eps-grid--justify-stretch {
  justify-content: stretch;
}
.eps-grid--justify-start {
  justify-content: flex-start;
}
.eps-grid--justify-center {
  justify-content: center;
}
.eps-grid--justify-end {
  justify-content: flex-end;
}
.eps-grid--justify-space-between {
  justify-content: space-between;
}
.eps-grid--justify-space-around {
  justify-content: space-around;
}
.eps-grid--justify-space-evenly {
  justify-content: space-evenly;
}
.eps-grid--align-content-stretch {
  align-content: stretch;
}
.eps-grid--align-content-start {
  align-content: flex-start;
}
.eps-grid--align-content-center {
  align-content: center;
}
.eps-grid--align-content-end {
  align-content: flex-end;
}
.eps-grid--align-content-space-between {
  align-content: space-between;
}
.eps-grid--align-items-start {
  align-items: flex-start;
}
.eps-grid--align-items-center {
  align-items: center;
}
.eps-grid--align-items-end {
  align-items: flex-end;
}
.eps-grid--align-items-baseline {
  align-items: baseline;
}
.eps-grid--align-items-stretch {
  align-items: stretch;
}

.eps-grid-item--zero-min-width {
  min-width: 0;
}

@media screen and (min-width: 480px) {
  .eps-grid-item-sm {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 768px) {
  .eps-grid-item-md {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 1025px) {
  .eps-grid-item-lg {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 1440px) {
  .eps-grid-item-xl {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 1600px) {
  .eps-grid-item-xxl {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
.eps-grid-item-xs-1 {
  flex-grow: 0;
  max-width: calc(1 / 12 * 100%);
  flex-basis: calc(1 / 12 * 100%);
}

.eps-grid-item-xs-2 {
  flex-grow: 0;
  max-width: calc(2 / 12 * 100%);
  flex-basis: calc(2 / 12 * 100%);
}

.eps-grid-item-xs-3 {
  flex-grow: 0;
  max-width: calc(3 / 12 * 100%);
  flex-basis: calc(3 / 12 * 100%);
}

.eps-grid-item-xs-4 {
  flex-grow: 0;
  max-width: calc(4 / 12 * 100%);
  flex-basis: calc(4 / 12 * 100%);
}

.eps-grid-item-xs-5 {
  flex-grow: 0;
  max-width: calc(5 / 12 * 100%);
  flex-basis: calc(5 / 12 * 100%);
}

.eps-grid-item-xs-6 {
  flex-grow: 0;
  max-width: calc(6 / 12 * 100%);
  flex-basis: calc(6 / 12 * 100%);
}

.eps-grid-item-xs-7 {
  flex-grow: 0;
  max-width: calc(7 / 12 * 100%);
  flex-basis: calc(7 / 12 * 100%);
}

.eps-grid-item-xs-8 {
  flex-grow: 0;
  max-width: calc(8 / 12 * 100%);
  flex-basis: calc(8 / 12 * 100%);
}

.eps-grid-item-xs-9 {
  flex-grow: 0;
  max-width: calc(9 / 12 * 100%);
  flex-basis: calc(9 / 12 * 100%);
}

.eps-grid-item-xs-10 {
  flex-grow: 0;
  max-width: calc(10 / 12 * 100%);
  flex-basis: calc(10 / 12 * 100%);
}

.eps-grid-item-xs-11 {
  flex-grow: 0;
  max-width: calc(11 / 12 * 100%);
  flex-basis: calc(11 / 12 * 100%);
}

.eps-grid-item-xs-12 {
  flex-grow: 0;
  max-width: calc(12 / 12 * 100%);
  flex-basis: calc(12 / 12 * 100%);
}

@media screen and (min-width: 480px) {
  .eps-grid-item-sm-1 {
    flex-grow: 0;
    max-width: calc(1 / 12 * 100%);
    flex-basis: calc(1 / 12 * 100%);
  }
  .eps-grid-item-sm-2 {
    flex-grow: 0;
    max-width: calc(2 / 12 * 100%);
    flex-basis: calc(2 / 12 * 100%);
  }
  .eps-grid-item-sm-3 {
    flex-grow: 0;
    max-width: calc(3 / 12 * 100%);
    flex-basis: calc(3 / 12 * 100%);
  }
  .eps-grid-item-sm-4 {
    flex-grow: 0;
    max-width: calc(4 / 12 * 100%);
    flex-basis: calc(4 / 12 * 100%);
  }
  .eps-grid-item-sm-5 {
    flex-grow: 0;
    max-width: calc(5 / 12 * 100%);
    flex-basis: calc(5 / 12 * 100%);
  }
  .eps-grid-item-sm-6 {
    flex-grow: 0;
    max-width: calc(6 / 12 * 100%);
    flex-basis: calc(6 / 12 * 100%);
  }
  .eps-grid-item-sm-7 {
    flex-grow: 0;
    max-width: calc(7 / 12 * 100%);
    flex-basis: calc(7 / 12 * 100%);
  }
  .eps-grid-item-sm-8 {
    flex-grow: 0;
    max-width: calc(8 / 12 * 100%);
    flex-basis: calc(8 / 12 * 100%);
  }
  .eps-grid-item-sm-9 {
    flex-grow: 0;
    max-width: calc(9 / 12 * 100%);
    flex-basis: calc(9 / 12 * 100%);
  }
  .eps-grid-item-sm-10 {
    flex-grow: 0;
    max-width: calc(10 / 12 * 100%);
    flex-basis: calc(10 / 12 * 100%);
  }
  .eps-grid-item-sm-11 {
    flex-grow: 0;
    max-width: calc(11 / 12 * 100%);
    flex-basis: calc(11 / 12 * 100%);
  }
  .eps-grid-item-sm-12 {
    flex-grow: 0;
    max-width: calc(12 / 12 * 100%);
    flex-basis: calc(12 / 12 * 100%);
  }
}
@media screen and (min-width: 768px) {
  .eps-grid-item-md-1 {
    flex-grow: 0;
    max-width: calc(1 / 12 * 100%);
    flex-basis: calc(1 / 12 * 100%);
  }
  .eps-grid-item-md-2 {
    flex-grow: 0;
    max-width: calc(2 / 12 * 100%);
    flex-basis: calc(2 / 12 * 100%);
  }
  .eps-grid-item-md-3 {
    flex-grow: 0;
    max-width: calc(3 / 12 * 100%);
    flex-basis: calc(3 / 12 * 100%);
  }
  .eps-grid-item-md-4 {
    flex-grow: 0;
    max-width: calc(4 / 12 * 100%);
    flex-basis: calc(4 / 12 * 100%);
  }
  .eps-grid-item-md-5 {
    flex-grow: 0;
    max-width: calc(5 / 12 * 100%);
    flex-basis: calc(5 / 12 * 100%);
  }
  .eps-grid-item-md-6 {
    flex-grow: 0;
    max-width: calc(6 / 12 * 100%);
    flex-basis: calc(6 / 12 * 100%);
  }
  .eps-grid-item-md-7 {
    flex-grow: 0;
    max-width: calc(7 / 12 * 100%);
    flex-basis: calc(7 / 12 * 100%);
  }
  .eps-grid-item-md-8 {
    flex-grow: 0;
    max-width: calc(8 / 12 * 100%);
    flex-basis: calc(8 / 12 * 100%);
  }
  .eps-grid-item-md-9 {
    flex-grow: 0;
    max-width: calc(9 / 12 * 100%);
    flex-basis: calc(9 / 12 * 100%);
  }
  .eps-grid-item-md-10 {
    flex-grow: 0;
    max-width: calc(10 / 12 * 100%);
    flex-basis: calc(10 / 12 * 100%);
  }
  .eps-grid-item-md-11 {
    flex-grow: 0;
    max-width: calc(11 / 12 * 100%);
    flex-basis: calc(11 / 12 * 100%);
  }
  .eps-grid-item-md-12 {
    flex-grow: 0;
    max-width: calc(12 / 12 * 100%);
    flex-basis: calc(12 / 12 * 100%);
  }
}
@media screen and (min-width: 1025px) {
  .eps-grid-item-lg-1 {
    flex-grow: 0;
    max-width: calc(1 / 12 * 100%);
    flex-basis: calc(1 / 12 * 100%);
  }
  .eps-grid-item-lg-2 {
    flex-grow: 0;
    max-width: calc(2 / 12 * 100%);
    flex-basis: calc(2 / 12 * 100%);
  }
  .eps-grid-item-lg-3 {
    flex-grow: 0;
    max-width: calc(3 / 12 * 100%);
    flex-basis: calc(3 / 12 * 100%);
  }
  .eps-grid-item-lg-4 {
    flex-grow: 0;
    max-width: calc(4 / 12 * 100%);
    flex-basis: calc(4 / 12 * 100%);
  }
  .eps-grid-item-lg-5 {
    flex-grow: 0;
    max-width: calc(5 / 12 * 100%);
    flex-basis: calc(5 / 12 * 100%);
  }
  .eps-grid-item-lg-6 {
    flex-grow: 0;
    max-width: calc(6 / 12 * 100%);
    flex-basis: calc(6 / 12 * 100%);
  }
  .eps-grid-item-lg-7 {
    flex-grow: 0;
    max-width: calc(7 / 12 * 100%);
    flex-basis: calc(7 / 12 * 100%);
  }
  .eps-grid-item-lg-8 {
    flex-grow: 0;
    max-width: calc(8 / 12 * 100%);
    flex-basis: calc(8 / 12 * 100%);
  }
  .eps-grid-item-lg-9 {
    flex-grow: 0;
    max-width: calc(9 / 12 * 100%);
    flex-basis: calc(9 / 12 * 100%);
  }
  .eps-grid-item-lg-10 {
    flex-grow: 0;
    max-width: calc(10 / 12 * 100%);
    flex-basis: calc(10 / 12 * 100%);
  }
  .eps-grid-item-lg-11 {
    flex-grow: 0;
    max-width: calc(11 / 12 * 100%);
    flex-basis: calc(11 / 12 * 100%);
  }
  .eps-grid-item-lg-12 {
    flex-grow: 0;
    max-width: calc(12 / 12 * 100%);
    flex-basis: calc(12 / 12 * 100%);
  }
}
@media screen and (min-width: 1440px) {
  .eps-grid-item-xl-1 {
    flex-grow: 0;
    max-width: calc(1 / 12 * 100%);
    flex-basis: calc(1 / 12 * 100%);
  }
  .eps-grid-item-xl-2 {
    flex-grow: 0;
    max-width: calc(2 / 12 * 100%);
    flex-basis: calc(2 / 12 * 100%);
  }
  .eps-grid-item-xl-3 {
    flex-grow: 0;
    max-width: calc(3 / 12 * 100%);
    flex-basis: calc(3 / 12 * 100%);
  }
  .eps-grid-item-xl-4 {
    flex-grow: 0;
    max-width: calc(4 / 12 * 100%);
    flex-basis: calc(4 / 12 * 100%);
  }
  .eps-grid-item-xl-5 {
    flex-grow: 0;
    max-width: calc(5 / 12 * 100%);
    flex-basis: calc(5 / 12 * 100%);
  }
  .eps-grid-item-xl-6 {
    flex-grow: 0;
    max-width: calc(6 / 12 * 100%);
    flex-basis: calc(6 / 12 * 100%);
  }
  .eps-grid-item-xl-7 {
    flex-grow: 0;
    max-width: calc(7 / 12 * 100%);
    flex-basis: calc(7 / 12 * 100%);
  }
  .eps-grid-item-xl-8 {
    flex-grow: 0;
    max-width: calc(8 / 12 * 100%);
    flex-basis: calc(8 / 12 * 100%);
  }
  .eps-grid-item-xl-9 {
    flex-grow: 0;
    max-width: calc(9 / 12 * 100%);
    flex-basis: calc(9 / 12 * 100%);
  }
  .eps-grid-item-xl-10 {
    flex-grow: 0;
    max-width: calc(10 / 12 * 100%);
    flex-basis: calc(10 / 12 * 100%);
  }
  .eps-grid-item-xl-11 {
    flex-grow: 0;
    max-width: calc(11 / 12 * 100%);
    flex-basis: calc(11 / 12 * 100%);
  }
  .eps-grid-item-xl-12 {
    flex-grow: 0;
    max-width: calc(12 / 12 * 100%);
    flex-basis: calc(12 / 12 * 100%);
  }
}
@media screen and (min-width: 1600px) {
  .eps-grid-item-xxl-1 {
    flex-grow: 0;
    max-width: calc(1 / 12 * 100%);
    flex-basis: calc(1 / 12 * 100%);
  }
  .eps-grid-item-xxl-2 {
    flex-grow: 0;
    max-width: calc(2 / 12 * 100%);
    flex-basis: calc(2 / 12 * 100%);
  }
  .eps-grid-item-xxl-3 {
    flex-grow: 0;
    max-width: calc(3 / 12 * 100%);
    flex-basis: calc(3 / 12 * 100%);
  }
  .eps-grid-item-xxl-4 {
    flex-grow: 0;
    max-width: calc(4 / 12 * 100%);
    flex-basis: calc(4 / 12 * 100%);
  }
  .eps-grid-item-xxl-5 {
    flex-grow: 0;
    max-width: calc(5 / 12 * 100%);
    flex-basis: calc(5 / 12 * 100%);
  }
  .eps-grid-item-xxl-6 {
    flex-grow: 0;
    max-width: calc(6 / 12 * 100%);
    flex-basis: calc(6 / 12 * 100%);
  }
  .eps-grid-item-xxl-7 {
    flex-grow: 0;
    max-width: calc(7 / 12 * 100%);
    flex-basis: calc(7 / 12 * 100%);
  }
  .eps-grid-item-xxl-8 {
    flex-grow: 0;
    max-width: calc(8 / 12 * 100%);
    flex-basis: calc(8 / 12 * 100%);
  }
  .eps-grid-item-xxl-9 {
    flex-grow: 0;
    max-width: calc(9 / 12 * 100%);
    flex-basis: calc(9 / 12 * 100%);
  }
  .eps-grid-item-xxl-10 {
    flex-grow: 0;
    max-width: calc(10 / 12 * 100%);
    flex-basis: calc(10 / 12 * 100%);
  }
  .eps-grid-item-xxl-11 {
    flex-grow: 0;
    max-width: calc(11 / 12 * 100%);
    flex-basis: calc(11 / 12 * 100%);
  }
  .eps-grid-item-xxl-12 {
    flex-grow: 0;
    max-width: calc(12 / 12 * 100%);
    flex-basis: calc(12 / 12 * 100%);
  }
}
:root {
  --eps-modal-background-color: #ffffff;
  --eps-modal-header-background-color: #2563EB;
  --eps-tip-background-color: #F0F7FF;
}

.eps-theme-dark {
  --eps-modal-background-color: #0C0D0E;
  --eps-modal-header-background-color: #0077CC;
  --eps-tip-background-color: #0A1A3D;
}

/** ----------------------------------------------------------------
	EPS Modal
---------------------------------------------------------------- */
.eps-modal {
  max-width: 43.75rem;
  background: var(--eps-modal-background-color);
  border-radius: 0.1875rem;
  animation: fade-in 0.4s ease-in both;
}
.eps-modal__overlay {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  display: flex;
  inset: 0;
  align-items: center;
  justify-content: center;
  z-index: 1030;
}
.eps-modal__header {
  font-size: 0.875rem;
  background: var(--eps-modal-header-background-color);
  height: 2.75rem;
  padding: 0.625rem 1rem;
  border-radius: 0.1875rem;
}
.eps-modal__header, .eps-modal__header .title {
  color: #ffffff;
}
.eps-modal__icon {
  margin-inline-end: 0.625rem;
}
.eps-modal__body {
  padding: 1.875rem;
}
.eps-modal__tip, .eps-modal .eps-tip {
  padding: 0.5rem;
  padding-inline-start: 0.75rem;
  border-inline-start: 3px solid #2563EB;
  background-color: var(--eps-tip-background-color);
}
.eps-modal__tip:not(:last-child), .eps-modal .eps-tip:not(:last-child) {
  margin-block-end: 1.875rem;
}
.eps-modal__tip:not(:first-child), .eps-modal .eps-tip:not(:first-child) {
  margin-block-start: 1.875rem;
}
.eps-modal__section:not(:first-child) {
  margin-block-start: 1.875rem;
}
.eps-modal__close-wrapper {
  padding-inline-start: 1rem;
  border-inline-start: solid 1px #ffffff;
}

.eps-button {
  display: inline-flex;
  --button-line-height: 16px;
  --button-padding-y: 0.5em;
  --button-padding-x: 1.5em;
  --button-primary-background-color: #F3BAFD;
  --button-primary-hover-background-color: #F5D0FE;
  --button-primary-active-background-color: #F3BAFD;
  --button-primary-color: #0C0D0E;
  --button-secondary-background-color: #69727D;
  --button-secondary-hover-background-color: rgb(81.7173913043, 88.7217391304, 97.2826086957);
  --button-secondary-active-background-color: rgb(58.4347826087, 63.4434782609, 69.5652173913);
  --button-secondary-color: #ffffff;
  --button-danger-background-color: #DC2626;
  --button-danger-hover-background-color: rgb(178.25, 28.75, 28.75);
  --button-danger-active-background-color: rgb(134.3333333333, 21.6666666667, 21.6666666667);
  --button-danger-color: #ffffff;
  --button-cta-background-color: #93003f;
  --button-cta-hover-background-color: rgb(96, 0, 41.1428571429);
  --button-cta-active-background-color: rgb(45, 0, 19.2857142857);
  --button-cta-color: #ffffff;
  --button-brand-background-color: #93003f;
  --button-brand-hover-background-color: rgb(96, 0, 41.1428571429);
  --button-brand-active-background-color: rgb(45, 0, 19.2857142857);
  --button-brand-color: #ffffff;
  --button-link-background-color: #515962;
  --button-link-hover-background-color: rgb(57.9217877095, 63.6424581006, 70.0782122905);
  --button-link-active-background-color: rgb(34.843575419, 38.2849162011, 42.156424581);
  --button-link-color: #ffffff;
  --button-disabled-background-color: #D5D8DC;
  --button-disabled-hover-background-color: rgb(185.1818181818, 190.1688311688, 196.8181818182);
  --button-disabled-active-background-color: rgb(157.3636363636, 164.3376623377, 173.6363636364);
  --button-disabled-color: #ffffff;
  color: var(--button-background-color, currentColor);
  font-size: var(--button-font-size, inherit);
  font-weight: 500;
  line-height: var(--button-line-height);
  transition: var(--e-a-transition-hover);
  cursor: pointer;
}
.eps-button:active {
  --button-background-color: var(--button-active-background-color, transparent);
}
.eps-button:hover {
  --button-background-color: var(--button-hover-background-color);
}
.eps-theme-dark .eps-button {
  --button-primary-background-color: #F3BAFD;
  --button-primary-color: #0C0D0E;
  --button-primary-hover-background-color: #EB8EFB;
  --button-primary-active-background-color: #F3BAFD;
  --button-secondary-background-color: #BABFC5;
  --button-secondary-color: #fff;
  --button-secondary-hover-background-color: rgb(158.2913385827, 165.2992125984, 173.7086614173);
  --button-secondary-active-background-color: rgb(130.5826771654, 139.5984251969, 150.4173228346);
  --button-cta-background-color: #93003f;
  --button-cta-hover-background-color: rgb(96, 0, 41.1428571429);
  --button-cta-active-background-color: rgb(45, 0, 19.2857142857);
  --button-cta-color: #fff;
  --button-brand-hover-background-color: rgb(96, 0, 41.1428571429);
  --button-brand-active-background-color: rgb(45, 0, 19.2857142857);
  --button-brand-color: #fff;
  --button-brand-background-color: #93003f;
  --button-link-background-color: #515962;
  --button-link-hover-background-color: rgb(57.9217877095, 63.6424581006, 70.0782122905);
  --button-link-active-background-color: rgb(34.843575419, 38.2849162011, 42.156424581);
  --button-link-color: #ffffff;
  --button-disabled-background-color: #69727D;
  --button-disabled-hover-background-color: rgb(81.7173913043, 88.7217391304, 97.2826086957);
  --button-disabled-active-background-color: rgb(58.4347826087, 63.4434782609, 69.5652173913);
  --button-disabled-color: #fff;
}
.eps-button--contained {
  color: var(--button-color);
  padding: var(--button-padding-y) var(--button-padding-x);
  background-color: var(--button-background-color, transparent);
  border: 1px solid var(--button-background-color);
}
.eps-button--contained:hover {
  color: var(--button-color);
}
.eps-button--outlined {
  display: block;
  padding: var(--button-padding-y) var(--button-padding-x);
  border: 1px solid var(--button-background-color);
}
.eps-button--contained, .eps-button--outlined {
  border-radius: 0.1875rem;
}
.eps-button--underlined {
  text-decoration: underline;
}
.eps-button--sm {
  --button-font-size: 0.75rem;
  --button-line-height: 14px;
}
.eps-button--lg {
  --button-font-size: 0.9375rem;
  --button-line-height: 18px;
}
.eps-button--primary {
  --button-color: var(--button-primary-color);
  --button-background-color: var(--button-primary-background-color);
  --button-hover-background-color: var(--button-primary-hover-background-color);
  --button-active-background-color: var(--button-primary-active-background-color);
}
.eps-button--secondary {
  --button-color: var(--button-secondary-color);
  --button-background-color: var(--button-secondary-background-color);
  --button-hover-background-color: var(--button-secondary-hover-background-color);
  --button-active-background-color: var(--button-secondary-active-background-color);
}
.eps-button--danger {
  --button-color: var(--button-danger-color);
  --button-background-color: var(--button-danger-background-color);
  --button-hover-background-color: var(--button-danger-hover-background-color);
  --button-active-background-color: var(--button-danger-active-background-color);
}
.eps-button--cta {
  --button-color: var(--button-brand-color);
  --button-background-color: var(--button-cta-background-color);
  --button-hover-background-color: var(--button-cta-hover-background-color);
  --button-active-background-color: var(--button-cta-active-background-color);
}
.eps-button--brand {
  --button-color: var(--button-cta-color);
  --button-background-color: var(--button-cta-background-color);
  --button-hover-background-color: var(--button-cta-hover-background-color);
  --button-active-background-color: var(--button-cta-active-background-color);
}
.eps-button--link {
  --button-color: var(--button-link-color);
  --button-background-color: var(--button-link-background-color);
  --button-hover-background-color: var(--button-link-hover-background-color);
  --button-active-background-color: var(--button-link-active-background-color);
}
.eps-button--disabled, .eps-button[disabled] {
  --button-color: var(--button-disabled-color);
  --button-background-color: var(--button-disabled-background-color);
  --button-hover-background-color: var(--button-disabled-hover-background-color);
  --button-active-background-color: var(--button-disabled-active-background-color);
  cursor: default;
}

:root {
  --e-site-editor-conditions-row-controls-background: #ffffff;
  --e-site-editor-input-wrapper-border-color: #D5D8DC;
  --e-site-editor-input-wrapper-select-color: #3f444b;
  --e-site-editor-conditions-row-controls-border: 1px solid #D5D8DC;
  --e-site-editor-add-button-background-color: #69727D;
  --e-site-editor-add-button-color-hover-background-color: #515962;
  --e-site-editor-input-wrapper-condition-include-background-color:
  #69727D;
  --e-site-editor-input-wrapper-condition-exclude-background-color:
  #818A96;
  --e-site-editor-input-select2-search-field-color: #515962 ;
}

.eps-theme-dark {
  --select2-selection-background-color: tints(600);
  --e-site-editor-conditions-row-controls-background: #515962;
  --e-site-editor-input-wrapper-border-color: #3f444b;
  --e-site-editor-input-wrapper-select-color: #BABFC5;
  --e-site-editor-conditions-row-controls-border: 1px solid #3f444b;
  --e-site-editor-add-button-background-color: #69727D;
  --e-site-editor-add-button-color-hover-background-color: #515962;
  --e-site-editor-input-wrapper-condition-include-background-color:
  		#515962;
  --e-site-editor-input-wrapper-condition-exclude-background-color:
  		#515962;
  --e-site-editor-input-select2-search-field-color: #ffffff ;
}

.e-site-editor-conditions__header {
  text-align: center;
}
.e-site-editor-conditions__header-image {
  display: block;
  margin: 0 auto 2.75rem;
  width: 4.375rem;
}
.e-site-editor-conditions__rows {
  margin: 2.75rem auto;
  max-width: 43.75rem;
}
.e-site-editor-conditions__row {
  display: flex;
  flex-grow: 1;
  margin-block-start: 0.75rem;
}
.e-site-editor-conditions__remove-condition {
  color: #818A96;
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.e-site-editor-conditions__row-controls {
  overflow: hidden;
  margin-inline-end: 0.625rem;
  background-color: var(--e-site-editor-conditions-row-controls-background);
  display: flex;
  width: 100%;
  border: var(--e-site-editor-conditions-row-controls-border);
  border-radius: 0.1875rem;
}
.e-site-editor-conditions__row-controls--error {
  border: 1px solid #DC2626;
}
.e-site-editor-conditions__conflict {
  text-align: center;
  margin-block-start: 0.3125rem;
  color: #DC2626;
}
.e-site-editor-conditions__row-controls-inner {
  width: 100%;
  display: flex;
}
.e-site-editor-conditions__row-controls-inner div {
  flex: 1;
}
.e-site-editor-conditions__add-button-container {
  text-align: center;
}
.e-site-editor-conditions__add-button {
  margin-block-start: 2.75rem;
  background-color: var(--e-site-editor-add-button-background-color);
  color: #ffffff;
  text-transform: uppercase;
}
.e-site-editor-conditions__add-button:hover {
  background-color: var(--e-site-editor-add-button-color-hover-background-color);
  color: #ffffff;
}
.e-site-editor-conditions__footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem;
  margin-top: 1rem;
}
.e-site-editor-conditions__input-wrapper {
  position: relative;
  padding-inline-start: 1px solid;
  border-color: var(--e-site-editor-input-wrapper-border-color);
}
.e-site-editor-conditions__input-wrapper:first-child {
  border: none;
}
.e-site-editor-conditions__input-wrapper select {
  appearance: none;
  -webkit-appearance: none;
  font-size: 0.75rem;
  height: 2.5rem;
  border-width: 0;
  padding: 0 0.625rem;
  width: 100%;
  position: relative;
  color: var(--e-site-editor-input-wrapper-select-color);
  outline: none;
  background: transparent;
}
.e-site-editor-conditions__input-wrapper:after {
  font-family: eicons;
  content: "\e8ad";
  font-size: 0.75rem;
  pointer-events: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0.625rem;
}
.e-site-editor-conditions__input-wrapper .select2-container--default .select2-selection--single {
  border: none;
  line-height: 2.5rem;
}
.e-site-editor-conditions__input-wrapper .select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 2.5rem;
  font-size: 0.75rem;
}
.e-site-editor-conditions__input-wrapper .select2-selection {
  outline: none;
  background: transparent;
  height: 2.5rem;
}
.e-site-editor-conditions__input-wrapper .select2-selection__arrow {
  display: none;
}
.e-site-editor-conditions__input-wrapper--condition-type {
  position: relative;
}
.e-site-editor-conditions__input-wrapper--condition-type:before {
  font-family: eicons;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0.75rem;
  font-size: 0.9375rem;
  pointer-events: none;
  z-index: 1000;
}
.e-site-editor-conditions__input-wrapper--condition-type select {
  text-transform: uppercase;
  padding-inline-start: 2.125rem;
  width: 7.5rem;
  font-size: 0.75rem;
  border-inline-end: 1px solid;
  border-color: var(--e-site-editor-input-wrapper-border-color);
}
.e-site-editor-conditions__input-wrapper--condition-type[data-elementor-condition-type=include]:before {
  content: "\e8cc";
}
.e-site-editor-conditions__input-wrapper--condition-type[data-elementor-condition-type=exclude]:before {
  content: "\e8cd";
}

.select2-search__field {
  background-color: transparent;
  color: var(--e-site-editor-input-select2-search-field-color);
}

.misc-pub-visibility {
  display: none;
}

.eps-modal__overlay {
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999; /* After '#adminmenuwrap' */
}

.select2-container {
  z-index: 9999; /* After 'eps-modal__overlay'. */
}

.post-conditions .spinner {
  margin: 0 10px;
  float: none;
  visibility: visible;
}
.post-conditions .eps-button--underlined {
  /* No variable was used since this color comes from wordpress */
  color: #0073aa;
}
.post-conditions .eps-modal {
  position: relative;
  background: #f1f3f5;
  max-width: 1200px;
  max-height: 800px;
  width: 90vw;
  height: 90vh;
  margin: auto;
}
.post-conditions .eps-modal .eps-modal__body {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.post-conditions .eps-modal .eps-h1 {
  font-size: 30px;
  font-weight: 300;
}
.post-conditions .eps-modal .eps-text {
  font-size: 18px;
  line-height: 150%;
  margin: 1em 0;
  color: #9DA5AE;
}
.post-conditions .eps-modal .eps-grid-container {
  width: auto;
}
.post-conditions .eps-modal__header {
  background: #ffffff;
  color: #1f2124;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  padding: 2px 15px;
}
.post-conditions .eps-modal__header .eps-app__logo {
  width: 1.75rem;
  height: 1.75rem;
  line-height: 1.75rem;
  text-align: center;
  font-size: 0.7rem;
  border-radius: 50%;
  color: #ffffff;
  background-color: #93003F;
}
.post-conditions .eps-modal__header .eps-text {
  /* Didn't find better solution */
  position: relative;
  top: 2px;
  font-size: 0.9375rem;
  font-weight: 700;
  text-transform: uppercase;
  color: #6d7882;
}
.post-conditions .eps-modal__header .eps-button {
  font-size: 18px;
}
.post-conditions .eps-modal .e-site-editor-conditions__header {
  padding-block-start: 5vh;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows {
  overflow-y: auto;
  max-height: 30vh;
  margin: 0 auto;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows:empty {
  margin-block-end: 30px;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows:not(:empty) {
  margin-block-end: 20px;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row {
  margin-block-start: 15px;
  padding: 0 14px;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row-controls {
  border-radius: 0;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row-controls select {
  text-transform: none;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row-controls select:focus {
  box-shadow: none;
  border: none;
  color: inherit;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row-controls select:hover {
  color: inherit;
}
.post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row-controls .e-site-editor-conditions__input-wrapper--condition-type select:hover, .post-conditions .eps-modal .e-site-editor-conditions__rows .e-site-editor-conditions__row-controls .e-site-editor-conditions__input-wrapper--condition-type select:focus {
  color: #ffffff;
}
.post-conditions .eps-modal .e-site-editor-conditions__add-button {
  margin-block-start: 0;
}
.post-conditions .eps-modal .e-site-editor-conditions__footer {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 1000;
  border-block-start: 1px solid #d5dadf;
}

#elementor-custom-code.postbox {
  border: 0;
}
#elementor-custom-code .postbox-header {
  display: none;
}
#elementor-custom-code .inside {
  padding: 0;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box {
  margin: 0;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel {
  border: 1px solid #F1F2F3;
  border-block-end: 1px solid #ccd0d4;
  background: #ffffff;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-custom-code-panel-placement {
  padding: 5px 40px;
  margin: 10px 0;
  flex-wrap: wrap;
  gap: 10px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-custom-code-options-placement {
  display: none;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-custom-code-options-placement.show {
  display: flex;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel div {
  display: flex;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field i {
  color: #BABFC5;
  font-size: 15px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field select {
  height: 20px;
  margin-block-start: 7px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field select#location {
  direction: ltr;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field.priority {
  margin-inline-start: auto;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field.location {
  margin-inline-end: 10px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field-label {
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: #3f444b;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field-label:nth-child(even) {
  margin-inline-end: 20px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field-label:nth-child(odd) {
  margin-inline-end: 5px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field-checkboxes {
  padding-block-start: 18px;
}
#elementor-custom-code .inside .elementor-custom-code-meta-box .elementor-custom-code-panel .elementor-field-checkboxes .label {
  position: relative;
  top: -5px;
}
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder {
  border: 1px solid #F1F2F3;
  background: #F9FAFA;
  padding: 40px;
}
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder .elementor-field-label {
  margin: 0;
}
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder .elementor-custom-code-codemirror {
  border: 1px solid #ccd0d4;
  resize: vertical;
  overflow: auto;
  max-width: 100%;
}
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder .elementor-custom-code-codemirror .elementor-field-textarea,
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder .elementor-custom-code-codemirror .CodeMirror.CodeMirror-wrap,
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder .elementor-custom-code-codemirror .CodeMirror-scroll {
  height: 100%;
}
#elementor-custom-code .inside .elementor-custom-code-codemirror-holder .elementor-custom-code-codemirror .CodeMirror-sizer {
  min-height: 300px !important;
  border-right: 0;
}

.post-type-elementor_snippet #minor-publishing-actions,
.post-type-elementor_snippet #save-action {
  display: none;
}

.e--ua-safari #elementor-custom-code .elementor-custom-code-codemirror {
  background-color: var(--e-a-bg-default);
  display: flex;
}
.e--ua-safari #elementor-custom-code .elementor-custom-code-codemirror .elementor-field-textarea {
  width: calc(100% - 8px);
}
.e--ua-safari #elementor-custom-code .elementor-custom-code-codemirror .CodeMirror-sizer {
  box-sizing: border-box;
}

@media (max-width: 850px), (max-height: 825px) {
  .post-conditions .eps-modal {
    width: 100vw;
    height: 90vh;
  }
  .post-conditions .eps-modal .e-site-editor-conditions__header {
    padding-block-start: 0;
  }
  .post-conditions .eps-modal .e-site-editor-conditions__rows {
    max-height: 30vh;
  }
  .post-conditions .eps-modal .e-site-editor-conditions__header-image {
    margin: 0 auto;
  }
}
@media (max-width: 590px), (max-height: 666px) {
  .post-conditions .eps-modal .eps-modal__body {
    overflow: auto;
    height: calc(100vh - 200px);
  }
  .post-conditions .eps-modal .eps-modal__body .e-site-editor-conditions__footer {
    background: #f1f3f5;
  }
}
.elementor-custom-code-panel .e-ai-button {
  all: unset;
  color: #D004D4;
  cursor: pointer;
  transition: var(--e-a-transition-hover);
}
.elementor-custom-code-panel .e-ai-button:hover {
  color: #E73CF6;
}

/*# sourceMappingURL=custom-code.css.map */