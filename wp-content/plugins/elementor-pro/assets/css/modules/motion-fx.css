.elementor-motion-effects-element, .elementor-motion-effects-layer {
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0, 0.33, 0.07, 1.03);
  transition-duration: 1s;
}

.elementor-motion-effects-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  transform-origin: var(--e-transform-origin-y) var(--e-transform-origin-x);
}
.elementor-motion-effects-layer {
  position: absolute;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  background-size: cover;
}
.elementor-motion-effects-perspective {
  perspective: 1200px;
}
.elementor-motion-effects-element {
  transform-origin: var(--e-transform-origin-y) var(--e-transform-origin-x);
}

/*# sourceMappingURL=motion-fx.css.map */