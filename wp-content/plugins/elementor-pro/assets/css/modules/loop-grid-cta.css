.e-loop-empty-view__container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}
.e-loop-empty-view__box {
  min-height: 413px;
  width: 100%;
  display: flex;
}
.e-loop-empty-view__box-inner {
  border: 1px dashed #D5D8DC;
  margin: 10px;
  width: 100%;
  padding: 10px;
  font-size: 12px;
  color: #9DA5AE;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  justify-content: center;
  font-family: Roboto, sans-serif;
}
.e-loop-empty-view__box-inner img {
  max-height: 99px;
  margin-bottom: 28px;
}
.e-loop-empty-view__box-title {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 12px;
}
.e-loop-empty-view__box-description {
  margin-bottom: 12px;
}
.e-loop-empty-view__box-cta {
  color: #ffffff;
  background-color: #9DA5AE;
  padding: 6px 8px;
  border-radius: 3px;
  text-decoration: none;
  transition-property: background, color, box-shadow, opacity;
  transition-duration: 0.3s;
}
.e-loop-empty-view__box-cta:hover {
  background-color: #3f444b;
  color: #ffffff;
}
.e-loop-empty-view__box--active {
  border: 2px solid #5EEAD4;
}

.elementor-widget-loop-grid .elementor-grid.e-loop-empty-view__container {
  grid-column-gap: calc(var(--grid-column-gap) - 20px);
}

.e-loop-empty-view__container.loop-carousel {
  width: 95%;
  margin: 0 auto;
  padding: 30px 0;
}
.e-loop-empty-view__container .e-loop-empty-view__box,
.e-loop-empty-view__container .elementor-swiper-button,
.e-loop-empty-view__container .swiper-pagination {
  opacity: 1;
}

.elementor-swiper-button {
  position: absolute;
  display: inline-flex;
  z-index: 1;
  cursor: default;
  font-size: 25px;
  color: rgba(224, 219, 219, 0.9);
  top: 50%;
  transform: translateY(-50%);
}
.elementor-swiper-button-prev {
  left: 0;
}
.elementor-swiper-button-next {
  right: 0;
}
.elementor-swiper-button svg {
  fill: rgba(224, 219, 219, 0.9);
  height: 1em;
  width: 1em;
}

.elementor-pagination-position-outside .elementor-swiper-button {
  top: calc(50% - 15px);
}

.screen-reader-text, .screen-reader-text span, .ui-helper-hidden-accessible, .elementor-screen-only {
  position: absolute;
  top: -10000em;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.swiper-container-horizontal > .swiper-pagination-bullets,
.swiper-horizontal > .swiper-pagination-bullets {
  bottom: 5px;
  left: 0;
  width: 100%;
}
.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 6px;
}

.swiper-container-horizontal > .swiper-pagination-bullets {
  bottom: 5px;
  left: 0;
  width: 100%;
}
.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 6px;
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination-bullet {
  width: 6px;
  height: 6px;
  display: inline-block;
  border-radius: 50%;
  background: #000;
  opacity: 0.2;
  pointer-events: none;
}
.swiper-pagination-fraction {
  bottom: 5px;
  left: 0;
  width: 100%;
}
.swiper-pagination-custom {
  bottom: 5px;
  left: 0;
  width: 100%;
}
.swiper-pagination-bullets {
  color: #fff;
  cursor: default;
  bottom: 0;
  left: 0;
  width: 100%;
}
.swiper-pagination-bullet-active {
  opacity: 0.8;
}

/*# sourceMappingURL=loop-grid-cta.css.map */