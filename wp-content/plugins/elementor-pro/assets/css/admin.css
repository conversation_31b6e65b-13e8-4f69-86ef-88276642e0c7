/*! elementor-pro - v3.27.0 - 06-02-2025 */
.wrap.elementor-admin-page-license form.elementor-license-box {
  max-width: 600px;
  background: white;
  margin: 20px 0;
  padding: 20px 20px;
}
.wrap.elementor-admin-page-license form.elementor-license-box h3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 0;
  padding-block-end: 20px;
  border-block-end: 1px solid #eee;
}
.wrap.elementor-admin-page-license form.elementor-license-box h3 span {
  flex-grow: 1;
  padding-inline-start: 5px;
}
.wrap.elementor-admin-page-license form.elementor-license-box h3 small {
  font-size: 13px;
  font-weight: normal;
}
.wrap.elementor-admin-page-license form.elementor-license-box label {
  display: block;
  font-size: 1.3em;
  font-weight: 600;
  margin: 1em 0;
}
.wrap.elementor-admin-page-license form.elementor-license-box .button {
  height: 30px;
  margin-inline-start: 15px;
  margin-block-end: 0;
}
.wrap.elementor-admin-page-license form.elementor-license-box p.description {
  margin: 10px 0;
}
.wrap.elementor-admin-page-license form.elementor-license-box .e-row-stretch {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.wrap.elementor-admin-page-license form.elementor-license-box .e-row-divider-bottom {
  padding-block-end: 15px;
  border-block-end: 1px solid #eeeeee;
}
.wrap.elementor-admin-page-license .elementor-box-action {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  margin-block-start: 30px;
}
.wrap.elementor-admin-page-license .elementor-box-action .elementor-manually-link {
  color: #72777c;
  margin-inline-end: 15px;
}
.wrap.elementor-admin-page-license .elementor-box-action .elementor-manually-link:hover {
  color: inherit;
}

#adminmenu #toplevel_page_elementor a[href*=elementor_pro_upgrade_license_menu_link] {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.5;
  background-color: #93003f;
  color: #ffffff;
  margin: 3px 10px 0;
  padding: 5px 0px;
  display: block;
  text-align: center;
  border-radius: 3px;
  transition: all 0.3s;
}
#adminmenu #toplevel_page_elementor a[href*=elementor_pro_upgrade_license_menu_link]:hover, #adminmenu #toplevel_page_elementor a[href*=elementor_pro_upgrade_license_menu_link]:focus {
  background-color: rgb(198, 0, 84.**********);
  box-shadow: none;
}

.fixed .column-elementor_library_type,
.fixed .column-instances {
  width: 10%;
}
.fixed .elementor-shortcode-input {
  min-width: 235px;
}
@media (min-width: 768px) and (max-width: 1440px) {
  .fixed .column-shortcode {
    width: 25%;
  }
  .fixed .elementor-shortcode-input {
    min-width: 100%;
  }
}

#available-widgets [class*=elementor-template] .widget-title:before {
  content: "\e801";
  font-family: eicon;
  font-size: 17px;
}

#elementor-widget-template-empty-templates {
  margin-block-start: 15px;
  text-align: center;
}

.elementor-widget-template-empty-templates-title {
  padding: 25px 0 30px;
}

.elementor-widget-template-empty-templates-icon {
  font-size: 96px;
}

.elementor-widget-template-empty-templates-footer {
  color: var(--e-a-color-txt-muted);
  font-size: 13px;
  font-style: italic;
  margin-block-end: 15px;
}

.elementor-button-spinner.error:before {
  content: "\f335";
  color: #ff0000;
}

@media screen and (max-width: 782px) {
  .e-form-submissions-list-table {
    /* Don't judge me... (need to override WordPress style). */
  }
  .e-form-submissions-list-table.wp-list-table tr:not(.inline-edit-row):not(.no-items) > td.bulk-checkbox-column:not(.check-column) {
    width: 2.2em !important;
  }
}
.e-form-submissions-list-table .bulk-checkbox-column {
  padding: 9px 0 0 3px;
  width: 2.2em;
}
.e-form-submissions-list-table .column-actions {
  width: 11%;
}
.e-form-submissions-list-table .column-actions i {
  font-size: 15px;
}
.e-form-submissions-list-table .column-id {
  width: 7%;
}
.e-form-submissions-list-table .column-page {
  width: 17%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.e-form-submissions-list-table .column-date {
  width: 17%;
}
.e-form-submissions-list-table .column-form {
  width: 20%;
}

.e-form-submissions-referer-icon {
  font-size: 18px;
}

.e-form-submissions-main .postbox {
  border: none;
}
.e-form-submissions-main .postbox-header {
  border: 1px solid #D5D8DC;
  border-block-end: none;
  padding: 8px;
}
.e-form-submissions-main__header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#e-form-submissions .eicon-success {
  color: #5cb85c;
}
#e-form-submissions .eicon-error {
  color: #d9534f;
}
#e-form-submissions .misc-pub-section {
  line-height: 1.3rem;
}

#e-form-submissions .e-form-submissions-item-table {
  border: none;
  border-collapse: collapse;
}
#e-form-submissions .e-form-submissions-item-table td:first-child {
  width: 150px;
  background: #F9FAFA;
  font-weight: 700;
}
@media screen and (max-width: 782px) {
  #e-form-submissions .e-form-submissions-item-table td:first-child {
    /* Must set !important because wordpress set also !important. */
    width: 90px !important;
  }
}
#e-form-submissions .e-form-submissions-item-table td {
  border: 1px solid #D5D8DC;
  padding: 15px;
}
#e-form-submissions .e-form-submissions-item-table input:not([type=checkbox]):not([type=radio]), #e-form-submissions .e-form-submissions-item-table textarea, #e-form-submissions .e-form-submissions-item-table select {
  width: 100%;
  max-width: 400px;
}

#e-form-submissions .e-export-button {
  margin-inline-start: 15px;
}
@media screen and (max-width: 782px) {
  #e-form-submissions .e-export-button {
    display: none;
  }
}
#e-form-submissions .tablenav .actions select {
  width: 10rem;
}
#e-form-submissions .tablenav .actions .select2-container {
  float: left;
  margin-right: 6px;
}
#e-form-submissions .tablenav .actions .select2-container .select2-selection__arrow {
  height: 30px;
}
#e-form-submissions .tablenav .actions .select2-container .select2-selection--single {
  height: 30px;
  border: 1px solid #7e8993;
}

input[type=search].select2-search__field {
  line-height: 1;
}

.e-form-submissions-search {
  position: relative;
}
.e-form-submissions-search__spinner {
  position: absolute;
  right: 12px;
  font-size: 11px;
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--wp-admin-theme-color-darker-20, #005a87);
}
.e-form-submissions-search:focus-within .e-form-submissions-search__spinner, .e-form-submissions-search:hover .e-form-submissions-search__spinner {
  right: 30px;
}

.e-form-submissions-action-log--success .e-form-submissions-action-log__message {
  background: #dff0d8;
  border-color: #5cb85c;
}
.e-form-submissions-action-log--error .e-form-submissions-action-log__message {
  background: #f2dede;
  border-color: #d9534f;
}
.e-form-submissions-action-log:not(:last-child) {
  border-block-end: 1px solid #D5D8DC;
}
.e-form-submissions-action-log__label {
  display: inline-block;
  padding-inline-end: 15px;
}
.e-form-submissions-action-log__icon {
  display: inline-block;
  padding-inline-end: 3px;
}
.e-form-submissions-action-log__date {
  font-size: 12px;
  color: #69727D;
}
.e-form-submissions-action-log__message {
  padding: 10px;
  font-size: 12px;
  color: #1f2124;
  background: #F1F2F3;
  border-inline-start: 4px solid #69727D;
}

.e-form-submissions-value-label:not(:last-child) {
  display: block;
  margin-block-end: 10px;
}

span.font-variations-count {
  display: inline-block;
  vertical-align: top;
  margin: 1px 0 0 5px;
  padding: 0 5px;
  min-width: 7px;
  height: 17px;
  border-radius: 11px;
  background-color: #d4dffb;
  color: #4278b2;
  font-size: 9px;
  line-height: 17px;
  text-align: center;
  z-index: 26;
}

.post-type-elementor_font #elementor-font-custommetabox {
  background: none;
  border: 0;
}
.post-type-elementor_font #elementor-font-custommetabox button.handlediv {
  display: none;
}
.post-type-elementor_font #elementor-font-custommetabox .inside {
  margin-block-start: 15px;
}
.post-type-elementor_font #elementor-font-custommetabox h2.hndle {
  padding-inline: 0;
  font-size: 16px;
}
.post-type-elementor_font #elementor-font-custommetabox .handle-actions {
  display: none;
}
.post-type-elementor_font #elementor-font-custommetabox .elementor-metabox-content:not(.has-font-variable):not(.has-font-static) {
  display: flex;
}
.post-type-elementor_font #tagsdiv-elementor_font_type,
.post-type-elementor_font #minor-publishing-actions,
.post-type-elementor_font #misc-publishing-actions {
  display: none;
}

.elementor-metabox-content #add-variable-font {
  margin-block-start: 18px;
  margin-inline-start: 18px;
}
.elementor-metabox-content.has-font-variable .repeater-content-bottom {
  display: flex;
  flex-direction: column;
}
.elementor-metabox-content.has-font-variable .variable-width-wrap,
.elementor-metabox-content.has-font-variable .variable-weight-wrap {
  display: flex;
  gap: 20px;
  margin-block: 10px;
  min-height: 30px;
  order: 1;
}
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_weight_min,
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_weight_max,
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_width_min,
.elementor-metabox-content.has-font-variable .variable-width-wrap.e-font-variable-hidden .variable_width_max,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_weight_min,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_weight_max,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_width_min,
.elementor-metabox-content.has-font-variable .variable-weight-wrap.e-font-variable-hidden .variable_width_max {
  display: none;
}
.elementor-metabox-content.has-font-variable #add-variable-font,
.elementor-metabox-content.has-font-variable .add-repeater-row,
.elementor-metabox-content.has-font-variable .font_weight,
.elementor-metabox-content.has-font-variable .font_style,
.elementor-metabox-content.has-font-variable .row-font-weight,
.elementor-metabox-content.has-font-variable .row-font-style,
.elementor-metabox-content.has-font-variable .repeater-block .elementor-field-file:not(.ttf) {
  display: none;
}
.elementor-metabox-content.has-font-variable .elementor-field-input {
  display: flex;
  align-items: center;
  min-width: 80px;
}
.elementor-metabox-content.has-font-variable .variable-description {
  order: 1;
  margin-block: 20px 10px;
}
.elementor-metabox-content.has-font-variable .variable_width .elementor-field-label,
.elementor-metabox-content.has-font-variable .variable_weight .elementor-field-label {
  order: 1;
}
.elementor-metabox-content.has-font-variable .variable_width_min label,
.elementor-metabox-content.has-font-variable .variable_width_max label,
.elementor-metabox-content.has-font-variable .variable_weight_min label,
.elementor-metabox-content.has-font-variable .variable_weight_max label {
  min-width: 80px;
}
.elementor-metabox-content.has-font-variable .variable_width_min input,
.elementor-metabox-content.has-font-variable .variable_width_max input,
.elementor-metabox-content.has-font-variable .variable_weight_min input,
.elementor-metabox-content.has-font-variable .variable_weight_max input {
  max-width: 80px;
}
.elementor-metabox-content.has-font-static .variable-width-wrap,
.elementor-metabox-content.has-font-static .variable-weight-wrap,
.elementor-metabox-content.has-font-static .variable-description,
.elementor-metabox-content.has-font-static #add-variable-font {
  display: none;
}
.elementor-metabox-content .repeater-block {
  background: #ffffff;
  color: #3f444b;
  padding: 20px;
  margin-block-end: 2px;
}
.elementor-metabox-content .repeater-block span.elementor-repeater-tool-btn.close-repeater-row {
  display: none;
}
.elementor-metabox-content .repeater-block.block-visible {
  padding-block-end: 0;
  margin-block-end: 0;
}
.elementor-metabox-content .repeater-block.block-visible span.elementor-repeater-tool-btn.toggle-repeater-row {
  display: none;
}
.elementor-metabox-content .repeater-block.block-visible span.elementor-repeater-tool-btn.close-repeater-row {
  display: inline-block;
}
.elementor-metabox-content .repeater-block:not(.block-visible) .close-repeater-row {
  display: none;
}
.elementor-metabox-content .repeater-block .repeater-title {
  cursor: pointer;
}
.elementor-metabox-content .repeater-block .elementor-field-file {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 10px 20px;
  margin-block-end: 10px;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
}
.elementor-metabox-content .repeater-block .elementor-field-file:last-child {
  margin-block-end: 0;
}
.elementor-metabox-content .repeater-block .elementor-field-file p,
.elementor-metabox-content .repeater-block .elementor-field-file input {
  box-sizing: border-box;
  flex-grow: 1;
  width: 100%;
  margin: 0;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-label {
  font-weight: 500;
  max-width: 120px;
  width: 100%;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-input {
  padding: 5px 8px;
  margin: 0 15px;
  border-radius: 3px;
  font-size: 12px;
  width: 100%;
  background: none;
  box-shadow: none;
  color: #0C0D0E;
  border: 1px solid;
  outline: none;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-input:not(:focus) {
  border-color: #D5D8DC;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-field-input:focus {
  border-color: #9DA5AE;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-btn,
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-clear-btn {
  max-width: 100px;
  font-size: 11px;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-clear-btn {
  transition: all 0.3s;
}
.elementor-metabox-content .repeater-block .elementor-field-file .elementor-upload-clear-btn:hover {
  background-color: #DC2626;
  border-color: #DC2626;
  box-shadow: none;
  color: white;
}
.elementor-metabox-content .row-font-label {
  padding: 0;
  margin: 0;
  display: flex;
  text-transform: capitalize;
  justify-content: space-between;
  align-items: center;
}
.elementor-metabox-content .row-font-label li {
  box-sizing: border-box;
  flex-grow: 1;
  width: 100%;
  margin: 0;
}
.elementor-metabox-content .row-font-label li span.label {
  font-weight: 500;
  padding-inline-end: 10px;
}
.elementor-metabox-content .row-font-label li.row-font-weight, .elementor-metabox-content .row-font-label li.row-font-style {
  max-width: 180px;
}
.elementor-metabox-content .row-font-label li.row-font-actions {
  max-width: 200px;
  text-align: end;
}
.elementor-metabox-content .repeater-content {
  margin: 0;
}
.elementor-metabox-content .repeater-content .repeater-content-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-block-end: 20px;
  line-height: 28px;
}
.elementor-metabox-content .repeater-content .repeater-content-top > div {
  box-sizing: border-box;
  flex-grow: 1;
}
.elementor-metabox-content .repeater-content .repeater-content-top p {
  margin: 0;
  display: inline-block;
}
.elementor-metabox-content .repeater-content .repeater-content-top p label {
  font-weight: 500;
  padding-inline-end: 10px;
}
.elementor-metabox-content .repeater-content .repeater-content-top .elementor-field-select {
  max-width: 180px;
}
.elementor-metabox-content .repeater-content .repeater-content-top .elementor-field-toolbar {
  max-width: 200px;
  text-align: end;
}
.elementor-metabox-content .repeater-content .repeater-content-bottom {
  background-color: #F9FAFA;
  padding: 20px 40px;
  margin: 0 -20px;
}
.elementor-metabox-content input.button.add-repeater-row {
  margin-block-start: 18px;
}
.elementor-metabox-content .elementor-repeater-tool-btn {
  cursor: pointer;
  padding: 0 20px;
  font-size: 12px;
  transition: all 0.3s;
}
.elementor-metabox-content .elementor-repeater-tool-btn i {
  padding-inline-end: 5px;
}
.elementor-metabox-content .elementor-repeater-tool-btn:hover {
  color: #3f444b;
}
.elementor-metabox-content .elementor-repeater-tool-btn.remove-repeater-row:hover {
  color: #DC2626;
}

.row-font-preview,
.inline-preview,
.widefat td.column-font_preview {
  font-size: 30px;
  text-transform: capitalize;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 600px;
  line-height: 1.5;
}

.post-type-elementor_icons .elementor-metabox-content .elementor-button:not([disabled]) {
  margin-block-start: 10px;
}
.post-type-elementor_icons div#postbox-container-1 {
  display: none;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox {
  display: none;
  border: 1px solid #F1F2F3;
  border-radius: 1px;
  background-color: #fff;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .inside {
  margin-block-start: 10px;
  margin-block-end: 20px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-metabox-content {
  background-color: #fff;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-custom-icons-metabox {
  padding-block-start: 4px;
  padding-block-end: 10px;
  padding-inline-start: 10px;
  padding-inline-end: 10px;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-custom-icons-metabox {
    padding: 0;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox h4 {
  color: #1f2124;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: 0.7px;
  line-height: 28px;
  margin: 0 0 4px 0;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox h5 {
  color: #9DA5AE;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  line-height: 21px;
  margin: 0;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor--dropzone--upload__icon i {
  font-size: 64px;
  color: #0A875A;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__uploading,
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__success,
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__error,
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__file {
  display: none;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .is-dragover {
  background-color: grey;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .box__input {
  padding: 180px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox .elementor-field-dropzone {
  outline: 2px dashed #D5D8DC;
  outline-offset: -3px;
  background-color: #fff;
  display: none;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons {
  background-color: #F9FAFA;
  border: 1px solid #F1F2F3;
  border-radius: 1px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-metabox-content {
  background-color: #F9FAFA;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-metabox-content .elementor-custom-icons-metabox {
  padding-block-start: 4px;
  padding-block-end: 0;
  padding-inline-start: 10px;
  padding-inline-end: 10px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header {
  height: 50px;
  color: #3f444b;
  background-color: #fff;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
  padding: 0 35px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header {
    padding: 0 6px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div {
  padding-inline-end: 10px;
  padding-inline-start: 10px;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div {
    line-height: 1;
  }
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div.remove {
    font-size: 10px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header div:nth-of-type(2) {
  border: 1px solid #9DA5AE;
  border-block-start: 0;
  border-block-end: 0;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta {
  color: #1f2124;
  font-size: 14px;
  line-height: 1;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta {
    font-size: 10px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-value {
  font-weight: bold;
}
@media (max-width: 1025px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-value {
    font-size: 10px;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-remove {
  margin-inline-start: auto;
  color: #1f2124;
  opacity: 0.6;
  cursor: pointer;
  transition: all 0.3s;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-remove i {
  color: #3f444b;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-header-meta-remove:hover {
  opacity: 1;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox.elementor--has-icons .elementor-icon-set-footer {
  color: #BABFC5;
  font-family: Roboto, Arial, Helvetica, sans-serif;
  border-block-start: 1px solid #F1F2F3;
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
  text-align: end;
  padding-block-start: 10px;
  padding-block-end: 10px;
  padding-inline-end: 35px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(105px, 1fr));
  grid-gap: 20px;
  padding-block-start: 15px;
  padding-block-end: 0;
  padding-inline-start: 35px;
  padding-inline-end: 35px;
  overflow-y: auto;
  max-height: 575px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li {
  position: relative;
  height: 0;
  padding-block-end: 100%;
  background-color: #fff;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  overflow: hidden;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li div.icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1px;
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li div.icon-name {
  color: #BABFC5;
  font-size: 11px;
  padding: 18px 20px 0;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 479px) {
  .post-type-elementor_icons div#elementor-custom-icons-metabox ul li div.icon-name {
    display: none;
  }
}
.post-type-elementor_icons div#elementor-custom-icons-metabox ul li i {
  font-size: 32px;
}
.post-type-elementor_icons #tagsdiv-elementor_icon_type,
.post-type-elementor_icons #minor-publishing-actions,
.post-type-elementor_icons #misc-publishing-actions {
  display: none;
}

.column-icons_prefix {
  width: 65%;
}
/*# sourceMappingURL=admin.css.map */