/*! elementor-pro - v3.27.0 - 06-02-2025 */
.elementor-widget-loop-carousel{--swiper-pagination-size:0;--swiper-pagination-spacing:10px;--swiper-slides-gap:10px;--swiper-offset-size:0;height:-moz-fit-content;height:fit-content;--swiper-padding-bottom:calc(var(--swiper-pagination-size) + var(--swiper-pagination-spacing));--arrow-prev-top-align:50%;--arrow-prev-top-position:0px;--arrow-prev-caption-spacing:15px;--arrow-next-top-align:50%;--arrow-next-top-position:0px;--arrow-next-caption-spacing:15px;--arrow-prev-left-align:0px;--arrow-prev-left-position:0px;--arrow-next-right-align:0px;--arrow-next-right-position:0px;--arrow-next-translate-x:0px;--arrow-next-translate-y:0px;--arrow-prev-translate-x:0px;--arrow-prev-translate-y:0px;--dots-vertical-position:100%;--dots-vertical-offset:0px;--dots-horizontal-position:50%;--dots-horizontal-offset:0px;--dots-horizontal-transform:-50%;--dots-vertical-transform:-100%;--fraction-vertical-position:100%;--fraction-vertical-offset:0px;--fraction-horizontal-position:50%;--fraction-horizontal-offset:0px;--fraction-horizontal-transform:-50%;--fraction-vertical-transform:-100%;--horizontal-transform-modifier:1;--horizontal-transform-modifier:-1}.elementor-widget-loop-carousel.elementor-pagination-type-bullets{--swiper-pagination-size:6px}.elementor-widget-loop-carousel.elementor-pagination-type-fraction{--swiper-pagination-size:16px}.elementor-widget-loop-carousel.elementor-pagination-type-progressbar{--swiper-pagination-size:4px}.elementor-widget-loop-carousel .elementor-loop-container>.swiper-wrapper>.swiper-slide-active.elementor-edit-area-active{overflow:initial}.elementor-widget-loop-carousel .elementor-loop-container.offset-left{padding-inline-start:var(--swiper-offset-size,0)}.elementor-widget-loop-carousel .elementor-loop-container.offset-right{padding-inline-end:var(--swiper-offset-size,0)}.elementor-widget-loop-carousel .elementor-loop-container.offset-both{padding-inline-end:var(--swiper-offset-size,0);padding-inline-start:var(--swiper-offset-size,0)}.elementor-widget-loop-carousel .swiper-container:not(.swiper-container-initialized)>.swiper-wrapper,.elementor-widget-loop-carousel .swiper:not(.swiper-initialized)>.swiper-wrapper{gap:var(--swiper-slides-gap);overflow:hidden}.elementor-widget-loop-carousel .swiper-container:not(.swiper-container-initialized)>.swiper-wrapper>.swiper--slide,.elementor-widget-loop-carousel .swiper:not(.swiper-initialized)>.swiper-wrapper>.swiper--slide{--number-of-gaps:max(calc(var(--swiper-slides-to-display) - 1),0);--gaps-width-total:calc(var(--number-of-gaps) * var(--swiper-slides-gap));max-width:calc((100% - var(--gaps-width-total)) / var(--swiper-slides-to-display, 1))}.elementor-widget-loop-carousel .e-loop-first-edit{margin-block-start:23px;min-width:33%}.elementor-widget-loop-carousel .swiper-wrapper .swiper-slide a.e-con{display:var(--display)}.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-next,.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-prev{border-style:var(--arrow-normal-border-type);color:var(--arrow-normal-color,hsla(0,0%,93%,.9));font-size:var(--arrow-size,25px);transition-duration:.25s;z-index:2}.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-next svg,.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-prev svg{fill:var(--arrow-normal-color,hsla(0,0%,93%,.9))}.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-next:hover,.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-prev:hover{border-style:var(--arrow-hover-border-type);color:var(--arrow-hover-color,hsla(0,0%,93%,.9))}.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-next:hover svg,.elementor-widget-loop-carousel .elementor-swiper-button.elementor-swiper-button-prev:hover svg{fill:var(--arrow-hover-color,hsla(0,0%,93%,.9))}.elementor-widget-loop-carousel.elementor-element :is(.swiper,.swiper-container)~.elementor-swiper-button-next{right:calc(var(--arrow-next-right-align) + var(--arrow-next-right-position));top:calc(var(--arrow-next-top-align) + var(--arrow-next-top-position) - var(--arrow-next-caption-spacing));transform:translate(var(--arrow-next-translate-x),var(--arrow-next-translate-y))}.elementor-widget-loop-carousel.elementor-element :is(.swiper,.swiper-container)~.elementor-swiper-button-prev{left:calc(var(--arrow-prev-left-align) + var(--arrow-prev-left-position));top:calc(var(--arrow-prev-top-align) + var(--arrow-prev-top-position) - var(--arrow-prev-caption-spacing));transform:translate(var(--arrow-prev-translate-x),var(--arrow-prev-translate-y))}.elementor-widget-loop-carousel .swiper-container-horizontal~.swiper-pagination-progressbar,.elementor-widget-loop-carousel .swiper-horizontal~.swiper-pagination-progressbar{height:var(--swiper-pagination-size)}.elementor-widget-loop-carousel .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background:var(--progressbar-normal-color,#000)}.elementor-widget-loop-carousel .swiper-pagination-progressbar .swiper-pagination-progressbar-fill:hover{background:var(--progressbar-hover-color,#000)}.elementor-widget-loop-carousel .swiper-pagination-fraction{color:var(--fraction-color,#000)}.elementor-widget-loop-carousel .swiper-pagination-bullet{background:var(--dots-normal-color,#000);height:var(--swiper-pagination-size);width:var(--swiper-pagination-size)}.elementor-widget-loop-carousel .swiper-pagination-bullet:hover{background:var(--dots-hover-color,#000);opacity:1}.elementor-widget-loop-carousel.elementor-in-place-template-editable .elementor-loop-container{overflow:visible;overflow-x:clip}.elementor-widget-loop-carousel .swiper-horizontal>.swiper-pagination-bullets,.elementor-widget-loop-carousel .swiper-pagination,.elementor-widget-loop-carousel .swiper-pagination-bullets.swiper-pagination-horizontal,.elementor-widget-loop-carousel .swiper-pagination-custom,.elementor-widget-loop-carousel .swiper-pagination-fraction{font-size:var(--swiper-pagination-size);line-height:var(--swiper-pagination-size)}.elementor-widget-loop-carousel.elementor-pagination-position-outside:not(:has(>.elementor-widget-container))>.swiper,.elementor-widget-loop-carousel.elementor-pagination-position-outside:not(:has(>.elementor-widget-container))>.swiper-container,.elementor-widget-loop-carousel.elementor-pagination-position-outside>.elementor-widget-container>.swiper,.elementor-widget-loop-carousel.elementor-pagination-position-outside>.elementor-widget-container>.swiper-container{padding-bottom:var(--swiper-padding-bottom)}.elementor-widget-loop-carousel.elementor-pagination-position-outside:not(:has(>.elementor-widget-container))>.swiper .elementor-background-slideshow,.elementor-widget-loop-carousel.elementor-pagination-position-outside:not(:has(>.elementor-widget-container))>.swiper-container .elementor-background-slideshow,.elementor-widget-loop-carousel.elementor-pagination-position-outside>.elementor-widget-container>.swiper .elementor-background-slideshow,.elementor-widget-loop-carousel.elementor-pagination-position-outside>.elementor-widget-container>.swiper-container .elementor-background-slideshow{padding-bottom:0}.elementor-widget-loop-carousel.elementor-pagination-position-outside:not(:has(>.elementor-widget-container)) .swiper-pagination-bullet,.elementor-widget-loop-carousel.elementor-pagination-position-outside>.elementor-widget-container .swiper-pagination-bullet{vertical-align:top}.elementor-widget-loop-carousel .swiper-pagination-bullets{height:-moz-max-content;height:max-content;right:calc(var(--dots-horizontal-position) + var(--dots-horizontal-offset));top:calc(var(--dots-vertical-position) + var(--dots-vertical-offset));transform:translate(calc(var(--dots-horizontal-transform) * var(--horizontal-transform-modifier)),var(--dots-vertical-transform));width:-moz-max-content;width:max-content;z-index:3}.elementor-widget-loop-carousel .swiper-pagination-fraction{height:-moz-max-content;height:max-content;right:calc(var(--fraction-horizontal-position) + var(--fraction-horizontal-offset));top:calc(var(--fraction-vertical-position) + var(--fraction-vertical-offset));transform:translate(calc(var(--fraction-horizontal-transform) * var(--horizontal-transform-modifier)),var(--fraction-vertical-transform));width:-moz-max-content;width:max-content;z-index:3}