/*! elementor-pro - v3.27.0 - 06-02-2025 */
.elementor-panel .elementor-control .e-control-error {
  color: #F59E0B;
}
.elementor-panel .elementor-control.forms-field-shortcode .elementor-control-content {
  flex-flow: row;
  align-items: center;
}
.elementor-panel .elementor-control.forms-field-shortcode .elementor-control-title {
  width: 45%;
}
.elementor-panel .elementor-control.forms-field-shortcode .elementor-control-raw-html {
  width: 55%;
}
.elementor-panel .elementor-control .elementor-button.elementor-button-default.elementor-button-center {
  display: block;
  margin: 0 auto;
}

#elementor-element--promotion__dialog .dialog-header .eicon-pro-icon {
  visibility: hidden;
}

.elementor-context-menu-list__item {
  position: relative;
}
.elementor-context-menu-list__item__shortcut .eicon-advanced {
  font-size: 16px;
  color: var(--e-a-color-txt-muted);
}
.elementor-context-menu-list__item__shortcut--link-fullwidth {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  cursor: pointer;
}

#elementor-widget-template-empty-templates {
  margin-block-start: 15px;
  text-align: center;
}

.elementor-widget-template-empty-templates-title {
  padding: 25px 0 30px;
}

.elementor-widget-template-empty-templates-icon {
  font-size: 96px;
}

.elementor-widget-template-empty-templates-footer {
  color: var(--e-a-color-txt-muted);
  font-size: 13px;
  font-style: italic;
  margin-block-end: 15px;
}

#elementor-panel-global-widget {
  height: 100%;
}
#elementor-panel-global-widget > * {
  background-color: var(--e-a-bg-default);
}

#elementor-global-widget-locked-header {
  border-block-end: var(--e-a-border);
}
#elementor-global-widget-locked-header.elementor-nerd-box {
  padding: 40px 25px;
}
#elementor-global-widget-locked-header.elementor-nerd-box .elementor-nerd-box-icon {
  margin-block-start: 20px;
}

#elementor-global-widget-locked-tools {
  margin-block-start: 15px;
  padding: 0 20px;
}

.elementor-global-widget-locked-tool {
  display: flex;
  padding: 20px 0;
  align-items: center;
}
.elementor-global-widget-locked-tool .elementor-button {
  min-width: 70px;
}

.elementor-global-widget-locked-tool-description {
  flex-grow: 1;
}

#elementor-global-widget-locked-unlink {
  border-block-start: var(--e-a-border);
}

#elementor-global-templates .elementor-element {
  position: relative;
}
#elementor-global-templates .elementor-element:before {
  position: absolute;
  font-family: eicons;
  content: "\e91f";
  top: 5px;
  left: 5px;
  font-size: 10px;
}
#elementor-global-templates .elementor-element:hover:before {
  color: var(--e-a-color-global);
}

#elementor-global-widget-loading {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
}
#elementor-global-widget-loading:not(.elementor-hidden) {
  display: flex;
}
#elementor-global-widget-loading i {
  font-size: 50px;
}

.elementor-panel .elementor-control-type-fields_map .elementor-repeater-fields {
  margin: 10px 0;
}
.elementor-panel .elementor-control-type-fields_map .elementor-repeater-fields .elementor-control {
  padding: 0;
}
.elementor-panel .elementor-control-type-fields_map .elementor-repeater-fields:last-child {
  margin-block-end: 0;
}

.elementor-repeater-row--form-step .elementor-repeater-row-tools:hover {
  background-color: #BABFC5;
}
.elementor-repeater-row--form-step .elementor-repeater-row-tools div:not(.elementor-repeater-row-handle-sortable) {
  background-color: #F1F2F3;
}
.elementor-repeater-row--form-step .elementor-repeater-row-tools div:not(.elementor-repeater-row-handle-sortable):hover {
  background-color: #ffffff;
}

.elementor-facebook-widget.fb_iframe_widget {
  width: 100% !important;
}
.elementor-facebook-widget.fb_iframe_widget span {
  width: 100% !important;
}
.elementor-facebook-widget.fb_iframe_widget iframe {
  position: relative;
  width: 100% !important;
}
.elementor-facebook-widget.fb-like {
  height: 1px;
}

.elementor-widget-facebook-comments iframe {
  width: 100% !important;
}

#elementor-publish {
  height: 100%;
  display: flex;
}
#elementor-publish__modal .dialog-message {
  padding: 0;
}
#elementor-publish__modal .dialog-buttons-wrapper {
  display: flex;
}
#elementor-publish__tabs {
  padding-block-start: 50px;
}
@media (max-width: 1439px) {
  #elementor-publish__tabs {
    width: 28%;
  }
}
#elementor-publish__screen {
  overflow: auto;
  padding: 50px;
}

.elementor-publish__tab {
  display: flex;
  align-items: center;
  position: relative;
  height: 110px;
  padding: 20px 15px;
  cursor: pointer;
}
.elementor-publish__tab:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-publish__tab.elementor-active {
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-accent);
}
.elementor-publish__tab.elementor-active:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 3px;
  background-color: var(--e-a-border-color-accent);
}
.elementor-publish__tab__image {
  width: 50px;
  flex-shrink: 0;
}
.elementor-publish__tab__image img {
  width: 100%;
}
.elementor-publish__tab__content {
  text-align: start;
  padding-inline-start: 15px;
}
.elementor-publish__tab__title {
  font-size: 18px;
  font-weight: bold;
}

#elementor-theme-builder-conditions {
  margin: 40px 0 60px;
}
#elementor-theme-builder-conditions-view {
  overflow: hidden;
}
#elementor-theme-builder-conditions .elementor-control {
  background-color: transparent;
  padding: 0;
}
#elementor-theme-builder-conditions .elementor-control:before {
  content: none;
}
#elementor-theme-builder-conditions .elementor-control select {
  border-width: 0;
  height: 40px;
  padding: 0 14px;
}
#elementor-theme-builder-conditions .elementor-control-type-query {
  width: 100px;
}
#elementor-theme-builder-conditions .elementor-control-type-select .elementor-control-input-wrapper:after {
  right: 10px;
}
#elementor-theme-builder-conditions .elementor-control-type {
  width: 120px;
}
#elementor-theme-builder-conditions .elementor-control-type[data-elementor-condition-type=include] .elementor-control-input-wrapper:before {
  content: "\e8cc";
}
#elementor-theme-builder-conditions .elementor-control-type[data-elementor-condition-type=exclude] .elementor-control-input-wrapper:before {
  content: "\e8cd";
}
#elementor-theme-builder-conditions .elementor-control-type[data-elementor-condition-type=exclude] select, #elementor-theme-builder-conditions .elementor-control-type[data-elementor-condition-type=include] select {
  padding-inline-start: 33px;
}
#elementor-theme-builder-conditions .elementor-control-type .elementor-control-input-wrapper:before {
  font-family: eicons;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 13px;
  font-size: 15px;
}
#elementor-theme-builder-conditions .elementor-theme-builder-conditions-repeater-row-controls {
  display: flex;
  flex-grow: 1;
  margin-inline-end: 10px;
  width: 70%;
  border: var(--e-a-border-bold);
  overflow: hidden;
  border-radius: var(--e-a-border-radius);
}
#elementor-theme-builder-conditions .elementor-theme-builder-conditions-repeater-row-controls .elementor-control:not(:first-child) {
  flex-grow: 1;
  border-inline-start: var(--e-a-border-bold);
  margin-inline-start: 1px;
}
#elementor-theme-builder-conditions .elementor-theme-builder-conditions-repeater-row-controls .elementor-control:not(:first-child) select {
  border-radius: 0;
}
#elementor-theme-builder-conditions .elementor-repeater-fields-wrapper {
  max-width: 700px;
  width: 100%;
  margin: auto;
}
#elementor-theme-builder-conditions .elementor-repeater-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-block-start: 10px;
}
#elementor-theme-builder-conditions .elementor-control-field {
  display: block;
}
#elementor-theme-builder-conditions .elementor-control-title,
#elementor-theme-builder-conditions .elementor-control-spinner {
  display: none;
}
#elementor-theme-builder-conditions .elementor-control-input-wrapper {
  width: 100%;
  max-width: initial;
}
#elementor-theme-builder-conditions .select2-selection {
  height: 40px;
  border: none;
  border-radius: 0;
}
#elementor-theme-builder-conditions .select2-selection__rendered {
  line-height: 40px;
  padding-inline-start: 15px;
  text-align: start;
}
#elementor-theme-builder-conditions .select2-selection__arrow {
  height: 30px;
}
#elementor-theme-builder-conditions .select2-selection__arrow b {
  border-width: 4px 4px 0 4px;
  margin-block-start: 2px;
  margin-inline-start: -7px;
}
#elementor-theme-builder-conditions .elementor-repeater-tool-remove {
  font-size: 18px;
  cursor: pointer;
  color: var(--e-a-color-txt);
}
#elementor-theme-builder-conditions .elementor-button-wrapper {
  margin-block-start: 50px;
}
#elementor-theme-builder-conditions .elementor-repeater-add {
  padding: 12px 26px;
}

.elementor-error .elementor-theme-builder-conditions-repeater-row-controls {
  border: 1px solid var(--e-a-color-warning);
}

.elementor-conditions-conflict-message {
  margin-block-start: 10px;
  font-size: 11px;
  color: var(--e-a-color-warning);
  text-align: start;
  padding-inline-start: 90px;
}
.elementor-conditions-conflict-message a {
  color: var(--e-a-color-warning);
}

.elementor-panel-footer-theme-builder-buttons-wrapper .elementor-panel-footer-sub-menu {
  display: flex;
}
.elementor-panel-footer-theme-builder-buttons-wrapper .elementor-panel-footer-sub-menu-item {
  width: 100%;
}
.elementor-panel-footer-theme-builder-buttons-wrapper .elementor-panel-footer-sub-menu-item i {
  margin-inline-end: 5px;
}
.elementor-panel-footer-theme-builder-buttons-wrapper .elementor-panel-footer-sub-menu-item > * {
  display: inline-block;
  line-height: 40px;
}

.elementor-conditions-select2-dropdown {
  border: none;
  border-radius: 0;
}
.elementor-conditions-select2-dropdown .select2-results__message {
  display: none;
}
.elementor-conditions-select2-dropdown .select2-search--dropdown .select2-search__field {
  border-width: 0 0 1px;
  border-radius: 0;
}

#elementor-toast.e-theme-builder-save-toaster .dialog-buttons-wrapper {
  flex-direction: column;
}
#elementor-toast.e-theme-builder-save-toaster .dialog-buttons-message {
  text-align: center;
}
#elementor-toast.e-theme-builder-save-toaster .dialog-open_site_editor {
  margin-block-end: 10px;
}

.elementor-control.elementor-control-sitemap_items.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title span {
  text-transform: capitalize;
}

.elementor-editor-popup .elementor-tab-control-settings a:before {
  content: "\e922";
}

#elementor-publish .elementor-popup__display-settings .elementor-control-type-slider .elementor-control-input-wrapper {
  width: 150px;
}
#elementor-publish .elementor-popup__display-settings .elementor-control-input-wrapper {
  width: 80px;
  max-width: 100%;
}

.elementor-popup__display-settings {
  text-align: start;
}
.elementor-popup__display-settings_controls_group {
  display: flex;
  align-items: center;
  height: 60px;
  border: var(--e-a-border);
  margin-block-end: 10px;
  padding-inline-end: 20px;
  border-radius: var(--e-a-border-radius);
}
.elementor-popup__display-settings_controls_group:hover {
  background-color: var(--e-a-bg-hover);
}
.elementor-popup__display-settings_controls_group.elementor-active {
  background-color: var(--e-a-bg-active);
  color: var(--e-a-color-txt-accent);
  border-color: var(--e-a-border-color-accent);
}
.elementor-popup__display-settings_controls_group:not(.elementor-active) .elementor-control:nth-child(2) h3 {
  color: #9DA5AE;
}
.elementor-popup__display-settings_controls_group__icon {
  width: 60px;
  height: 60px;
  border-inline-end: var(--e-a-border);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.elementor-popup__display-settings_controls_group__icon img {
  width: 35px;
}
.elementor-popup__display-settings_controls_group .elementor-control {
  padding: 0;
  margin: 0;
}
.elementor-popup__display-settings_controls_group .elementor-control:nth-child(2) {
  width: 230px;
}
.elementor-popup__display-settings_controls_group .elementor-control:nth-child(2) h3 {
  font-size: 16px;
  font-weight: 400;
}
.elementor-popup__display-settings_controls_group .elementor-control:not(:nth-child(2)) .elementor-control-title:not(:empty) {
  width: 75px;
}
.elementor-popup__display-settings_controls_group .elementor-control:not(:last-child) {
  margin-inline-start: 25px;
}
.elementor-popup__display-settings_controls_group .elementor-control-type-slider .elementor-control-input-wrapper {
  display: flex;
}
.elementor-popup__display-settings_controls_group .elementor-control-type-slider .elementor-slider-input {
  width: 45%;
}
.elementor-popup__display-settings_controls_group .elementor-control-type-switcher .elementor-control-field {
  justify-content: flex-end;
}
.elementor-popup__display-settings .elementor-control-type-section {
  display: none;
}
.elementor-popup__display-settings__group-toggle {
  flex-grow: 1;
}

#elementor-popup-timing__controls .select2-selection {
  min-height: 27px;
}
#elementor-popup-timing__controls .select2-selection__rendered {
  line-height: 1;
}
#elementor-popup-timing__controls .select2-selection__choice {
  font-size: 10px;
}

#elementor-popup__timing-controls-group--url .elementor-control-url_url .elementor-control-input-wrapper {
  width: 200px;
}
#elementor-popup__timing-controls-group--sources .elementor-control-sources_sources .elementor-control-input-wrapper {
  width: 300px;
}
#elementor-popup__timing-controls-group--devices .elementor-control-devices_devices .elementor-control-input-wrapper {
  width: 330px;
}
#elementor-popup__timing-controls-group--logged_in .elementor-control-logged_in_roles .elementor-control-input-wrapper {
  width: 195px;
}
#elementor-popup__timing-controls-group--browsers .elementor-control-browsers_browsers .elementor-control-input-wrapper {
  width: 100px;
}
#elementor-popup__timing-controls-group--browsers .elementor-control-browsers_browsers_options .elementor-control-input-wrapper {
  width: 200px;
}

#elementor-popup__timing-controls-group--times .elementor-control-field {
  display: unset;
}

#elementor-popup__timing-controls-group--schedule .elementor-control-title {
  width: unset;
}
#elementor-popup__timing-controls-group--schedule .elementor-control-input-wrapper {
  margin-block-start: 5px;
}
#elementor-popup__timing-controls-group--schedule div[class*=elementor-control-schedule_] .elementor-control-content .elementor-control-field {
  display: block;
}
#elementor-popup__timing-controls-group--schedule div[class*=elementor-control-schedule_] .elementor-control-content .elementor-control-input-wrapper {
  width: 108px;
}
#elementor-popup__timing-controls-group--schedule .elementor-control-schedule_timezone {
  margin-inline-start: -17px;
}

.elementor-control-type-raw_html .elementor-descriptor-subtle a {
  color: inherit;
  border-block-end-color: inherit;
  font-weight: 500;
}
.elementor-control-type-raw_html .elementor-descriptor-subtle a:hover {
  color: #0C0D0E;
}

.e-page-transition-preview::before {
  content: "";
  height: 1em;
  width: 0.8em;
  margin-inline-end: 4px;
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='13' viewBox='0 0 10 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.40554 6.20969C8.6115 6.34841 8.6115 6.65153 8.40554 6.79026L1.19553 11.6467C0.963052 11.8033 0.65 11.6368 0.65 11.3565L0.650001 1.64349C0.650001 1.36319 0.963054 1.19661 1.19553 1.3532L8.40554 6.20969Z' stroke='white' stroke-width='1.3'/%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
  background-size: contain;
  display: inline-block;
  vertical-align: top;
}

.elementor-template-query-control-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-block-start: 15px;
  gap: 15px;
}

.e-control-display-conditions__wrapper,
.e-control-display-conditions-promo__wrapper {
  display: flex;
  justify-content: space-between;
}
.e-control-display-conditions__desc,
.e-control-display-conditions-promo__desc {
  align-self: center;
}
.e-control-display-conditions__desc .eicon-lock,
.e-control-display-conditions-promo__desc .eicon-lock {
  margin-inline-start: 15px;
}
.e-control-display-conditions__desc .eicon-lock:hover,
.e-control-display-conditions-promo__desc .eicon-lock:hover {
  color: var(--e-a-color-accent);
}
.e-control-display-conditions.eicon-flow,
.e-control-display-conditions-promo.eicon-flow {
  align-self: flex-end;
  cursor: pointer;
  border: var(--e-a-border-bold);
  border-radius: var(--e-a-border-radius);
  padding: 5px;
}
.e-control-display-conditions.eicon-flow.filled,
.e-control-display-conditions-promo.eicon-flow.filled {
  background-color: var(--e-a-bg-active);
  color: #E73CF6;
}
/*# sourceMappingURL=editor.css.map */