/*! elementor-pro - v3.27.0 - 06-02-2025 */
:root {
  --color-box-shadow-color: rgba(0, 0, 0, 0.05);
}

.eps-theme-dark {
  --color-box-shadow-color: rgba(0, 0, 0, 0.1);
}

:root {
  --eps-meta-icon-color: #818A96;
}

.eps-theme-dark {
  --eps-meta-icon-color: #BABFC5;
}

.e-site-template__meta-data {
  margin-inline-start: 0.625rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.6875rem;
}
.e-site-template__meta-data:last-of-type {
  margin-inline-end: auto;
}
.e-site-template__meta-data:first-of-type {
  margin-inline-start: 1rem;
}
.e-site-template__meta-data .eps-icon {
  margin-inline-end: 0.3125rem;
  color: var(--eps-meta-icon-color);
  font-size: 0.8125rem;
}
.e-site-template__placeholder .eps-card__image {
  filter: var(--placeholder-filter, none);
}
.e-site-template__overlay-preview {
  padding-block-start: calc(var(--card-image-aspect-ratio, 95.6%) - 1.875rem);
  position: relative;
}
.e-site-template__overlay-preview-button {
  font-weight: bold;
  display: flex;
  flex-wrap: wrap;
  height: 1.875rem;
  width: 100%;
  background-color: var(--card-background-color-hover);
  justify-content: center;
  align-items: center;
  padding-block-start: 0.625rem;
  line-height: 1.25rem;
  --button-background-color: var(--card-headline-color);
}
.e-site-template__overlay-preview-button::before {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  top: 0;
  left: 0;
  padding-block-start: calc(var(--card-image-aspect-ratio, 95.6%) - 1.875rem);
}
.e-site-template__overlay-preview-button > :not(:first-child) {
  margin-inline-start: 0.3125rem;
}
.e-site-template__edit-btn {
  margin-inline-end: 1.25rem;
}
.e-site-template__edit-btn .eps-icon {
  margin-inline-end: 0.3125rem;
}
.e-site-template__instances .eps-icon {
  margin-inline-end: 0.3125rem;
  color: var(--eps-meta-icon-color);
  font-size: 0.8125rem;
}
.e-site-template__instances-list {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.e-site-template__edit-conditions {
  margin-inline-start: 1rem;
  text-decoration: underline;
  font-style: italic;
}
.e-site-template--extended .eps-card__figure {
  overflow: auto;
}
.e-site-template--extended .eps-card__headline {
  flex-grow: 0;
}
.e-site-template--wide {
  --card-image-aspect-ratio: 12.35%;
}

.eps-add-new__overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  --card-image-overlay-background-color: transparent;
}

.e-site-editor__templates .page-header {
  margin-block-end: 0.625rem;
}
.e-site-editor__templates .page-header > a {
  align-self: baseline;
}
.e-site-editor__templates .eps-separator {
  margin-block-end: 2.75rem;
}

:root {
  --e-site-editor-conditions-row-controls-background: #ffffff;
  --e-site-editor-input-wrapper-border-color: #D5D8DC;
  --e-site-editor-input-wrapper-select-color: #3f444b;
  --e-site-editor-conditions-row-controls-border: 1px solid #D5D8DC;
  --e-site-editor-add-button-background-color: #69727D;
  --e-site-editor-add-button-color-hover-background-color: #515962;
  --e-site-editor-input-wrapper-condition-include-background-color:
  #69727D;
  --e-site-editor-input-wrapper-condition-exclude-background-color:
  #818A96;
  --e-site-editor-input-select2-search-field-color: #515962 ;
}

.eps-theme-dark {
  --select2-selection-background-color: tints(600);
  --e-site-editor-conditions-row-controls-background: #515962;
  --e-site-editor-input-wrapper-border-color: #3f444b;
  --e-site-editor-input-wrapper-select-color: #BABFC5;
  --e-site-editor-conditions-row-controls-border: 1px solid #3f444b;
  --e-site-editor-add-button-background-color: #69727D;
  --e-site-editor-add-button-color-hover-background-color: #515962;
  --e-site-editor-input-wrapper-condition-include-background-color:
  		#515962;
  --e-site-editor-input-wrapper-condition-exclude-background-color:
  		#515962;
  --e-site-editor-input-select2-search-field-color: #ffffff ;
}

.e-site-editor-conditions__header {
  text-align: center;
}
.e-site-editor-conditions__header-image {
  display: block;
  margin: 0 auto 2.75rem;
  width: 4.375rem;
}
.e-site-editor-conditions__rows {
  margin: 2.75rem auto;
  max-width: 43.75rem;
}
.e-site-editor-conditions__row {
  display: flex;
  flex-grow: 1;
  margin-block-start: 0.75rem;
}
.e-site-editor-conditions__remove-condition {
  color: #818A96;
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.e-site-editor-conditions__row-controls {
  overflow: hidden;
  margin-inline-end: 0.625rem;
  background-color: var(--e-site-editor-conditions-row-controls-background);
  display: flex;
  width: 100%;
  border: var(--e-site-editor-conditions-row-controls-border);
  border-radius: 0.1875rem;
}
.e-site-editor-conditions__row-controls--error {
  border: 1px solid #DC2626;
}
.e-site-editor-conditions__conflict {
  text-align: center;
  margin-block-start: 0.3125rem;
  color: #DC2626;
}
.e-site-editor-conditions__row-controls-inner {
  width: 100%;
  display: flex;
}
.e-site-editor-conditions__row-controls-inner div {
  flex: 1;
}
.e-site-editor-conditions__add-button-container {
  text-align: center;
}
.e-site-editor-conditions__add-button {
  margin-block-start: 2.75rem;
  background-color: var(--e-site-editor-add-button-background-color);
  color: #ffffff;
  text-transform: uppercase;
}
.e-site-editor-conditions__add-button:hover {
  background-color: var(--e-site-editor-add-button-color-hover-background-color);
  color: #ffffff;
}
.e-site-editor-conditions__footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem;
  margin-top: 1rem;
}
.e-site-editor-conditions__input-wrapper {
  position: relative;
  padding-inline-start: 1px solid;
  border-color: var(--e-site-editor-input-wrapper-border-color);
}
.e-site-editor-conditions__input-wrapper:first-child {
  border: none;
}
.e-site-editor-conditions__input-wrapper select {
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  font-size: 0.75rem;
  height: 2.5rem;
  border-width: 0;
  padding: 0 0.625rem;
  width: 100%;
  position: relative;
  color: var(--e-site-editor-input-wrapper-select-color);
  outline: none;
  background: transparent;
}
.e-site-editor-conditions__input-wrapper:after {
  font-family: eicons;
  content: "\e8ad";
  font-size: 0.75rem;
  pointer-events: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0.625rem;
}
.e-site-editor-conditions__input-wrapper .select2-container--default .select2-selection--single {
  border: none;
  line-height: 2.5rem;
}
.e-site-editor-conditions__input-wrapper .select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 2.5rem;
  font-size: 0.75rem;
}
.e-site-editor-conditions__input-wrapper .select2-selection {
  outline: none;
  background: transparent;
  height: 2.5rem;
}
.e-site-editor-conditions__input-wrapper .select2-selection__arrow {
  display: none;
}
.e-site-editor-conditions__input-wrapper--condition-type {
  position: relative;
}
.e-site-editor-conditions__input-wrapper--condition-type:before {
  font-family: eicons;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0.75rem;
  font-size: 0.9375rem;
  pointer-events: none;
  z-index: 1000;
}
.e-site-editor-conditions__input-wrapper--condition-type select {
  text-transform: uppercase;
  padding-inline-start: 2.125rem;
  width: 7.5rem;
  font-size: 0.75rem;
  border-inline-end: 1px solid;
  border-color: var(--e-site-editor-input-wrapper-border-color);
}
.e-site-editor-conditions__input-wrapper--condition-type[data-elementor-condition-type=include]:before {
  content: "\e8cc";
}
.e-site-editor-conditions__input-wrapper--condition-type[data-elementor-condition-type=exclude]:before {
  content: "\e8cd";
}

.select2-search__field {
  background-color: transparent;
  color: var(--e-site-editor-input-select2-search-field-color);
}

.eps-back-button {
  font-size: 14px;
  margin-block-end: 1.5rem;
}
.eps-back-button .eps-icon {
  transform: rotate(180deg);
}

:root {
  --indicator-bullet-border-color: #ffffff;
}

.eps-theme-dark {
  --indicator-bullet-border-color: #69727D;
}

.eps-indicator-bullet {
  display: block;
  flex-shrink: 0;
  width: 0.75rem;
  height: 0.75rem;
  box-shadow: 0 2px 3px 1px var(--color-box-shadow-color);
  background-color: #9DA5AE;
  border: 2px solid var(--indicator-bullet-border-color);
  border-radius: 100%;
  margin-inline-end: 0.625rem;
}
.eps-indicator-bullet--active {
  background-color: #0A875A;
}

.site-editor__preview-iframe {
  height: 50vh;
  position: relative;
}
.site-editor__preview-iframe__iframe {
  top: 0;
  left: 0;
  position: absolute;
  border: none;
  transform-origin: 0 0;
  height: 100%;
}
.site-editor__preview-iframe--header, .site-editor__preview-iframe--footer {
  height: 15vh;
}

.e-site-editor__content_container {
  flex-direction: column;
  min-height: 100%;
  flex-wrap: nowrap;
}

.e-site-editor__content_container_main {
  flex: 1;
  padding: 1.875rem;
}

.e-site-editor__content_container_secondary {
  margin: 0 auto;
  align-items: center;
  border-block-start: 1px solid var(--hr-color);
  padding-block-start: 1rem;
  padding-block-end: 1rem;
  padding-inline: 1.875rem;
}

.eps-app__content:has(.e-site-editor__content_container_main) {
  padding: 0;
}
/*# sourceMappingURL=app-rtl.css.map */