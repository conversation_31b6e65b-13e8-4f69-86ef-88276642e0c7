/*! elementor-pro - v3.27.0 - 06-02-2025 */
*[data-editable-elementor-document] {
  position: relative;
}
*[data-editable-elementor-document]:before, *[data-editable-elementor-document]:after {
  content: "";
  display: table;
}
*[data-editable-elementor-document] .elementor-document-handle {
  position: absolute;
  z-index: 2147483620;
  cursor: pointer;
  inset: 0;
  display: none;
  border: 2px solid var(--e-a-color-primary, #F3BAFD);
}
*[data-editable-elementor-document] .elementor-document-handle:before {
  content: "";
  position: absolute;
  background: var(--e-a-color-primary, #F3BAFD);
  opacity: 0.3;
  inset: 0;
}
*[data-editable-elementor-document] .elementor-document-handle__inner {
  display: none;
  align-items: center;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: var(--e-a-color-primary, #F3BAFD);
  padding: 8px 16px;
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 12px;
  line-height: 14px;
  color: var(--e-p-border-global-invert, #0C0D0E);
  gap: 8px;
  border-radius: 0 0 3px 3px;
}
*[data-editable-elementor-document] .elementor-document-handle__inner:before, *[data-editable-elementor-document] .elementor-document-handle__inner:after {
  content: "";
  position: absolute;
  border: solid transparent;
}
*[data-editable-elementor-document] .elementor-document-handle__inner:before {
  right: calc(100% - 1px);
  border-inline-end-color: var(--e-a-color-primary, #F3BAFD);
  border-width: 0 14px 30px 0;
}
*[data-editable-elementor-document] .elementor-document-handle__inner:after {
  left: calc(100% - 1px);
  border-inline-start-color: var(--e-a-color-primary, #F3BAFD);
  border-width: 0 0 30px 14px;
}
*[data-editable-elementor-document].e-embedded-document-active .elementor-document-handle:not(.elementor-document-save-back-handle), *[data-editable-elementor-document].elementor-widget-container .elementor-document-handle:not(.elementor-document-save-back-handle) {
  bottom: unset;
  border-block-end: unset;
}
*[data-editable-elementor-document].e-embedded-document-active .elementor-document-handle:not(.elementor-document-save-back-handle)::before, *[data-editable-elementor-document].elementor-widget-container .elementor-document-handle:not(.elementor-document-save-back-handle)::before {
  display: none;
  bottom: unset;
}

.elementor-editor-active *[data-editable-elementor-document]:not(.elementor-edit-mode):hover .elementor-document-handle:not(.elementor-document-save-back-handle) {
  display: block;
}
.elementor-editor-active *[data-editable-elementor-document]:not(.elementor-edit-mode):hover .elementor-document-handle:not(.elementor-document-save-back-handle)::before {
  display: block;
}
.elementor-editor-active *[data-editable-elementor-document]:not(.elementor-edit-mode):hover .elementor-document-handle:not(.elementor-document-save-back-handle) .elementor-document-handle__inner {
  display: flex;
}
.elementor-editor-active *[data-editable-elementor-document].loading {
  opacity: 0.5;
}
.elementor-editor-active *[data-editable-elementor-document][data-elementor-type=single] > .elementor-document-handle {
  transform: translateX(-50%) translateY(-100%);
  border-radius: 3px 3px 0 0;
}
.elementor-editor-active *[data-editable-elementor-document][data-elementor-type=single] > .elementor-document-handle:before {
  border-width: 30px 14px 0 0;
}
.elementor-editor-active *[data-editable-elementor-document][data-elementor-type=single] > .elementor-document-handle:after {
  border-width: 30px 0 0 14px;
}

[data-elementor-post-type=elementor_library] > .elementor-document-handle {
  border: 2px solid var(--e-p-border-global, #5EEAD4);
}
[data-elementor-post-type=elementor_library] > .elementor-document-handle:before {
  background-color: var(--e-p-border-global, #5EEAD4);
}
[data-elementor-post-type=elementor_library] > .elementor-document-handle .elementor-document-handle__inner {
  background-color: var(--e-p-border-global, #5EEAD4);
}
[data-elementor-post-type=elementor_library] > .elementor-document-handle .elementor-document-handle__inner:before {
  border-inline-end-color: var(--e-p-border-global, #5EEAD4);
}
[data-elementor-post-type=elementor_library] > .elementor-document-handle .elementor-document-handle__inner:after {
  border-inline-start-color: var(--e-p-border-global, #5EEAD4);
}

.elementor-widget.elementor-sticky--effects .elementor-editor-widget-settings {
  right: -14px;
}

.elementor-editor-active .elementor.elementor-edit-mode .elementor-widget.elementor-global-widget:hover {
  outline: 1px solid var(--e-p-border-global);
}
.elementor-editor-active .elementor.elementor-edit-mode .elementor-widget.elementor-global-widget.elementor-element-editable, .elementor-editor-active .elementor.elementor-edit-mode .elementor-widget.elementor-global-widget.elementor-element-editable:hover {
  box-shadow: 0 0 0 2px var(--e-p-border-global);
}
.elementor-editor-active .elementor.elementor-edit-mode .elementor-global-widget .elementor-editor-widget-settings {
  background-color: var(--e-p-border-global);
}
.elementor-editor-active .elementor.elementor-edit-mode .elementor-global-widget .elementor-editor-widget-settings .elementor-editor-element-setting {
  background-color: var(--e-p-border-global);
  color: var(--e-p-border-global-invert);
}
.elementor-editor-active .elementor.elementor-edit-mode .elementor-global-widget .elementor-editor-widget-settings .elementor-editor-element-setting:hover {
  background-color: var(--e-p-border-global-hover);
}
.elementor-editor-active .elementor.elementor-edit-mode .elementor-global-widget .elementor-editor-widget-settings .elementor-editor-element-setting.elementor-editor-element-save {
  display: none;
}

.elementor-embedded-editor.elementor-location-header .elementor-section-wrap:not(:empty) + #elementor-add-new-section {
  display: none;
}

.elementor-editor-preview .elementor-edit-area-active[data-elementor-type=loop-item] .elementor-element-overlay,
.elementor-editor-preview .elementor-edit-area-active[data-elementor-type=loop-item] .elementor-empty,
.elementor-editor-preview .elementor-edit-area-active[data-elementor-type=loop-item] .elementor-add-section,
.elementor-editor-preview .elementor-edit-area-active[data-elementor-type=loop-item] .elementor-add-section-inline,
.elementor-editor-preview .elementor-edit-area-active[data-elementor-type=loop-item] .elementor-empty-view,
.elementor-editor-preview .elementor-edit-area-active[data-elementor-type=loop-item] .elementor-widget-empty {
  display: initial;
}
.elementor.elementor-edit-area-active .elementor-document-handle.elementor-document-save-back-handle {
  display: block;
  bottom: unset;
  border: unset;
}
.elementor.elementor-edit-area-active .elementor-document-handle.elementor-document-save-back-handle:before {
  display: none;
}
.elementor.elementor-edit-area-active .elementor-document-handle.elementor-document-save-back-handle > .elementor-document-handle__inner {
  display: flex;
  opacity: 1;
}
.elementor-document-handle.elementor-document-save-back-handle .elementor-document-handle__inner {
  transform: translateX(-50%) translateY(-100%);
  border-radius: 3px 3px 0 0;
}
.elementor-document-handle.elementor-document-save-back-handle .elementor-document-handle__inner:before {
  border-width: 30px 14px 0 0;
}
.elementor-document-handle.elementor-document-save-back-handle .elementor-document-handle__inner:after {
  border-width: 30px 0 0 14px;
}
.elementor-document-handle.elementor-document-save-back-handle .eicon-arrow-left,
.elementor-document-handle.elementor-document-save-back-handle .eicon-arrow-right {
  margin-inline-end: 5px;
}
.elementor-loop-container > div.elementor-edit-area-active:first-of-type {
  border: 2px solid #5EEAD4;
}

div[class*=elementor-widget-loop] .elementor-edit-area-active[data-editable-elementor-document], div[class*=elementor-widget-loop] .elementor-edit-area-active.swiper-slide-active, div[class*=elementor-widget-loop] .elementor-edit-area-active.e-loop-first-edit:first-of-type {
  border: 2px solid #5EEAD4;
}
div[class*=elementor-widget-loop] .elementor-edit-area-active .elementor-document-save-back-handle {
  display: flex;
  line-height: initial;
  white-space: nowrap;
}
div[class*=elementor-widget-loop] #elementor-add-new-section {
  margin: 30px auto;
}
div[class*=elementor-widget-loop] .elementor-add-section-inner {
  padding: 15px 0;
}

.elementor-edit-area-active[data-elementor-type=loop-item] .elementor-section-wrap {
  /* Overwrite the 25px min-height from the default .elementor-section-wrap:empty, as this causes unnecessary
  white space between the "Drag widget here" box and the document handles for in-place editing, making
  it look like this area is not vertically aligned on a new empty state. */
}
.elementor-edit-area-active[data-elementor-type=loop-item] .elementor-section-wrap:not(:empty) + #elementor-add-new-section {
  display: none;
}
.elementor-edit-area-active[data-elementor-type=loop-item] .elementor-section-wrap:empty {
  min-height: 0;
}

.elementor-editor-active [class*=elementor-widget-loop] .elementor:not([data-editable-elementor-document]):not(.e-loop-alternate-template):hover {
  box-shadow: initial;
}

.elementor-editor-active [class*=elementor-widget-loop]:hover .elementor-document-handle:not(.elementor-document-save-back-handle) {
  display: flex;
  line-height: initial;
  white-space: nowrap;
}
.elementor-editor-active [class*=elementor-widget-loop]:hover .elementor-document-handle:not(.elementor-document-save-back-handle)::before {
  display: block;
}
.elementor-editor-active [class*=elementor-widget-loop]:hover .elementor-document-handle:not(.elementor-document-save-back-handle) .elementor-document-handle__inner {
  display: flex;
}
.elementor-editor-active [class*=elementor-widget-loop]:hover .e-loop-alternate-template {
  box-shadow: 0 0 0 1px #9DA5AE;
}

.e-loop-template-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}
.e-loop-template-canvas [data-elementor-type=loop-item].e-loop-item {
  max-width: var(--preview-width, 410px);
  width: var(--preview-width, 410px);
}
.e-loop-template-canvas [data-elementor-type=loop-item].e-loop-item #elementor-add-new-section {
  width: var(--preview-width, 410px);
}
/*# sourceMappingURL=preview.css.map */