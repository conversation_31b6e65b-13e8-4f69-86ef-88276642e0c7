/*! elementor-pro - v3.27.0 - 06-02-2025 */
.elementor-editor-preview .woocommerce-notices-wrapper {
  padding: 2em 2em 0.3em 2em;
}
.elementor-editor-preview .woocommerce-notices-wrapper.elementor-loading {
  opacity: 0.3;
}

.e-wc-message-notice .woocommerce-message {
  border: 0px solid #000000;
  border-top: 3px solid var(--message-message-icon-color, #95AC3C);
  background-color: #FAFBF5;
  color: var(--message-message-text-color, #3f444b);
  border-radius: var(--message-box-border-radius, 0);
}
.e-wc-message-notice .woocommerce-message a:not([class]),
.e-wc-message-notice .woocommerce-message .restore-item {
  color: var(--notice-message-normal-links-color, #3f444b);
  text-decoration: underline;
  font-size: 16px;
  line-height: 24px;
  font-family: Roboto;
  font-weight: 400;
  line-height: 24px;
  text-transform: none;
  text-shadow: none;
  font-style: normal;
  letter-spacing: 0px;
}
.e-wc-message-notice .woocommerce-message a:not([class]):hover,
.e-wc-message-notice .woocommerce-message .restore-item:hover {
  color: var(--notice-message-hover-links-color, #3f444b);
}
.e-wc-message-notice .woocommerce-message .button {
  border-style: var(--message-border-type, solid);
  border-color: var(--message-border-color, #95AC3C);
  border-width: 1px;
  color: var(--message-buttons-normal-text-color, #95AC3C);
  background-color: transparent;
  padding: var(--message-buttons-padding, 6px 12px);
  font-size: 14px;
  border-radius: var(--message-buttons-border-radius, 0);
  line-height: 1;
  font-weight: 400;
  transition-duration: var(--message-buttons-hover-transition-duration, 0.3s);
  transition-property: color, background, box-shadow;
  text-transform: none;
  text-shadow: none;
  font-style: normal;
  letter-spacing: 0px;
  font-family: Roboto, sans-serif;
}
.e-wc-message-notice .woocommerce-message .button:hover {
  color: var(--message-buttons-hover-text-color, #95AC3C);
  border-color: var(--message-buttons-hover-border-color, #95AC3C);
  background-color: transparent;
}
.e-wc-message-notice .woocommerce-message::before {
  color: var(--message-message-icon-color, #95AC3C);
}
.e-wc-info-notice {
  /* Need to override some 'Links' styling from My Account Widget */
}
.e-wc-info-notice .e-my-account-tab:not(.e-my-account-tab__dashboard--custom) .woocommerce .woocommerce-info .woocommerce-Button {
  color: var(--info-buttons-normal-text-color, #3F84B9);
}
.e-wc-info-notice .woocommerce-info {
  border: 0px solid #000000;
  border-top: 3px solid var(--info-message-icon-color, #3F84B9);
  background-color: #F6F9FB;
  color: var(--info-message-text-color, #3f444b);
  border-radius: var(--info-box-border-radius, 0);
}
.e-wc-info-notice .woocommerce-info .button {
  border-style: var(--info-border-type, solid);
  border-color: var(--info-border-color, #3F84B9);
  border-width: 1px;
  color: var(--info-buttons-normal-text-color, #3F84B9);
  background-color: transparent;
  padding: var(--info-buttons-padding, 6px 12px);
  font-size: 14px;
  border-radius: var(--info-buttons-border-radius, 0);
  line-height: 1;
  font-weight: 400;
  transition-duration: var(--info-buttons-hover-transition-duration, 0.3s);
  transition-property: color, background, box-shadow;
  text-transform: none;
  text-shadow: none;
  font-style: normal;
  letter-spacing: 0px;
  font-family: Roboto, sans-serif;
}
.e-wc-info-notice .woocommerce-info .button:hover {
  color: var(--info-buttons-hover-text-color, #3F84B9);
  border-color: var(--info-buttons-hover-border-color, #3F84B9);
  background-color: transparent;
}
.e-wc-info-notice .woocommerce-info::before {
  color: var(--info-message-icon-color, #3F84B9);
}
.e-wc-error-notice {
  /* Need to override some styling from the My Account Widget */
}
.e-wc-error-notice .elementor-widget-woocommerce-my-account .woocommerce .woocommerce-error strong {
  color: var(--error-message-text-color, #3f444b);
}
.e-wc-error-notice .woocommerce-error {
  border: 0px solid #000000;
  border-top: 3px solid var(--error-message-icon-color, #b81c23);
  background-color: #F9F2F5;
  color: var(--error-message-text-color, #3f444b);
  border-radius: var(--error-box-border-radius, 0);
}
.e-wc-error-notice .woocommerce-error .wc-backward {
  color: var(--error-message-normal-links-color, #3f444b);
  text-decoration: underline;
  font-size: 16px;
  line-height: 24px;
  font-family: Roboto;
  font-weight: 400;
  line-height: 1;
  text-transform: none;
  text-shadow: none;
  font-style: normal;
  letter-spacing: 0px;
}
.e-wc-error-notice .woocommerce-error .wc-backward:hover {
  color: var(--error-message-hover-links-color, #3f444b);
}
.e-wc-error-notice .woocommerce-error::before {
  color: var(--error-message-icon-color, #b81c23);
}

div.wc-block-components-notice-banner {
  --wc-blocks-notice-banner-color: #2f2f2f;
  --wc-blocks-notice-banner-padding: 16px;
  --wc-blocks-notice-banner-gap: 12px;
  --wc-blocks-notice-banner-gap-smaller: 8px;
  --wc-blocks-notice-banner-gap-large: 24px;
  --wc-blocks-notice-banner-border-radius: 4px;
  --wc-blocks-notice-banner-font-size: .875em;
  --message-message-icon-color: #4ab866;
  --info-message-icon-color: #007cba;
  --error-message-icon-color: #cc1818;
}
div.wc-block-components-notice-banner {
  display: flex;
  align-items: stretch;
  align-content: flex-start;
  color: var(--wc-blocks-notice-banner-color);
  padding: var(--wc-blocks-notice-banner-padding) !important;
  gap: var(--wc-blocks-notice-banner-gap);
  margin: var(--wc-blocks-notice-banner-padding) 0;
  border-radius: var(--wc-blocks-notice-banner-border-radius);
  border-color: var(--wc-blocks-notice-banner-color);
  font-weight: 400;
  line-height: 1.5;
  border: 1px solid;
  font-size: var(--wc-blocks-notice-banner-font-size);
  background-color: #fff;
  box-sizing: border-box;
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content {
  align-self: center;
  white-space: normal;
  flex-basis: 100%;
  padding-left: initial;
  padding-right: var(--wc-blocks-notice-banner-padding);
}
body.rtl div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content {
  padding-right: initial;
  padding-left: var(--wc-blocks-notice-banner-padding);
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content:last-child {
  padding-left: initial;
  padding-right: 0;
}
body.rtl div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content:last-child {
  padding-right: initial;
  padding-left: 0;
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-block-components-notice-banner__summary {
  margin: 0 0 var(--wc-blocks-notice-banner-gap-smaller);
  font-weight: 600;
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content ul,
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content ol {
  margin: 0 0 0 var(--wc-blocks-notice-banner-gap-large);
  padding: 0;
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content ul li::after,
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content ol li::after {
  content: "";
  clear: both;
  display: block;
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward {
  color: var(--wc-blocks-notice-banner-color) !important;
  background: transparent;
  padding: 0 !important;
  margin: 0;
  border: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  opacity: 0.6;
  text-decoration-line: underline;
  text-underline-position: under;
  float: right;
}
body.rtl div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward {
  float: left;
}
div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward:hover, div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward:focus, div.wc-block-components-notice-banner > .wc-block-components-notice-banner__content .wc-forward:active {
  opacity: 1;
}
div.wc-block-components-notice-banner > svg {
  fill: #fff;
  border-radius: 50%;
  padding: 2px;
  background-color: var(--wc-blocks-notice-banner-color);
  flex-shrink: 0;
  flex-grow: 0;
}
div.wc-block-components-notice-banner > .wc-block-components-button {
  margin: 6px 0 0 auto !important;
  background: transparent none !important;
  box-shadow: none !important;
  outline: none !important;
  border: 0 !important;
  padding: 0 !important;
  height: 16px !important;
  width: 16px !important;
  min-height: auto !important;
  color: var(--wc-blocks-notice-banner-color) !important;
  min-width: 0 !important;
  flex: 0 0 16px;
  opacity: 0.6;
}
div.wc-block-components-notice-banner > .wc-block-components-button > svg {
  margin: 0 !important;
}
div.wc-block-components-notice-banner > .wc-block-components-button:hover, div.wc-block-components-notice-banner > .wc-block-components-button:focus, div.wc-block-components-notice-banner > .wc-block-components-button:active {
  opacity: 1;
}

div.wc-block-components-notice-banner.is-error {
  --wc-blocks-notice-banner-color: var(--error-message-text-color);
  --wc-blocks-notice-banner-border-radius: var(--error-box-border-radius);
  border-color: var(--wc-blocks-notice-banner-alert-red);
  background-color: #fff0f0;
}
div.wc-block-components-notice-banner.is-error .wc-backward {
  color: var(--error-message-normal-links-color);
}
div.wc-block-components-notice-banner.is-error .wc-backward:hover {
  color: var(--error-message-hover-links-color);
}
div.wc-block-components-notice-banner.is-error > svg {
  background-color: var(--error-message-icon-color);
  transform: rotate(180deg);
}

div.wc-block-components-notice-banner.is-success {
  --wc-blocks-notice-banner-color: var(--message-message-text-color);
  --wc-blocks-notice-banner-border-radius: var(--message-box-border-radius);
  border-color: var(--wc-blocks-notice-banner-alert-green);
  background-color: #f4fff7;
}
div.wc-block-components-notice-banner.is-success a:not([class]),
div.wc-block-components-notice-banner.is-success .restore-item {
  color: var(--notice-message-normal-links-color);
}
div.wc-block-components-notice-banner.is-success a:not([class]):hover,
div.wc-block-components-notice-banner.is-success .restore-item:hover {
  color: var(--notice-message-hover-links-color);
}
div.wc-block-components-notice-banner.is-success > svg {
  background-color: var(--message-message-icon-color);
}
div.wc-block-components-notice-banner.is-success .button {
  border-style: var(--message-border-type);
  border-color: var(--message-border-color);
  color: var(--message-buttons-normal-text-color) !important;
  padding: var(--message-buttons-padding) !important;
  border-radius: var(--message-buttons-border-radius);
  transition-duration: var(--message-buttons-hover-transition-duration);
}
div.wc-block-components-notice-banner.is-success .button:hover {
  color: var(--message-buttons-hover-text-color) !important;
  border-color: var(--message-buttons-hover-border-color);
}

div.wc-block-components-notice-banner.is-info {
  --wc-blocks-notice-banner-color: var(--info-message-text-color);
  --wc-blocks-notice-banner-border-radius: var(--info-box-border-radius);
  border-color: #007cba;
  background-color: #f4f8ff;
}
div.wc-block-components-notice-banner.is-info > svg {
  background-color: var(--info-message-icon-color);
}
div.wc-block-components-notice-banner.is-info .button {
  border-style: var(--info-border-type);
  border-color: var(--info-border-color);
  color: var(--info-buttons-normal-text-color) !important;
  padding: var(--info-buttons-padding) !important;
  border-radius: var(--info-buttons-border-radius);
  transition-duration: var(--info-buttons-hover-transition-duration);
}
div.wc-block-components-notice-banner.is-info .button:hover {
  color: var(--info-buttons-hover-text-color) !important;
  border-color: var(--info-buttons-hover-border-color);
}
/*# sourceMappingURL=woocommerce-notices.css.map */