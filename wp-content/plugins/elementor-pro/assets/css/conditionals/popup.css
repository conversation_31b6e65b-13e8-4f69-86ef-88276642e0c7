[data-elementor-type=popup]:not(.elementor-edit-area) {
  display: none;
}
[data-elementor-type=popup] .elementor-section-wrap:not(:empty) + #elementor-add-new-section {
  display: none;
}

.elementor-popup-modal.dialog-type-lightbox {
  display: flex;
  pointer-events: none;
  background-color: transparent;
  user-select: auto;
}
.elementor-popup-modal .dialog-header, .elementor-popup-modal .dialog-buttons-wrapper {
  display: none;
}
.elementor-popup-modal .dialog-close-button {
  display: none;
  top: 20px;
  margin-top: 0;
  inset-inline-end: 20px;
  opacity: 1;
  z-index: 9999;
  pointer-events: all;
}
.elementor-popup-modal .dialog-close-button svg {
  fill: #1f2124;
  height: 1em;
  width: 1em;
}
.elementor-popup-modal .dialog-widget-content {
  background-color: #FFFFFF;
  width: initial;
  overflow: visible;
  max-width: 100%;
  max-height: 100%;
  border-radius: 0;
  box-shadow: none;
  pointer-events: all;
}
.elementor-popup-modal .dialog-message {
  width: 640px;
  max-width: 100vw;
  max-height: 100vh;
  padding: 0;
  overflow: auto;
  display: flex;
}
.elementor-popup-modal .elementor {
  width: 100%;
}

/*# sourceMappingURL=popup.css.map */