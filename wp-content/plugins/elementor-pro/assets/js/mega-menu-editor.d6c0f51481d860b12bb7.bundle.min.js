/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[811],{4570:(e,t,n)=>{n(6281);const i=n(7842);e.exports=i.extend({__construct(){this.cache={},i.prototype.__construct.apply(this,arguments)},onInit(){elementor.channels.editor.on("editor:widget:mega-menu:section_layout:activated",this.maybeSetContentWidthValue)},maybeSetContentWidthValue(){const e=this.getEditorControlView("content_width"),t=e.getControlValue();["","full","boxed"].includes(t)&&(e.setValue("full_width"),e.applySavedValue())}})},2644:(e,t,n)=>{var i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n(3824)),r=i(n(4570)),s=i(n(9766));class Module extends elementorModules.editor.utils.Module{constructor(){super(),elementor.elementsManager.registerElementType(new a.default),new r.default,this.urlHelper=new s.default}getCurrentMenuItemClass(e,t){if(e=e?.trim(e),!e||!t)return"";const n=this.urlHelper.parse_url(t),i=this.urlHelper.parse_url(e);return _.isEqual(n,i)?"e-current":""}onElementorFrontendInit(){elementor.on("document:loaded",this.closeAllMegaMenus.bind(this))}closeAllMegaMenus(){const e=elementor.$previewContents[0].querySelectorAll(".elementor-widget-n-menu");e.length&&Array.from(e).forEach((e=>{const t=e.getAttribute("data-id");window.jQuery(window).trigger("elementor/mega-menu/dropdown-toggle-by-keyboard",{widgetId:t,show:!1})}))}}t.default=Module},3824:(e,t,n)=>{var i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedModule=void 0;var a=i(n(1290));class NestedModule extends elementor.modules.elements.types.NestedElementBase{getType(){return"mega-menu"}getView(){return a.default}}t.NestedModule=NestedModule;t.default=NestedModule},9766:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.UrlHelper=void 0;class UrlHelper{parse_url(e){try{const{hostname:t,pathname:n,search:i}=new URL(e),a=t.replace("www.",""),r=/^\/+|\/+$/g;return[a,n.replace(r,""),i]}catch(e){return!1}}}t.UrlHelper=UrlHelper;t.default=UrlHelper},1290:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class View extends $e.components.get("nested-elements").exports.NestedView{constructor(){super(...arguments),this.isRendering=!1,this.itemTitle="item_title",this.itemLink="item_link",this.internalUrl="internal-url",this.itemLinkSelector=".elementor-control-item_link"}filter(e,t){return e.attributes.dataIndex=t+1,e.attributes.widgetId=e.id,!0}onAddChild(e){const t=e._parent.$el.find(".e-n-menu")[0]?.dataset.widgetNumber||e.model.attributes.widgetId,n=e.model.attributes.dataIndex,i=e._parent.$el.find(`.e-n-menu-item-title[data-tab-index="${n}"]`)?.attr("id")||e.model.attributes.widgetId+" "+n;e.$el.attr({id:"e-n-menu-content-"+t+n,role:"menu","aria-labelledby":i,"data-tab-index":n})}getChildViewContainer(e,t){const{elements_placeholder_selector:n,child_container_placeholder_selector:i}=this.model.config.defaults;return void 0!==t&&void 0!==t._index&&i?e.$el.find(`${i}`)[t._index]:n?e.$el.find(this.model.config.defaults.elements_placeholder_selector):super.getChildViewContainer(e,t)}attachBuffer(e,t){const n=this.getChildViewContainer(e);if(this.model?.config?.support_improved_repeaters&&this.model?.config?.is_interlaced){const e=(this.model?.config?.defaults?.child_container_placeholder_selector||"").replace(".","");this._updateChildContainers(n[0],e,t)}else n.append(t)}_updateChildContainers(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;_.each(e.children,(a=>{if(a.classList?.contains(t)){const t=n.childNodes.length;a.appendChild(n.childNodes[0]),n.appendChild(a),e.append(n.childNodes[t-1]),i++}else this._updateChildContainers(a,t,n,i)}))}renderOnChange(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!this.allowRender)return;if(this.isRendering)return void(this.isRendering=!1);const n=this.renderDataBindings(e,this.dataBindings,t);n instanceof Promise&&n.then((t=>{t||this.renderChanges(e)})),n||this.renderChanges(e)}renderDataBindings(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(!this.dataBindings?.length)return!1;let i=!1;const renderDataBinding=async t=>{if(void 0!==e.changed[t.dataset.bindingSetting])return t.el.innerHTML=e.changed[t.dataset.bindingSetting],!0;if(!e?.changed.__dynamic__||!n.length)return!1;if(!this.isTitleOrLinkChanged(e))return!0;const{bindingSetting:i}=t.dataset,a=this.getChangedDynamicControlKey(e);let r=e.changed[i];if(this.isInternalUrl(e?.changed?.__dynamic__?.item_link)&&this.isSettingChanged(e,this.itemLink))return await this.getDynamicValue(e,a,i,t,n);if(this.isAtomicDynamic(e.changed,t,a)){const s=await this.getDynamicValue(e,a,i,t,n);if(this.itemLink===a)return!0;s&&(r=s)}return void 0!==r&&(t.el.innerHTML=r,!0)};for(const a of t){switch(a.dataset.bindingType){case"repeater-item":{const r=this.container.repeaters[a.dataset.bindingRepeaterName];if(!r)break;const s=r.children.find((t=>t.id===e.attributes._id));if(s?.parent?.children.indexOf(s)+1===parseInt(a.dataset.bindingIndex))i=renderDataBinding(a);else if(t.indexOf(a)+1===this.getRepeaterItemActiveIndex()){if(this.isItemLinkChild(n))return!0;i=this.tryHandleDynamicCoverSettings(a,e)}}break;case"content":i=renderDataBinding(a)}if(i)break}return i}isAtomicDynamic(e,t,n){return t.el.hasAttribute("data-binding-dynamic")&&(this.itemTitle===n||this.itemLink===n)}async getDynamicValue(e,t,n,i,a){const r={active:!0},s=this.extractValueToParse(this.getChangedData(e,t,n));if(void 0===s)return e.attributes[t];const d=await this.getDataFromCacheOrBackend(s,r);return this.itemTitle===t?d:(void 0!==d&&this.tryFormatDynamicMegaMenuUrl(s,i,a,t,r),e.attributes[t])}extractValueToParse(e){return"object"==typeof e?e[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"url"]:e}getChangedDynamicControlKey(e){if(!e?.changed?.__dynamic__)return Object.keys(e.changed)[0];const t=this.findUniqueKey(e?.changed?.__dynamic__,e?._previousAttributes?.__dynamic__)[0];return t||(this.isSettingChanged(e,this.itemLink)?this.itemLink:this.itemTitle)}tryFormatDynamicMegaMenuUrl(e,t,n,i,a){const r=this.getDynamicTagName(e);if(this.itemLink!==i||"internal_link"===r)return!1;const s=elementor.dynamicTags.parseTagsText(e,a,elementor.dynamicTags.getTagDataContent);elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/dynamic/url_change",{detail:{element:t.el,actionName:e&&r,value:s}})),t.el=Array.from(n)[0].querySelectorAll(".e-n-menu-title-text")[t.dataset.bindingIndex-1]}getDynamicTagName(e){const t=e.match(/name="([^"]*)"/);return t?t[1]:null}isInternalUrl(e){return!!e&&this.internalUrl===this.getDynamicTagName(e)}isItemLinkChild(e){return e[0].closest(this.itemLinkSelector)}async getDataFromCacheOrBackend(e,t){try{return elementor.dynamicTags.parseTagsText(e,t,elementor.dynamicTags.getTagDataContent)}catch{return await new Promise((e=>{elementor.dynamicTags.refreshCacheFromServer((()=>{e()}))})),!_.isEmpty(elementor.dynamicTags.cache)&&elementor.dynamicTags.parseTagsText(e,t,elementor.dynamicTags.getTagDataContent)}}getChangedDataForRemovedItem(e,t,n){return e.attributes?.[t]?.[n]||e.attributes?.[t]}getChangedDataForAddedItem(e,t,n){return e.attributes?.__dynamic__?.[t]?.[n]||e.attributes?.__dynamic__?.[t]}getChangedData(e,t,n){const i=this.getChangedDataForRemovedItem(e,t,n);return this.getChangedDataForAddedItem(e,t,n)||i}getTitleWithAdvancedValues(e,t){const{attributes:n,_previousAttributes:i}=e;if(this.compareSettings(n,i,"fallback")&&(t=t.replace(new RegExp(i.fallback),"")),!t||n.fallback===t)return n.fallback||"";if(this.compareSettings(n,i,"before")&&(t=t.replace(i.before,"")),this.compareSettings(n,i,"after")&&(t=t.replace(new RegExp(i.after+"$"),"")),!t)return n.fallback||"";return t=this.getNewSettingsValue(n,i,"before")+t,t+=this.getNewSettingsValue(n,i,"after")}compareSettings(e,t,n){return t[n]&&t[n]!==e[n]}getNewSettingsValue(e,t,n){return t[n]!==e[n]&&e[n]||""}getRepeaterItemActiveIndex(){return this.getContainer().renderer.view.model.changed.editSettings.changed.activeItemIndex||this.getContainer().renderer.view.model.changed.editSettings.attributes.activeItemIndex}tryHandleDynamicCoverSettings(e,t){return!!this.isAdvancedDynamicSettings(t.attributes)&&(this.isRendering=!0,e.el.textContent=this.getTitleWithAdvancedValues(t,e.el.textContent),!0)}isAdvancedDynamicSettings(e){return"before"in e&&"after"in e&&"fallback"in e}isSettingChanged(e,t){return e.attributes.__dynamic__?.[t]!==e._previousAttributes.__dynamic__?.[t]}isTitleOrLinkChanged(e){return this.isSettingChanged(e,this.itemTitle)||this.isSettingChanged(e,this.itemLink)}}t.default=View}}]);