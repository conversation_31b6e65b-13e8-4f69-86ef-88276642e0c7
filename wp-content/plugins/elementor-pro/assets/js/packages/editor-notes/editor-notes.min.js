!function(){"use strict";var e=window.elementorV2.editorAppBar,o=window.elementorV2.editorV1Adapters,t=window.elementorV2.icons,n=window.wp.i18n;e.toolsMenu.registerToggleAction({id:"toggle-notes",priority:4,useProps:function(){const{isActive:e,isBlocked:i}=(0,o.__privateUseRouteStatus)("notes",{blockOnPreviewMode:!1});return{title:(0,n.__)("Notes","elementor-pro"),icon:t.MessageIcon,onClick:()=>{const e=window,t=e?.elementor?.editorEvents?.config;t&&e.elementor.editorEvents.dispatchEvent(t.names.topBar.notes,{location:t.locations.topBar,secondaryLocation:t.secondaryLocations.notes,trigger:t.triggers.toggleClick,element:t.elements.buttonIcon}),(0,o.__privateRunCommand)("notes/toggle")},selected:e,disabled:i}}}),(window.elementorV2=window.elementorV2||{}).editorNotes={}}();