/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[621],{4702:(e,o)=>{Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;class _default extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{woocommerceNotices:":not(.woocommerce-NoticeGroup) .wc-block-components-notice-banner, .woocommerce-NoticeGroup, :not(.woocommerce-NoticeGroup) .woocommerce-error, :not(.woocommerce-NoticeGroup) .woocommerce-message, :not(.woocommerce-NoticeGroup) .woocommerce-info",noticesWrapper:".e-woocommerce-notices-wrapper"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$documentScrollToElements:elementorFrontend.elements.$document.find("html, body"),$woocommerceCheckoutForm:elementorFrontend.elements.$body.find(".form.checkout"),$noticesWrapper:this.$element.find(e.noticesWrapper)}}moveNotices(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const o=this.getSettings("selectors");let t=elementorFrontend.elements.$body.find(o.woocommerceNotices);if((elementorFrontend.isEditMode()||elementorFrontend.isWPPreviewMode())&&(t=t.filter(":not(.e-notices-demo-notice)")),e&&this.elements.$documentScrollToElements.stop(),this.elements.$noticesWrapper.prepend(t),this.is_ready||(this.elements.$noticesWrapper.removeClass("e-woocommerce-notices-wrapper-loading"),this.is_ready=!0),e){let e=t;e.length||(e=this.elements.$woocommerceCheckoutForm),e.length&&this.elements.$documentScrollToElements.animate({scrollTop:e.offset().top-document.documentElement.clientHeight/2},1e3)}}onInit(){super.onInit(),this.is_ready=!1,this.moveNotices(!0)}bindEvents(){elementorFrontend.elements.$body.on("updated_wc_div updated_checkout updated_cart_totals applied_coupon removed_coupon applied_coupon_in_checkout removed_coupon_in_checkout checkout_error",(()=>this.moveNotices(!0)))}}o.default=_default}}]);