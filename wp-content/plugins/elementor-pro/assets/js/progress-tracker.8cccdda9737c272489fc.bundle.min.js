/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[581],{7238:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=class CircularProgress{constructor(e,r){this.settings=r,this.lastKnownProgress=null,this.circularProgressTracker=e.find(".elementor-scrolling-tracker-circular")[0],this.circularCurrentProgress=this.circularProgressTracker.getElementsByClassName("current-progress")[0],this.circularCurrentProgressPercentage=this.circularProgressTracker.getElementsByClassName("current-progress-percentage")[0];const s=2*this.circularCurrentProgress.r.baseVal.value*Math.PI;this.circularCurrentProgress.style.strokeDasharray=`${s} ${s}`,this.circularCurrentProgress.style.strokeDashoffset=s,this.elements=this.cacheElements(),this.resizeObserver=new ResizeObserver((()=>{this.lastKnownProgress&&this.updateProgress(this.lastKnownProgress)})),this.resizeObserver.observe(this.circularProgressTracker)}cacheElements(){return{circularProgressTracker:this.circularProgressTracker,circularCurrentProgress:this.circularCurrentProgress,circularCurrentProgressPercentage:this.circularCurrentProgressPercentage}}updateProgress(e){if(e<=0)return this.elements.circularCurrentProgress.style.display="none",void(this.elements.circularCurrentProgressPercentage.style.display="none");this.elements.circularCurrentProgress.style.display="block",this.elements.circularCurrentProgressPercentage.style.display="block";const r=2*this.elements.circularCurrentProgress.r.baseVal.value*Math.PI,s=r-e/100*r;this.lastKnownProgress=e,this.elements.circularCurrentProgress.style.strokeDasharray=`${r} ${r}`,this.elements.circularCurrentProgress.style.strokeDashoffset="ltr"===this.settings.direction?-s:s,"yes"===this.settings.percentage&&(this.elements.circularCurrentProgressPercentage.innerHTML=Math.round(e)+"%")}onDestroy(){this.resizeObserver.unobserve(this.circularProgressTracker)}}},5138:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=class LinearProgress{constructor(e,r){this.settings=r,this.linearProgressTracker=e.find(".elementor-scrolling-tracker-horizontal")[0],this.linearCurrentProgress=this.linearProgressTracker.getElementsByClassName("current-progress")[0],this.linearCurrentProgressPercentage=this.linearProgressTracker.getElementsByClassName("current-progress-percentage")[0],this.elements=this.cacheElements()}cacheElements(){return{linearProgressTracker:this.linearProgressTracker,linearCurrentProgress:this.linearCurrentProgress,linearCurrentProgressPercentage:this.linearCurrentProgressPercentage}}updateProgress(e){e<1?this.elements.linearCurrentProgress.style.display="none":(this.elements.linearCurrentProgress.style.display="flex",this.elements.linearCurrentProgress.style.width=e+"%","yes"===this.settings.percentage&&this.elements.linearCurrentProgress.getBoundingClientRect().width>1.5*this.elements.linearCurrentProgressPercentage.getBoundingClientRect().width?(this.elements.linearCurrentProgressPercentage.innerHTML=Math.round(e)+"%",this.elements.linearCurrentProgressPercentage.style.color=getComputedStyle(this.linearCurrentProgress).getPropertyValue("--percentage-color")):this.elements.linearCurrentProgressPercentage.style.color="transparent")}}},287:(e,r,s)=>{var t=s(6784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=t(s(7238)),l=t(s(5138));class ProgressTracker extends elementorModules.frontend.handlers.Base{onInit(){elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),this.circular="circular"===this.getElementSettings().type;const e=this.circular?n.default:l.default;this.progressBar=new e(this.$element,this.getElementSettings()),this.progressPercentage=0,this.scrollHandler(),this.handler=this.scrollHandler.bind(this),this.initListeners()}getTrackingElementSelector(){let e;switch(this.getElementSettings().relative_to){case"selector":e=jQuery(this.getElementSettings().selector);break;case"post_content":e=jQuery(".elementor-widget-theme-post-content");break;default:e=this.isScrollSnap()?jQuery("#e-scroll-snap-container"):elementorFrontend.elements.$body}return e}isScrollSnap(){return"yes"===(this.isEdit?elementor.settings.page.model.attributes.scroll_snap:elementorFrontend.config.settings.page.scroll_snap)}addScrollSnapContainer(){this.isScrollSnap()&&!jQuery("#e-scroll-snap-container").length&&jQuery("body").wrapInner('<div id="e-scroll-snap-container" />')}scrollHandler(){this.addScrollSnapContainer();const e=this.getTrackingElementSelector(),r=e.is(elementorFrontend.elements.$body)||e.is(jQuery("#e-scroll-snap-container"))?-100:0;this.progressPercentage=elementorModules.utils.Scroll.getElementViewportPercentage(this.getTrackingElementSelector(),{start:r,end:-100}),this.progressBar.updateProgress(this.progressPercentage)}initListeners(){window.addEventListener("scroll",this.handler),elementorFrontend.elements.$body[0].addEventListener("scroll",this.handler)}onDestroy(){this.progressBar.onDestroy&&this.progressBar.onDestroy(),window.removeEventListener("scroll",this.handler),elementorFrontend.elements.$body[0].removeEventListener("scroll",this.handler)}}r.default=ProgressTracker}}]);