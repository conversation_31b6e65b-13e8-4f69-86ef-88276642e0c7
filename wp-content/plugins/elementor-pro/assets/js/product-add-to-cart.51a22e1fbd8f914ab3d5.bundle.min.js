/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[787],{3046:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;class Base extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{stickyRightColumn:".e-sticky-right-column"},classes:{stickyRightColumnActive:"e-sticky-right-column--active"}}}getDefaultElements(){const t=this.getSettings("selectors");return{$stickyRightColumn:this.$element.find(t.stickyRightColumn)}}bindEvents(){elementorFrontend.elements.$document.on("select2:open",(t=>{this.addSelect2Wrapper(t)}))}addSelect2Wrapper(t){const e=jQuery(t.target).data("select2");e.$dropdown&&e.$dropdown.addClass("e-woo-select2-wrapper")}isStickyRightColumnActive(){const t=this.getSettings("classes");return this.elements.$stickyRightColumn.hasClass(t.stickyRightColumnActive)}activateStickyRightColumn(){const t=this.getElementSettings(),e=elementorFrontend.elements.$wpAdminBar,n=this.getSettings("classes");let s=t.sticky_right_column_offset||0;e.length&&"fixed"===e.css("position")&&(s+=e.height()),"yes"===this.getElementSettings("sticky_right_column")&&(this.elements.$stickyRightColumn.addClass(n.stickyRightColumnActive),this.elements.$stickyRightColumn.css("top",s+"px"))}deactivateStickyRightColumn(){if(!this.isStickyRightColumnActive())return;const t=this.getSettings("classes");this.elements.$stickyRightColumn.removeClass(t.stickyRightColumnActive)}toggleStickyRightColumn(){this.getElementSettings("sticky_right_column")?this.isStickyRightColumnActive()||this.activateStickyRightColumn():this.deactivateStickyRightColumn()}equalizeElementHeight(t){if(t.length){t.removeAttr("style");let e=0;t.each(((t,n)=>{e=Math.max(e,n.offsetHeight)})),0<e&&t.css({height:e+"px"})}}removePaddingBetweenPurchaseNote(t){t&&t.each(((t,e)=>{jQuery(e).prev().children("td").addClass("product-purchase-note-is-below")}))}updateWpReferers(){const t=this.getSettings("selectors"),e=this.$element.find(t.wpHttpRefererInputs),n=new URL(document.location);n.searchParams.set("elementorPageId",elementorFrontend.config.post.id),n.searchParams.set("elementorWidgetId",this.getID()),e.attr("value",n)}}e.default=Base},6973:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=s(n(3046));class ProductAddToCart extends o.default{getDefaultSettings(){return{selectors:{quantityInput:".e-loop-add-to-cart-form input.qty",addToCartButton:".e-loop-add-to-cart-form .ajax_add_to_cart",addedToCartButton:".added_to_cart",loopFormContainer:".e-loop-add-to-cart-form-container"}}}getDefaultElements(){const t=this.getSettings("selectors");return{$quantityInput:this.$element.find(t.quantityInput),$addToCartButton:this.$element.find(t.addToCartButton)}}updateAddToCartButtonQuantity(){this.elements.$addToCartButton.attr("data-quantity",this.elements.$quantityInput.val())}handleAddedToCart(t){const e=this.getSettings("selectors"),n=t.siblings(e.addedToCartButton),s=n.parents(e.loopFormContainer);s.children(e.addedToCartButton).remove(),s.append(n)}bindEvents(){super.bindEvents(...arguments),this.elements.$quantityInput.on("change",(()=>{this.updateAddToCartButtonQuantity()})),elementorFrontend.elements.$body.off("added_to_cart.elementor-woocommerce-product-add-to-cart"),elementorFrontend.elements.$body.on("added_to_cart.elementor-woocommerce-product-add-to-cart",((t,e,n,s)=>{this.handleAddedToCart(s)}))}}e.default=ProductAddToCart}}]);