/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[249],{9744:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class FloatingBarDomHelper{constructor(e){this.$element=e}maybeMoveToTop(){const e=this.$element[0],t=e.querySelector(".e-floating-bars");if(elementorFrontend.isEditMode())t.classList.add("is-sticky");else if(e.dataset.widget_type.startsWith("floating-bars")&&t.classList.contains("has-vertical-position-top")&&!t.classList.contains("is-sticky")){const t=document.getElementById("wpadminbar"),n=e.closest(".elementor");t?t.after(n):document.body.prepend(n)}}}},2319:(e,t,n)=>{var s=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=s(n(8562)),o=s(n(9744)),l=s(n(8243));class FloatingBarsHandler extends i.default{getDefaultSettings(){return{selectors:{main:".e-floating-bars",closeButton:".e-floating-bars__close-button",playButton:".e-floating-bars__play-button",pauseButton:".e-floating-bars__pause-button",headline:".e-floating-bars__headline",headlines:".e-floating-bars__headlines",headlinesInner:".e-floating-bars__headlines-inner",overlay:".e-floating-bars__overlay"},constants:{isHidden:"is-hidden",isSticky:"is-sticky",hasVerticalPositionTop:"has-vertical-position-top",hasVerticalPositionBottom:"has-vertical-position-bottom",isPaused:"is-paused",animationTypeControl:"style_ticker_animation_type",autoplay:"autoplay"}}}onElementChange(e){["style_headline_text_typography_font_size","style_headlines_icon_size","style_floating_bar_padding","style_floating_bar_controls_size","style_floating_bar_element_spacing"].includes(e)&&this.initDefaultState()}getDefaultElements(){const e=this.getSettings("selectors");return{main:this.$element[0].querySelector(e.main),closeButton:this.$element[0].querySelector(e.closeButton),pauseButton:this.$element[0].querySelector(e.pauseButton),playButton:this.$element[0].querySelector(e.playButton),headlineAll:this.$element[0].querySelectorAll(e.headline),headlines:this.$element[0].querySelector(e.headlines),headlinesInner:this.$element[0].querySelector(e.headlinesInner),overlay:this.$element[0].querySelector(e.overlay)}}getResponsiveSetting(e){const t=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),e,"",t)}bindEvents(){this.elements.closeButton&&this.elements.closeButton.addEventListener("click",this.closeFloatingBar.bind(this)),this.elements.pauseButton&&this.elements.pauseButton.addEventListener("click",this.pauseCarousel.bind(this)),this.elements.playButton&&this.elements.playButton.addEventListener("click",this.playCarousel.bind(this)),this.hasStickyElements()&&window.addEventListener("resize",this.handleStickyElements.bind(this))}isMobileDevice(){return["mobile","mobile_extra"].includes(elementorFrontend.getCurrentDeviceMode())}isStickyTop(){const{isSticky:e,hasVerticalPositionTop:t}=this.getSettings("constants");return this.elements.main.classList.contains(e)&&this.elements.main.classList.contains(t)}isStickyBottom(){const{isSticky:e,hasVerticalPositionBottom:t}=this.getSettings("constants");return this.elements.main.classList.contains(e)&&this.elements.main.classList.contains(t)}hasStickyElements(){return document.querySelectorAll(".elementor-sticky").length>0}pauseCarousel(){const{isPaused:e}=this.getSettings("constants");this.elements.headlines.classList.add(e),this.elements.playButton&&this.elements.pauseButton&&(this.elements.playButton.setAttribute("aria-hidden","false"),this.elements.pauseButton.setAttribute("aria-hidden","true"))}playCarousel(){const{isPaused:e}=this.getSettings("constants");this.elements.headlines.classList.remove(e),this.elements.playButton&&this.elements.pauseButton&&(this.elements.pauseButton.setAttribute("aria-hidden","false"),this.elements.playButton.setAttribute("aria-hidden","true"))}closeFloatingBar(){const{isHidden:e}=this.getSettings("constants");elementorFrontend.isEditMode()||(this.elements.main.classList.add(e),this.hasStickyElements()?this.handleStickyElements():this.isStickyTop()&&this.removeBodyPadding())}focusOnLoad(){this.elements.main.setAttribute("tabindex","0"),this.elements.main.focus({focusVisible:!0})}applyBodyPadding(){const e=this.elements.main.offsetHeight;document.body.style.paddingTop=`${e}px`}removeBodyPadding(){document.body.style.paddingTop="0"}cloneScrollerContent(){Array.from(this.elements.headlinesInner.children).forEach((e=>{const t=e.cloneNode(!0);t.setAttribute("aria-hidden","true"),t.classList.add("e-floating-bars__headline--clone"),this.elements.headlinesInner.appendChild(t)}))}cloneHeadlinesToFillContainer(){let e=this.elements.headlinesInner.offsetWidth;const t=this.elements.headlines.offsetWidth;for(;e<t;)this.cloneScrollerContent(),e=this.elements.headlinesInner.offsetWidth}cloneHeadlinesInner(){const e=this.elements.headlinesInner.cloneNode(!0),t=this.elements.overlay;e.classList.add("e-floating-bars__headlines-inner--clone"),e.setAttribute("aria-hidden","true"),this.elements.headlines.insertBefore(e,t)}handleBrowserScrollAnimation(){document.addEventListener("scroll",(()=>{const e=window.scrollY,t=this.elements.headlinesInner.lastElementChild;if(elementorFrontend.config.is_rtl){this.elements.headlinesInner.style.transform=`translateX(${e}px)`;t.lastElementChild.getBoundingClientRect().left>=this.elements.headlines.getBoundingClientRect().left&&this.cloneScrollerContent()}else{this.elements.headlinesInner.style.transform=`translateX(-${e}px)`;t.lastElementChild.getBoundingClientRect().right<=this.elements.headlines.getBoundingClientRect().right&&this.cloneScrollerContent()}}))}handleTickerClick(e){e.preventDefault();const{isPlaying:t}=this.getSettings("constants");if(this.elements.headlines.classList.contains(t))this.pauseCarousel();else{const t=e.currentTarget.getAttribute("href"),n=e.currentTarget.getAttribute("target");!!n?window.open(t,n):t&&(window.location.href=t),this.playCarousel()}}handleWPAdminBar(){const e=elementorFrontend.elements.$wpAdminBar;e.length&&(this.elements.main.style.top=`${e.height()}px`)}handleStickyElements(){const e=this.elements.main.offsetHeight,t=elementorFrontend.elements.$wpAdminBar,n=document.querySelectorAll(".elementor-sticky:not(.elementor-sticky__spacer)");0!==n.length&&(n.forEach((n=>{const s=n.getAttribute("data-settings"),i=JSON.parse(s)?.sticky,o="0px"===n.style.top||"top"===i,l="0px"===n.style.bottom||"bottom"===i;this.isStickyTop()&&o?t.length?n.style.top=`${e+t.height()}px`:n.style.top=`${e}px`:this.isStickyBottom()&&l&&(n.style.bottom=`${e}px`),elementorFrontend.isEditMode()&&(o?n.style.top=this.isStickyTop()?`${e}px`:"0px":l&&(n.style.bottom=this.isStickyBottom()?`${e}px`:"0px"))})),document.querySelectorAll(".elementor-sticky__spacer").forEach((t=>{const n=t.getAttribute("data-settings"),s=JSON.parse(n)?.sticky,i="0px"===t.style.top||"top"===s;this.isStickyTop()&&i&&(t.style.marginBottom=`${e}px`)})))}handleClickOutside(){const{isPlaying:e}=this.getSettings("constants");document.addEventListener("click",(t=>{this.elements.headlines.classList.contains(e)||this.elements.main.contains(t.target)||this.playCarousel()}))}initScrollingAnimation(){const{autoplay:e,animationTypeControl:t}=this.getSettings("constants");e===this.getResponsiveSetting(t)?(this.cloneHeadlinesInner(),this.elements.headlines.setAttribute("data-animated","true"),this.playCarousel(),this.isMobileDevice()&&(this.elements.headlineAll.forEach((e=>{e.addEventListener("click",this.handleTickerClick.bind(this))})),this.handleClickOutside())):this.handleBrowserScrollAnimation()}initDefaultState(){this.isStickyTop()&&this.handleWPAdminBar(),this.hasStickyElements()?this.handleStickyElements():this.isStickyTop()&&this.applyBodyPadding(),this.elements.main&&!elementorFrontend.isEditMode()&&this.focusOnLoad(),this.elements.headlinesInner&&this.cloneHeadlinesToFillContainer(),window.matchMedia("(prefers-reduced-motion: reduce)").matches||this.initScrollingAnimation()}setupInnerContainer(){this.elements.main.closest(".e-con-inner").classList.add("e-con-inner--floating-bars"),this.elements.main.closest(".e-con").classList.add("e-con--floating-bars")}onInit(){super.onInit(...arguments),this.clickTrackingHandler=new l.default({$element:this.$element});new o.default(this.$element).maybeMoveToTop(),this.initDefaultState(),this.setupInnerContainer()}}t.default=FloatingBarsHandler},8243:(e,t,n)=>{var s=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=s(n(8562));class ClickTrackingHandler extends i.default{clicks=[];getDefaultSettings(){return{selectors:{contentWrapper:".e-contact-buttons__content-wrapper",contentWrapperFloatingBars:".e-floating-bars",floatingBarCouponButton:".e-floating-bars__coupon-button",floatingBarsHeadline:".e-floating-bars__headline",contactButtonsVar4:".e-contact-buttons__contact-icon-link",contactButtonsVar5:".e-contact-buttons__chat-button",contactButtonsVar6:".e-contact-buttons-var-6",contactButtonsVar8:".e-contact-buttons-var-8",elementorWrapper:'[data-elementor-type="floating-buttons"]',contactButtonCore:".e-contact-buttons__send-button"}}}getDefaultElements(){const e=this.getSettings("selectors");return{contentWrapper:this.$element[0].querySelector(e.contentWrapper),contentWrapperFloatingBars:this.$element[0].querySelector(e.contentWrapperFloatingBars),contactButtonsVar5:this.$element[0].querySelector(e.contactButtonsVar5),contactButtonsVar6:this.$element[0].querySelector(e.contactButtonsVar6)}}bindEvents(){this.elements.contentWrapper&&this.elements.contentWrapper.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contactButtonsVar5&&this.elements.contactButtonsVar5.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contactButtonsVar6&&this.elements.contactButtonsVar6.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contentWrapperFloatingBars&&this.elements.contentWrapperFloatingBars.addEventListener("click",this.onChatButtonTrackClick.bind(this)),window.addEventListener("beforeunload",(()=>{this.clicks.length>0&&this.sendClicks()}))}onChatButtonTrackClick(e){const t=e.target||e.srcElement,n=this.getSettings("selectors"),s=[n.contactButtonsVar4,n.contactButtonsVar6,n.floatingBarCouponButton,n.floatingBarsHeadline,n.contactButtonCore];for(const e of s)(t.matches(e)||t.closest(e))&&this.getDocumentIdAndTrack(t,n);(t.matches(n.contactButtonsVar5)||t.closest(n.contactButtonsVar5))&&t.closest(".e-contact-buttons-var-5")&&this.getDocumentIdAndTrack(t,n)}getDocumentIdAndTrack(e,t){const n=e.closest(t.elementorWrapper).dataset.elementorId;this.trackClick(n)}trackClick(e){e&&(this.clicks.push(e),this.clicks.length>=10&&this.sendClicks())}sendClicks(){const e=new FormData;e.append("action","elementor_send_clicks"),e.append("_nonce",elementorFrontendConfig?.nonces?.floatingButtonsClickTracking),this.clicks.forEach((t=>e.append("clicks[]",t))),fetch(elementorFrontendConfig?.urls?.ajaxurl,{method:"POST",body:e}).then((()=>{this.clicks=[]}))}}t.default=ClickTrackingHandler},8562:e=>{e.exports=elementorModules.ViewModule.extend({$element:null,editorListeners:null,onElementChange:null,onEditSettingsChange:null,onPageSettingsChange:null,isEdit:null,__construct(e){this.isActive(e)&&(this.$element=e.$element,this.isEdit=this.$element.hasClass("elementor-element-edit-mode"),this.isEdit&&this.addEditorListeners())},isActive:()=>!0,isElementInTheCurrentDocument(){return!!elementorFrontend.isEditMode()&&elementor.documents.currentDocument.id.toString()===this.$element[0].closest(".elementor").dataset.elementorId},findElement(e){var t=this.$element;return t.find(e).filter((function(){return jQuery(this).parent().closest(".elementor-element").is(t)}))},getUniqueHandlerID(e,t){return e||(e=this.getModelCID()),t||(t=this.$element),e+t.attr("data-element_type")+this.getConstructorID()},initEditorListeners(){var e=this;if(e.editorListeners=[{event:"element:destroy",to:elementor.channels.data,callback(t){t.cid===e.getModelCID()&&e.onDestroy()}}],e.onElementChange){const t=e.getWidgetType()||e.getElementType();let n="change";"global"!==t&&(n+=":"+t),e.editorListeners.push({event:n,to:elementor.channels.editor,callback(t,n){e.getUniqueHandlerID(n.model.cid,n.$el)===e.getUniqueHandlerID()&&e.onElementChange(t.model.get("name"),t,n)}})}e.onEditSettingsChange&&e.editorListeners.push({event:"change:editSettings",to:elementor.channels.editor,callback(t,n){if(n.model.cid!==e.getModelCID())return;const s=Object.keys(t.changed)[0];e.onEditSettingsChange(s,t.changed[s])}}),["page"].forEach((function(t){var n="on"+t[0].toUpperCase()+t.slice(1)+"SettingsChange";e[n]&&e.editorListeners.push({event:"change",to:elementor.settings[t].model,callback(t){e[n](t.changed)}})}))},getEditorListeners(){return this.editorListeners||this.initEditorListeners(),this.editorListeners},addEditorListeners(){var e=this.getUniqueHandlerID();this.getEditorListeners().forEach((function(t){elementorFrontend.addListenerOnce(e,t.event,t.callback,t.to)}))},removeEditorListeners(){var e=this.getUniqueHandlerID();this.getEditorListeners().forEach((function(t){elementorFrontend.removeListeners(e,t.event,null,t.to)}))},getElementType(){return this.$element.data("element_type")},getWidgetType(){const e=this.$element.data("widget_type");if(e)return e.split(".")[0]},getID(){return this.$element.data("id")},getModelCID(){return this.$element.data("model-cid")},getElementSettings(e){let t={};const n=this.getModelCID();if(this.isEdit&&n){const e=elementorFrontend.config.elements.data[n],s=e.attributes;let i=s.widgetType||s.elType;s.isInner&&(i="inner-"+i);let o=elementorFrontend.config.elements.keys[i];o||(o=elementorFrontend.config.elements.keys[i]=[],jQuery.each(e.controls,((e,t)=>{(t.frontend_available||t.editor_available)&&o.push(e)}))),jQuery.each(e.getActiveControls(),(function(e){if(-1!==o.indexOf(e)){let n=s[e];n.toJSON&&(n=n.toJSON()),t[e]=n}}))}else t=this.$element.data("settings")||{};return this.getItems(t,e)},getEditSettings(e){var t={};return this.isEdit&&(t=elementorFrontend.config.elements.editSettings[this.getModelCID()].attributes),this.getItems(t,e)},getCurrentDeviceSetting(e){return elementorFrontend.getCurrentDeviceSetting(this.getElementSettings(),e)},onInit(){this.isActive(this.getSettings())&&elementorModules.ViewModule.prototype.onInit.apply(this,arguments)},onDestroy(){this.isEdit&&this.removeEditorListeners(),this.unbindEvents&&this.unbindEvents()}})}}]);