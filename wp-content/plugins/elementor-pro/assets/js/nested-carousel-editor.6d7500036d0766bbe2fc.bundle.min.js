/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[625],{9886:(e,t,s)=>{var l=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=l(s(7936));t.default=class Module{constructor(){elementor.elementsManager.registerElementType(new r.default)}}},7936:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedCarousel=void 0;class NestedCarousel extends elementor.modules.elements.types.NestedElementBase{getType(){return"nested-carousel"}}t.NestedCarousel=NestedCarousel;t.default=NestedCarousel}}]);