/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[912],{9774:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(6914);class MenuTitleKeyboardHandler extends elementorModules.frontend.handlers.Base{isEditorElementsChanged=!1;__construct(){super.__construct(...arguments),this.focusableElementSelector=(0,s.focusableElementSelectors)(),this.handleMenuToggleKeydown=this.handleMenuToggleKeydown.bind(this)}getDefaultSettings(){return{selectors:{widgetInnerWrapper:".e-n-menu",menuItem:".e-n-menu-item",menuItemWrapper:".e-n-menu-title",focusableMenuElement:".e-focus",itemContainer:".e-n-menu-content > .e-con, .e-n-menu-heading > .e-con",menuToggle:".e-n-menu-toggle",directTabTitle:":scope > .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-title",tabDropdown:".e-n-menu-dropdown-icon"},ariaAttributes:{titleStateAttribute:"aria-expanded",activeTitleSelector:'[aria-expanded="true"]',titleControlAttribute:"aria-controls"},datasets:{titleIndex:"data-focus-index"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$menuItemWrappers:this.findElement(e.menuItemWrapper),$focusableMenuElements:this.findElement(e.focusableMenuElement),$itemContainers:this.findElement(e.itemContainer),$focusableContainerElements:this.getFocusableElements(this.findElement(e.itemContainer)),$menuToggle:this.findElement(e.menuToggle)}}getFocusableElements(e){return e.find(this.focusableElementSelector).not('[disabled], [inert], [tabindex="-1"]')}getTitleIndex(e){const{titleIndex:t}=this.getSettings("datasets");return parseInt(e?.getAttribute(t))}getTitleFilterSelector(e){const{titleIndex:t}=this.getSettings("datasets");return`[${t}="${e}"]`}getActiveTitleElement(){const e=this.getSettings("ariaAttributes").activeTitleSelector;return this.elements.$focusableMenuElements.filter(e)}onInit(){super.onInit(...arguments);let e=1;this.elements.$focusableMenuElements.each(((t,n)=>{n.setAttribute(this.getSettings("datasets").titleIndex,e++)}))}getTitleEvents(){return{keydown:this.handleTitleKeyboardNavigation.bind(this),keyup:this.handeTitleKeyUp.bind(this)}}getContentElementEvents(){return{keydown:this.handleContentElementKeyboardNavigation.bind(this)}}bindEvents(){this.elements.$focusableMenuElements.on(this.getTitleEvents()),this.elements.$focusableContainerElements.on(this.getContentElementEvents()),elementorFrontend.elements.$window.on("keydown",this.handleMenuToggleKeydown),elementorFrontend.elements.$window.on("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}unbindEvents(){this.elements.$focusableMenuElements.off(this.getTitleEvents()),this.elements.$focusableContainerElements.off(this.getContentElementEvents()),elementorFrontend.elements.$window.off("keydown",this.handleMenuToggleKeydown),elementorFrontend.elements.$window.off("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}handleMenuToggleKeydown(e){"Escape"===e.key&&(e.preventDefault(),e.stopPropagation(),this.closeMenuDropdown())}handleTitleKeyboardNavigation(e){switch(e.key){case"Tab":this.maybeRebindFocusableElements();const t=this.elements.$focusableMenuElements,n=!e.shiftKey,s=n&&t.last().is(jQuery(e.currentTarget)),i=!n&&t.first().is(jQuery(e.currentTarget));if(this.isDropdownLayout()&&!s&&!i)return;const o=!e.currentTarget.getAttribute("aria-expanded")||"false"===e.currentTarget?.getAttribute("aria-expanded");(n&&o||i)&&(this.closeActiveContentElements(),this.closeMenuDropdown());break;case"Home":case"End":this.handleTitleHomeOrEndKey(e);break;case"Enter":case" ":this.handleTitleActivationKey(e);break;case"Escape":this.handleTitleEscapeKey(e)}}handeTitleKeyUp(e){if(this.isDropdownLayout())return!0;const t="Tab"===e.key,n=!e.currentTarget.getAttribute("aria-expanded")||"false"===e.currentTarget?.getAttribute("aria-expanded");t&&n&&this.closeActiveContentElements()}isDropdownLayout(){const e=this.getSettings("selectors");return"dropdown"===this.$element.find(e.widgetInnerWrapper).attr("data-layout")}closeMenuDropdown(){this.isDropdownLayout()&&elementorFrontend.elements.$window.trigger("elementor/mega-menu/dropdown-toggle-by-keyboard",{widgetId:this.getID(),show:!1})}handleTitleHomeOrEndKey(e){e.preventDefault();const t=this.getTitleIndex(e.currentTarget)||1,n=this.elements.$focusableMenuElements.length,s=this.getTitleIndexFocusUpdated(e,t,n);this.setTitleFocus(s),e.stopPropagation()}handleTitleActivationKey(e){if(e.preventDefault(),this.handleTitleLinkEnterOrSpaceEvent(e))return;const t=this.getTitleIndex(e.currentTarget);elementorFrontend.elements.$window.trigger("elementor/nested-elements/activate-by-keyboard",{widgetId:this.getID(),titleIndex:t})}setTitleFocus(e){this.elements.$focusableMenuElements.filter(this.getTitleFilterSelector(e)).trigger("focus")}handleTitleLinkEnterOrSpaceEvent(e){const t="a"===e?.currentTarget?.tagName?.toLowerCase();return!elementorFrontend.isEditMode()&&t&&(e?.currentTarget?.click(),e.stopPropagation()),t}handleTitleEscapeKey(e){e.preventDefault(),e.stopPropagation(),this.isDropdownLayout()&&(elementorFrontend.elements.$window.trigger("elementor/mega-menu/dropdown-toggle-by-keyboard",{widgetId:this.getID()}),this.setFocusToMenuToggle()),elementorFrontend.elements.$window.trigger("elementor/nested-elements/activate-by-keyboard",{widgetId:this.getID()})}setFocusToMenuToggle(){const e=this.getSettings("selectors");this.$element.find(e.menuToggle).trigger("focus")}handleContentElementKeyboardNavigation(e){switch(e.key){case"Tab":e.shiftKey||this.handleContentElementTabEvents(e);break;case"Escape":e.preventDefault(),e.stopPropagation(),this.handleContentElementEscapeEvents(e)}}maybeRebindFocusableElements(){this.isEditorElementsChanged&&(this.elements.$focusableContainerElements.off(this.getContentElementEvents()),this.elements.$focusableContainerElements=this.getFocusableElements(this.elements.$itemContainers),this.elements.$focusableContainerElements.on(this.getContentElementEvents()),this.isEditorElementsChanged=!1)}handleContentElementTabEvents(e){const t=this.getSettings("selectors"),n=jQuery(e.currentTarget),s=t.itemContainer,i=n.closest(s),o=this.getFocusableElements(i).last();if(!n.is(o))return;this.isDropdownLayout()||this.closeActiveContentElements();const l=t.menuItem,r=0===i.closest(l).next(l).length;this.isDropdownLayout()&&r&&(this.closeActiveContentElements(),this.closeMenuDropdown())}handleContentElementEscapeEvents(){this.getActiveTitleElement().trigger("focus"),this.closeActiveContentElements()}closeActiveContentElements(){elementorFrontend.elements.$window.trigger("elementor/nested-elements/activate-by-keyboard",{widgetId:this.getID()})}linkContainer(e){const{container:t}=e.detail,n=t.model.get("id"),s=String(this.$element.data("id")),i=t.view.$el;n===s&&(this.updateIndexValues(i),this.updateListeners(i))}updateIndexValues(e){const{selectors:{directTabTitle:t,tabDropdown:n}}=this.getDefaultSettings(),s=e[0].querySelectorAll(t);let i=1;s.forEach((e=>{e.querySelector("a")&&e.querySelector("a").setAttribute("data-focus-index",i++),e.querySelector(n)&&e.querySelector(n).setAttribute("data-focus-index",i++)}))}updateListeners(e){this.elements.$focusableMenuElements.off();const{selectors:{focusableMenuElement:t,itemContainer:n}}=this.getSettings();this.elements.$focusableMenuElements=e.find(t),this.elements.$itemContainers=e.find(n),this.elements.$focusableMenuElements.on(this.getTitleEvents()),this.isEditorElementsChanged=!0}}t.default=MenuTitleKeyboardHandler}}]);