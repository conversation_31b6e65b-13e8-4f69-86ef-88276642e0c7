/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[635],{7193:(e,l)=>{Object.defineProperty(l,"__esModule",{value:!0}),l.default=void 0;class codeHighlightHandler extends elementorModules.frontend.handlers.Base{onInit(){super.onInit(...arguments),Prism.highlightAllUnder(this.$element[0],!1)}onElementChange(){Prism.highlightAllUnder(this.$element[0],!1)}}l.default=codeHighlightHandler}}]);