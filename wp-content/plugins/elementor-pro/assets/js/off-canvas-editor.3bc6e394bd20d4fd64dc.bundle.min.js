/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[367],{6078:(e,t,n)=>{var o=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function _default(){return a.default.createElement("div",{className:"elementor-first-add"},a.default.createElement("div",{className:"elementor-icon eicon-plus",onClick:()=>$e.route("panel/elements/categories")}))};var a=o(n(1594))},4582:(e,t,n)=>{var o=n(2470).__,a=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(4066));class Module extends elementorModules.editor.utils.Module{constructor(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];super(t),elementor.elementsManager.registerElementType(new r.default),elementor.listenTo(elementor.channels.editor,"all",this.populateOffCanvasDropdownOptions)}showOffCanvas(){const e={id:elementor.getPanelView().getCurrentPageView().getOption("editedElementView").getEditModel().get("id"),displayMode:"open"};elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor-pro/off-canvas/toggle-display-mode",{detail:e}))}populateOffCanvasDropdownOptions=(()=>{var e=this;return function(t){if(!e.isOffCanvasTagPopover(t))return;const n=arguments.length<=1?void 0:arguments[1],a=n.collection.findWhere({name:"off_canvas"});if(!a)return;const r=e.getOffCanvasWidgetsForCurrentDocument(),l={"":o("Select a widget","elementor-pro")};r.length||e.updateControl(a,l);for(const e of r){const t=e.dataset.id,n=e.querySelector(".e-off-canvas").getAttribute("aria-label");l[t]=n}e.updateControl(a,l);n.children.findByModel(a).render()}})();updateControl(e,t){e.set({options:t})}getOffCanvasWidgetsForCurrentDocument(){return elementor.$previewContents[0].querySelectorAll(`[data-elementor-id="${elementor.config.document.id}"] .elementor-widget-off-canvas.elementor-element-edit-mode`)}isOffCanvasTagPopover(e){return e.endsWith(":off-canvas:settings:activated")}hideAdvancedTab(e,t){if("off-canvas"!==(t?.model?.get("widgetType")||""))return;const n=t?.el.querySelector(".elementor-tab-control-advanced")||!1;n&&(n.style.display="none")}onInit(){elementor.channels.editor.on("editor:widget:off-canvas:section_layout:activated",this.showOffCanvas.bind(this)),elementor.channels.editor.on("section:activated",this.hideAdvancedTab.bind(this))}}t.default=Module},4066:(e,t,n)=>{var o=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.OffCanvas=void 0;var a=o(n(6078));class OffCanvas extends elementor.modules.elements.types.NestedElementBase{getType(){return"off-canvas"}getEmptyView(){return a.default}}t.OffCanvas=OffCanvas;t.default=OffCanvas}}]);