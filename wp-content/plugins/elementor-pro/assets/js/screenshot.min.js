/*! elementor-pro - v3.27.0 - 06-02-2025 */
(()=>{"use strict";class Screenshot extends elementorModules.ViewModule{getDefaultSettings(){return{empty_content_headline:"Empty Content.",crop:{width:1200,height:1500},excluded_external_css_urls:["https://kit-pro.fontawesome.com"],external_images_urls:["https://i.ytimg.com"],timeout:15e3,render_timeout:5e3,timerLabel:null,timer_label:`${ElementorScreenshotConfig.post_id} - timer`,image_placeholder:"data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACwAAAAAAQABAAACAkQBADs=",isDebug:elementorCommonConfig.isElementorDebug,isDebugSvg:!1,...ElementorScreenshotConfig}}getDefaultElements(){const e=jQuery(ElementorScreenshotConfig.selector),t=e.find(".elementor-section-wrap > .elementor-section, .elementor > .elementor-section");return{$elementor:e,$sections:t,$firstSection:t.first(),$notElementorElements:elementorCommon.elements.$body.find("> *:not(style, link)").not(e),$head:jQuery("head")}}onInit(){return super.onInit(),this.log("Screenshot init","time"),this.timeoutTimer=setTimeout(this.screenshotFailed.bind(this),this.getSettings("timeout")),this.captureScreenshot()}captureScreenshot(){return this.elements.$elementor.length||(elementorCommon.helpers.consoleWarn("Screenshots: The content of this page is empty, the module will create a fake conent just for this screenshot."),this.createFakeContent()),this.removeUnnecessaryElements(),this.handleIFrames(),this.removeFirstSectionMargin(),this.handleLinks(),this.loadExternalCss(),this.loadExternalImages(),Promise.resolve().then(this.createImage.bind(this)).then(this.createImageElement.bind(this)).then(this.cropCanvas.bind(this)).then(this.save.bind(this)).then(this.screenshotSucceed.bind(this)).catch(this.screenshotFailed.bind(this))}createFakeContent(){this.elements.$elementor=jQuery("<div>").css({height:this.getSettings("crop.height"),width:this.getSettings("crop.width"),display:"flex",alignItems:"center",justifyContent:"center"}),this.elements.$elementor.append(jQuery("<h1>").css({fontSize:"85px"}).html(this.getSettings("empty_content_headline"))),document.body.prepend(this.elements.$elementor)}loadExternalCss(){const e=[this.getSettings("home_url"),...this.getSettings("excluded_external_css_urls")].map((e=>`[href^="${e}"]`)).join(", ");jQuery("link").not(e).each(((e,t)=>{const s=jQuery(t),n=s.clone();n.attr("href",this.getScreenshotProxyUrl(s.attr("href"))),this.elements.$head.append(n),s.remove()}))}loadExternalImages(){const e=this.getSettings("external_images_urls").map((e=>`img[src^="${e}"]`)).join(", ");jQuery(e).each(((e,t)=>{const s=jQuery(t);s.attr("src",this.getScreenshotProxyUrl(s.attr("src")))}))}handleIFrames(){this.elements.$elementor.find("iframe").each(((e,t)=>{const s=jQuery(t),n=jQuery("<div />",{css:{background:"gray",width:s.width(),height:s.height()}});s.before(n),s.remove()}))}removeUnnecessaryElements(){let e=0;this.elements.$sections.filter(((t,s)=>{let n=!1;return e>=this.getSettings("crop.height")&&(n=!0),e+=jQuery(s).outerHeight(),n})).each(((e,t)=>{t.remove()})),this.elements.$notElementorElements.remove()}handleLinks(){elementorCommon.elements.$body.find("a").attr("href","/")}removeFirstSectionMargin(){this.elements.$firstSection.css({marginTop:0})}createImage(){const e=new Promise((e=>{window.addEventListener("load",(()=>{e()}))})),t=new Promise((e=>{setTimeout((()=>{e()}),this.getSettings("render_timeout"))}));return Promise.race([e,t]).then((()=>{if(this.log("Start creating screenshot."),this.getSettings("isDebugSvg"))return domtoimage.toSvg(document.body,{imagePlaceholder:this.getSettings("image_placeholder")}).then((e=>this.download(e))),Promise.reject("Debug SVG.");return/^((?!chrome|android).)*safari/i.test(window.userAgent)?(this.log('Creating screenshot with "html2canvas"'),html2canvas(document.body).then((e=>e.toDataURL("image/png")))):(this.log('Creating screenshot with "dom-to-image"'),domtoimage.toPng(document.body,{imagePlaceholder:this.getSettings("image_placeholder")}))}))}download(e){const t=jQuery("<a/>",{href:e,download:"debugSvg.svg",html:"Download SVG"});elementorCommon.elements.$body.append(t),t.trigger("click")}createImageElement(e){const t=new Image;return t.src=e,new Promise((e=>{t.onload=()=>e(t)}))}cropCanvas(e){const t=this.getSettings("crop.width"),s=this.getSettings("crop.height"),n=document.createElement("canvas"),i=n.getContext("2d"),o=t/e.width;return n.width=t,n.height=s>e.height?e.height:s,i.drawImage(e,0,0,e.width,e.height,0,0,e.width*o,e.height*o),Promise.resolve(n)}save(e){return new Promise(((t,s)=>{elementorCommon.ajax.addRequest("screenshot_save",{data:{post_id:this.getSettings("post_id"),screenshot:e.toDataURL("image/png")},success:e=>{this.log(`Screenshot created: ${encodeURI(e)}`),t(e)},error:()=>{this.log("Failed to create screenshot."),s()}})}))}markAsFailed(){return new Promise(((e,t)=>{elementorCommon.ajax.addRequest("screenshot_failed",{data:{post_id:this.getSettings("post_id")},success:()=>{this.log("Marked as failed."),e()},error:()=>{this.log("Failed to mark this screenshot as failed."),t()}})}))}getScreenshotProxyUrl(e){return`${this.getSettings("home_url")}?screenshot_proxy&nonce=${this.getSettings("nonce")}&href=${e}`}screenshotSucceed(e){this.screenshotDone(!0,e)}screenshotFailed(e){this.log(e,null),this.markAsFailed().then((()=>this.screenshotDone(!1)))}screenshotDone(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;clearTimeout(this.timeoutTimer),this.timeoutTimer=null,window.parent.postMessage({name:"capture-screenshot-done",success:e,id:this.getSettings("post_id"),imageUrl:t},"*"),this.log(`Screenshot ${e?"Succeed":"Failed"}.`,"timeEnd")}log(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"timeLog";this.getSettings("isDebug")&&(console.log("string"==typeof e?`${this.getSettings("post_id")} - ${e}`:e),t&&console[t](this.getSettings("timer_label")))}}jQuery((()=>{new Screenshot}))})();