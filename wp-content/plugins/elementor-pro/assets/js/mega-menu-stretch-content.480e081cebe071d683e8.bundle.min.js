/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[87],{8636:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class StretchedMenuItemContent extends elementorModules.frontend.handlers.StretchedElement{getStretchedClass(){return"elementor-widget-n-menu"}getStretchElementForConfig(){return this.$element.find(".e-n-menu-wrapper")}getStretchElementConfig(){const e=super.getStretchElementConfig();return e.cssOutput="variables",e}bindEvents(){super.bindEvents(),elementorFrontend.addListenerOnce(this.getUniqueHandlerID(),"elementor-pro/mega-menu/dropdown-open",this.stretch),elementorFrontend.elements.$window.on("elementor-pro/mega-menu/heading-mouse-event",this.stretch)}unbindEvents(){super.unbindEvents(),elementorFrontend.removeListeners(this.getUniqueHandlerID(),"elementor-pro/mega-menu/dropdown-open",this.stretch),elementorFrontend.elements.$window.off("elementor-pro/mega-menu/heading-mouse-event",this.stretch)}isStretchSettingEnabled(){return!0}isActive(){return!0}}t.default=StretchedMenuItemContent}}]);