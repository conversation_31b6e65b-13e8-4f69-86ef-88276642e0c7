/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[396],{7907:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(s(2195));t.default=i.default.extend({getSkinPrefix:()=>"cards_"})},2195:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=elementorModules.frontend.handlers.Base.extend({getSkinPrefix:()=>"classic_",bindEvents(){elementorFrontend.addListenerOnce(this.getModelCID(),"resize",this.onWindowResize)},unbindEvents(){elementorFrontend.removeListeners(this.getModelCID(),"resize",this.onWindowResize)},getClosureMethodsNames(){return elementorModules.frontend.handlers.Base.prototype.getClosureMethodsNames.apply(this,arguments).concat(["fitImages","onWindowResize","runMasonry"])},getDefaultSettings:()=>({classes:{fitHeight:"elementor-fit-height",hasItemRatio:"elementor-has-item-ratio"},selectors:{postsContainer:".elementor-posts-container",post:".elementor-post",postThumbnail:".elementor-post__thumbnail",postThumbnailImage:".elementor-post__thumbnail img"}}),getDefaultElements(){var e=this.getSettings("selectors");return{$postsContainer:this.$element.find(e.postsContainer),$posts:this.$element.find(e.post)}},fitImage(e){var t=this.getSettings(),s=e.find(t.selectors.postThumbnail),n=s.find("img")[0];if(n){var i=s.outerHeight()/s.outerWidth(),o=n.naturalHeight/n.naturalWidth;s.toggleClass(t.classes.fitHeight,o<i)}},fitImages(){var e=jQuery,t=this,s=getComputedStyle(this.$element[0],":after").content,n=this.getSettings();t.isMasonryEnabled()?this.elements.$postsContainer.removeClass(n.classes.hasItemRatio):(this.elements.$postsContainer.toggleClass(n.classes.hasItemRatio,!!s.match(/\d/)),this.elements.$posts.each((function(){var s=e(this),i=s.find(n.selectors.postThumbnailImage);t.fitImage(s),i.on("load",(function(){t.fitImage(s)}))})))},setColsCountSettings(){const e=this.getElementSettings(),t=this.getSkinPrefix(),s=elementorProFrontend.utils.controls.getResponsiveControlValue(e,`${t}columns`);this.setSettings("colsCount",s)},isMasonryEnabled(){return!!this.getElementSettings(this.getSkinPrefix()+"masonry")},initMasonry(){imagesLoaded(this.elements.$posts,this.runMasonry)},getVerticalSpaceBetween(){let e=elementorProFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),`${this.getSkinPrefix()}row_gap`,"size");return""===this.getSkinPrefix()&&""===e&&(e=this.getElementSettings("item_gap.size")),e},runMasonry(){var e=this.elements;e.$posts.css({marginTop:"",transitionDuration:""}),this.setColsCountSettings();var t=this.getSettings("colsCount"),s=this.isMasonryEnabled()&&t>=2;if(e.$postsContainer.toggleClass("elementor-posts-masonry",s),!s)return void e.$postsContainer.height("");const n=this.getVerticalSpaceBetween();new elementorModules.utils.Masonry({container:e.$postsContainer,items:e.$posts.filter(":visible"),columnsCount:this.getSettings("colsCount"),verticalSpaceBetween:n||0}).run()},run(){setTimeout(this.fitImages,0),this.initMasonry()},onInit(){elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),this.bindEvents(),this.run()},onWindowResize(){this.fitImages(),this.runMasonry()},onElementChange(){this.fitImages(),setTimeout(this.runMasonry)}})}}]);