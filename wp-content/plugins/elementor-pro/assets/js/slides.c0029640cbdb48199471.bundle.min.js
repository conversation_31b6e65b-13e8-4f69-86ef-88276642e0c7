/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[829],{3271:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class SlidesHandler extends elementorModules.frontend.handlers.SwiperBase{getDefaultSettings(){return{selectors:{slider:".elementor-slides-wrapper",slide:".swiper-slide",slideInnerContents:".swiper-slide-contents",activeSlide:".swiper-slide-active",activeDuplicate:".swiper-slide-duplicate-active"},classes:{animated:"animated",kenBurnsActive:"elementor-ken-burns--active",slideBackground:"swiper-slide-bg"},attributes:{dataSliderOptions:"slider_options",dataAnimation:"animation"}}}getDefaultElements(){const e=this.getSettings("selectors"),t={$swiperContainer:this.$element.find(e.slider)};return t.$slides=t.$swiperContainer.find(e.slide),t}getSwiperOptions(){const e=this.getElementSettings(),t={autoplay:this.getAutoplayConfig(),grabCursor:!0,initialSlide:this.getInitialSlide(),slidesPerView:1,slidesPerGroup:1,loop:"yes"===e.infinite,speed:e.transition_speed,effect:e.transition,observeParents:!0,observer:!0,handleElementorBreakpoints:!0,on:{slideChange:()=>{this.handleKenBurns()}}},i="arrows"===e.navigation||"both"===e.navigation,s="dots"===e.navigation||"both"===e.navigation;return i&&(t.navigation={prevEl:".elementor-swiper-button-prev",nextEl:".elementor-swiper-button-next"}),s&&(t.pagination={el:".swiper-pagination",type:"bullets",clickable:!0}),!0===t.loop&&(t.loopedSlides=this.getSlidesCount()),"fade"===t.effect&&(t.fadeEffect={crossFade:!0}),t}getAutoplayConfig(){const e=this.getElementSettings();return"yes"===e.autoplay&&{stopOnLastSlide:!0,delay:e.autoplay_speed,disableOnInteraction:"yes"===e.pause_on_interaction}}initSingleSlideAnimations(){const e=this.getSettings(),t=this.elements.$swiperContainer.data(e.attributes.dataAnimation);this.elements.$swiperContainer.find("."+e.classes.slideBackground).addClass(e.classes.kenBurnsActive),t&&this.elements.$swiperContainer.find(e.selectors.slideInnerContents).addClass(e.classes.animated+" "+t)}async initSlider(){const e=this.elements.$swiperContainer;if(!e.length)return;if(1>=this.getSlidesCount())return;const t=elementorFrontend.utils.swiper;this.swiper=await new t(e,this.getSwiperOptions()),e.data("swiper",this.swiper),this.handleKenBurns();this.getElementSettings().pause_on_hover&&this.togglePauseOnHover(!0);const i=this.getSettings(),s=e.data(i.attributes.dataAnimation);s&&(this.swiper.on("slideChangeTransitionStart",(function(){e.find(i.selectors.slideInnerContents).removeClass(i.classes.animated+" "+s).hide()})),this.swiper.on("slideChangeTransitionEnd",(function(){e.find(i.selectors.slideInnerContents).show().addClass(i.classes.animated+" "+s)})))}onInit(){elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),2>this.getSlidesCount()?this.initSingleSlideAnimations():this.initSlider()}getChangeableProperties(){return{pause_on_hover:"pauseOnHover",pause_on_interaction:"disableOnInteraction",autoplay_speed:"delay",transition_speed:"speed"}}updateSwiperOption(e){if(0===e.indexOf("width"))return void this.swiper.update();const t=this.getElementSettings(),i=t[e];let s=this.getChangeableProperties()[e],n=i;switch(e){case"autoplay_speed":s="autoplay",n={delay:i,disableOnInteraction:"yes"===t.pause_on_interaction};break;case"pause_on_hover":this.togglePauseOnHover("yes"===i);break;case"pause_on_interaction":n="yes"===i}"pause_on_hover"!==e&&(this.swiper.params[s]=n),this.swiper.update()}onElementChange(e){if(1>=this.getSlidesCount())return;const t=this.getChangeableProperties();Object.prototype.hasOwnProperty.call(t,e)&&(this.updateSwiperOption(e),this.swiper.autoplay.start())}onEditSettingsChange(e){1>=this.getSlidesCount()||"activeItemIndex"===e&&(this.swiper.slideToLoop(this.getEditSettings("activeItemIndex")-1),this.swiper.autoplay.stop())}}t.default=SlidesHandler}}]);