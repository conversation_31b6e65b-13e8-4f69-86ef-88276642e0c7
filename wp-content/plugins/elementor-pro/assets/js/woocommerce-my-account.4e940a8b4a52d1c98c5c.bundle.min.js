/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[662],{3046:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class Base extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{stickyRightColumn:".e-sticky-right-column"},classes:{stickyRightColumnActive:"e-sticky-right-column--active"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$stickyRightColumn:this.$element.find(e.stickyRightColumn)}}bindEvents(){elementorFrontend.elements.$document.on("select2:open",(e=>{this.addSelect2Wrapper(e)}))}addSelect2Wrapper(e){const t=jQuery(e.target).data("select2");t.$dropdown&&t.$dropdown.addClass("e-woo-select2-wrapper")}isStickyRightColumnActive(){const e=this.getSettings("classes");return this.elements.$stickyRightColumn.hasClass(e.stickyRightColumnActive)}activateStickyRightColumn(){const e=this.getElementSettings(),t=elementorFrontend.elements.$wpAdminBar,s=this.getSettings("classes");let n=e.sticky_right_column_offset||0;t.length&&"fixed"===t.css("position")&&(n+=t.height()),"yes"===this.getElementSettings("sticky_right_column")&&(this.elements.$stickyRightColumn.addClass(s.stickyRightColumnActive),this.elements.$stickyRightColumn.css("top",n+"px"))}deactivateStickyRightColumn(){if(!this.isStickyRightColumnActive())return;const e=this.getSettings("classes");this.elements.$stickyRightColumn.removeClass(e.stickyRightColumnActive)}toggleStickyRightColumn(){this.getElementSettings("sticky_right_column")?this.isStickyRightColumnActive()||this.activateStickyRightColumn():this.deactivateStickyRightColumn()}equalizeElementHeight(e){if(e.length){e.removeAttr("style");let t=0;e.each(((e,s)=>{t=Math.max(t,s.offsetHeight)})),0<t&&e.css({height:t+"px"})}}removePaddingBetweenPurchaseNote(e){e&&e.each(((e,t)=>{jQuery(t).prev().children("td").addClass("product-purchase-note-is-below")}))}updateWpReferers(){const e=this.getSettings("selectors"),t=this.$element.find(e.wpHttpRefererInputs),s=new URL(document.location);s.searchParams.set("elementorPageId",elementorFrontend.config.post.id),s.searchParams.set("elementorWidgetId",this.getID()),t.attr("value",s)}}t.default=Base},1627:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(s(3046));class MyAccountHandler extends i.default{getDefaultSettings(){return{selectors:{address:"address",tabLinks:".woocommerce-MyAccount-navigation-link a",viewOrderButtons:".my_account_orders .woocommerce-button.view",viewOrderLinks:".woocommerce-orders-table__cell-order-number a",authForms:"form.login, form.register",tabWrapper:".e-my-account-tab",tabItem:".woocommerce-MyAccount-navigation li",allPageElements:"[e-my-account-page]",purchasenote:"tr.product-purchase-note",contentWrapper:".woocommerce-MyAccount-content-wrapper"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$address:this.$element.find(e.address),$tabLinks:this.$element.find(e.tabLinks),$viewOrderButtons:this.$element.find(e.viewOrderButtons),$viewOrderLinks:this.$element.find(e.viewOrderLinks),$authForms:this.$element.find(e.authForms),$tabWrapper:this.$element.find(e.tabWrapper),$tabItem:this.$element.find(e.tabItem),$allPageElements:this.$element.find(e.allPageElements),$purchasenote:this.$element.find(e.purchasenote),$contentWrapper:this.$element.find(e.contentWrapper)}}editorInitTabs(){this.elements.$allPageElements.each(((e,t)=>{const s=t.getAttribute("e-my-account-page");let n;if("view-order"===s)n=this.elements.$viewOrderLinks.add(this.elements.$viewOrderButtons);else n=this.$element.find(".woocommerce-MyAccount-navigation-link--"+s);n.on("click",(()=>{this.currentPage=s,this.editorShowTab()}))}))}editorShowTab(){const e=this.$element.find('[e-my-account-page="'+this.currentPage+'"]');this.$element.attr("e-my-account-page",this.currentPage),this.elements.$allPageElements.hide(),e.show(),this.toggleEndpointClasses(),"view-order"!==this.currentPage&&(this.elements.$tabItem.removeClass("is-active"),this.$element.find(".woocommerce-MyAccount-navigation-link--"+this.currentPage).addClass("is-active")),"edit-address"!==this.currentPage&&"view-order"!==this.currentPage||this.equalizeElementHeights()}toggleEndpointClasses(){const e=["dashboard","orders","view-order","downloads","edit-account","edit-address","payment-methods"];let t="";this.elements.$tabWrapper.removeClass("e-my-account-tab__"+e.join(" e-my-account-tab__")+" e-my-account-tab__dashboard--custom"),"dashboard"===this.currentPage&&this.elements.$contentWrapper.find(".elementor").length&&(t=" e-my-account-tab__dashboard--custom"),e.includes(this.currentPage)&&this.elements.$tabWrapper.addClass("e-my-account-tab__"+this.currentPage+t)}applyButtonsHoverAnimation(){const e=this.getElementSettings();e.forms_buttons_hover_animation&&this.$element.find(".woocommerce button.button,  #add_payment_method #payment #place_order").addClass("elementor-animation-"+e.forms_buttons_hover_animation),e.tables_button_hover_animation&&this.$element.find(".order-again .button, td .button, .woocommerce-pagination .button").addClass("elementor-animation-"+e.tables_button_hover_animation)}equalizeElementHeights(){this.equalizeElementHeight(this.elements.$address),this.isEdit||this.equalizeElementHeight(this.elements.$authForms)}onElementChange(e){0!==e.indexOf("general_text_typography")&&0!==e.indexOf("sections_padding")||this.equalizeElementHeights(),0===e.indexOf("forms_rows_gap")&&this.removePaddingBetweenPurchaseNote(this.elements.$purchasenote),"customize_dashboard_select"===e&&elementorPro.modules.woocommerce.onTemplateIdChange("customize_dashboard_select")}bindEvents(){super.bindEvents(),elementorFrontend.elements.$body.on("keyup change",".register #reg_password",(()=>{this.equalizeElementHeights()}))}onInit(){super.onInit(...arguments),this.isEdit&&(this.editorInitTabs(),this.$element.attr("e-my-account-page")?this.currentPage=this.$element.attr("e-my-account-page"):this.currentPage="dashboard",this.editorShowTab()),this.applyButtonsHoverAnimation(),this.equalizeElementHeights(),this.removePaddingBetweenPurchaseNote(this.elements.$purchasenote)}}t.default=MyAccountHandler}}]);