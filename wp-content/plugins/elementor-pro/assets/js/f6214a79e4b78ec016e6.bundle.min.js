/*! elementor-pro - v3.27.0 - 06-02-2025 */
/*! For license information please see f6214a79e4b78ec016e6.bundle.min.js.LICENSE.txt */
(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[234],{1234:function(e){e.exports=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:a,seal:i,create:l}=Object,{apply:c,construct:s}="undefined"!=typeof Reflect&&Reflect;a||(a=function freeze(e){return e}),i||(i=function seal(e){return e}),c||(c=function apply(e,t,n){return e.apply(t,n)}),s||(s=function construct(e,t){return new e(...t)});const u=unapply(Array.prototype.forEach),m=unapply(Array.prototype.pop),p=unapply(Array.prototype.push),d=unapply(String.prototype.toLowerCase),f=unapply(String.prototype.toString),h=unapply(String.prototype.match),g=unapply(String.prototype.replace),T=unapply(String.prototype.indexOf),y=unapply(String.prototype.trim),_=unapply(Object.prototype.hasOwnProperty),E=unapply(RegExp.prototype.test),S=unconstruct(TypeError);function unapply(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return c(e,t,o)}}function unconstruct(e){return function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return s(e,n)}}function addToSet(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d;t&&t(e,null);let a=o.length;for(;a--;){let t=o[a];if("string"==typeof t){const e=r(t);e!==t&&(n(o)||(o[a]=e),t=e)}e[t]=!0}return e}function cleanArray(e){for(let t=0;t<e.length;t++)_(e,t)||(e[t]=null);return e}function clone(t){const n=l(null);for(const[o,r]of e(t))_(t,o)&&(Array.isArray(r)?n[o]=cleanArray(r):r&&"object"==typeof r&&r.constructor===Object?n[o]=clone(r):n[o]=r);return n}function lookupGetter(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return unapply(n.get);if("function"==typeof n.value)return unapply(n.value)}e=o(e)}function fallbackValue(){return null}return fallbackValue}const A=a(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),b=a(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),N=a(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),R=a(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),w=a(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),k=a(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),C=a(["#text"]),D=a(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),O=a(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),L=a(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),v=a(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),x=i(/\{\{[\w\W]*|[\w\W]*\}\}/gm),M=i(/<%[\w\W]*|[\w\W]*%>/gm),I=i(/\${[\w\W]*}/gm),U=i(/^data-[\-\w.\u00B7-\uFFFF]/),P=i(/^aria-[\-\w]+$/),F=i(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),H=i(/^(?:\w+script|data):/i),z=i(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),G=i(/^html$/i),B=i(/^[a-z][.\w]*(-[.\w]+)+$/i);var W=Object.freeze({__proto__:null,MUSTACHE_EXPR:x,ERB_EXPR:M,TMPLIT_EXPR:I,DATA_ATTR:U,ARIA_ATTR:P,IS_ALLOWED_URI:F,IS_SCRIPT_OR_DATA:H,ATTR_WHITESPACE:z,DOCTYPE_NAME:G,CUSTOM_ELEMENT:B});const Y={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},j=function getGlobal(){return"undefined"==typeof window?null:window},X=function _createTrustedTypesPolicy(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}};function createDOMPurify(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:j();const DOMPurify=e=>createDOMPurify(e);if(DOMPurify.version="3.1.7",DOMPurify.removed=[],!t||!t.document||t.document.nodeType!==Y.document)return DOMPurify.isSupported=!1,DOMPurify;let{document:n}=t;const o=n,r=o.currentScript,{DocumentFragment:i,HTMLTemplateElement:c,Node:s,Element:x,NodeFilter:M,NamedNodeMap:I=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:U,DOMParser:P,trustedTypes:H}=t,z=x.prototype,B=lookupGetter(z,"cloneNode"),q=lookupGetter(z,"remove"),V=lookupGetter(z,"nextSibling"),$=lookupGetter(z,"childNodes"),K=lookupGetter(z,"parentNode");if("function"==typeof c){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let Z,J="";const{implementation:Q,createNodeIterator:ee,createDocumentFragment:te,getElementsByTagName:ne}=n,{importNode:oe}=o;let re={};DOMPurify.isSupported="function"==typeof e&&"function"==typeof K&&Q&&void 0!==Q.createHTMLDocument;const{MUSTACHE_EXPR:ae,ERB_EXPR:ie,TMPLIT_EXPR:le,DATA_ATTR:ce,ARIA_ATTR:se,IS_SCRIPT_OR_DATA:ue,ATTR_WHITESPACE:me,CUSTOM_ELEMENT:pe}=W;let{IS_ALLOWED_URI:de}=W,fe=null;const he=addToSet({},[...A,...b,...N,...w,...C]);let ge=null;const Te=addToSet({},[...D,...O,...L,...v]);let ye=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),_e=null,Ee=null,Se=!0,Ae=!0,be=!1,Ne=!0,Re=!1,we=!0,ke=!1,Ce=!1,De=!1,Oe=!1,Le=!1,ve=!1,xe=!0,Me=!1;const Ie="user-content-";let Ue=!0,Pe=!1,Fe={},He=null;const ze=addToSet({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ge=null;const Be=addToSet({},["audio","video","img","source","image","track"]);let We=null;const Ye=addToSet({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),je="http://www.w3.org/1998/Math/MathML",Xe="http://www.w3.org/2000/svg",qe="http://www.w3.org/1999/xhtml";let Ve=qe,$e=!1,Ke=null;const Ze=addToSet({},[je,Xe,qe],f);let Je=null;const Qe=["application/xhtml+xml","text/html"],et="text/html";let tt=null,nt=null;const ot=n.createElement("form"),rt=function isRegexOrFunction(e){return e instanceof RegExp||e instanceof Function},at=function _parseConfig(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!nt||nt!==e){if(e&&"object"==typeof e||(e={}),e=clone(e),Je=-1===Qe.indexOf(e.PARSER_MEDIA_TYPE)?et:e.PARSER_MEDIA_TYPE,tt="application/xhtml+xml"===Je?f:d,fe=_(e,"ALLOWED_TAGS")?addToSet({},e.ALLOWED_TAGS,tt):he,ge=_(e,"ALLOWED_ATTR")?addToSet({},e.ALLOWED_ATTR,tt):Te,Ke=_(e,"ALLOWED_NAMESPACES")?addToSet({},e.ALLOWED_NAMESPACES,f):Ze,We=_(e,"ADD_URI_SAFE_ATTR")?addToSet(clone(Ye),e.ADD_URI_SAFE_ATTR,tt):Ye,Ge=_(e,"ADD_DATA_URI_TAGS")?addToSet(clone(Be),e.ADD_DATA_URI_TAGS,tt):Be,He=_(e,"FORBID_CONTENTS")?addToSet({},e.FORBID_CONTENTS,tt):ze,_e=_(e,"FORBID_TAGS")?addToSet({},e.FORBID_TAGS,tt):{},Ee=_(e,"FORBID_ATTR")?addToSet({},e.FORBID_ATTR,tt):{},Fe=!!_(e,"USE_PROFILES")&&e.USE_PROFILES,Se=!1!==e.ALLOW_ARIA_ATTR,Ae=!1!==e.ALLOW_DATA_ATTR,be=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ne=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Re=e.SAFE_FOR_TEMPLATES||!1,we=!1!==e.SAFE_FOR_XML,ke=e.WHOLE_DOCUMENT||!1,Oe=e.RETURN_DOM||!1,Le=e.RETURN_DOM_FRAGMENT||!1,ve=e.RETURN_TRUSTED_TYPE||!1,De=e.FORCE_BODY||!1,xe=!1!==e.SANITIZE_DOM,Me=e.SANITIZE_NAMED_PROPS||!1,Ue=!1!==e.KEEP_CONTENT,Pe=e.IN_PLACE||!1,de=e.ALLOWED_URI_REGEXP||F,Ve=e.NAMESPACE||qe,ye=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&rt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ye.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&rt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ye.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(ye.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Re&&(Ae=!1),Le&&(Oe=!0),Fe&&(fe=addToSet({},C),ge=[],!0===Fe.html&&(addToSet(fe,A),addToSet(ge,D)),!0===Fe.svg&&(addToSet(fe,b),addToSet(ge,O),addToSet(ge,v)),!0===Fe.svgFilters&&(addToSet(fe,N),addToSet(ge,O),addToSet(ge,v)),!0===Fe.mathMl&&(addToSet(fe,w),addToSet(ge,L),addToSet(ge,v))),e.ADD_TAGS&&(fe===he&&(fe=clone(fe)),addToSet(fe,e.ADD_TAGS,tt)),e.ADD_ATTR&&(ge===Te&&(ge=clone(ge)),addToSet(ge,e.ADD_ATTR,tt)),e.ADD_URI_SAFE_ATTR&&addToSet(We,e.ADD_URI_SAFE_ATTR,tt),e.FORBID_CONTENTS&&(He===ze&&(He=clone(He)),addToSet(He,e.FORBID_CONTENTS,tt)),Ue&&(fe["#text"]=!0),ke&&addToSet(fe,["html","head","body"]),fe.table&&(addToSet(fe,["tbody"]),delete _e.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw S('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw S('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');Z=e.TRUSTED_TYPES_POLICY,J=Z.createHTML("")}else void 0===Z&&(Z=X(H,r)),null!==Z&&"string"==typeof J&&(J=Z.createHTML(""));a&&a(e),nt=e}},it=addToSet({},["mi","mo","mn","ms","mtext"]),lt=addToSet({},["annotation-xml"]),ct=addToSet({},["title","style","font","a","script"]),st=addToSet({},[...b,...N,...R]),ut=addToSet({},[...w,...k]),mt=function _checkValidNamespace(e){let t=K(e);t&&t.tagName||(t={namespaceURI:Ve,tagName:"template"});const n=d(e.tagName),o=d(t.tagName);return!!Ke[e.namespaceURI]&&(e.namespaceURI===Xe?t.namespaceURI===qe?"svg"===n:t.namespaceURI===je?"svg"===n&&("annotation-xml"===o||it[o]):Boolean(st[n]):e.namespaceURI===je?t.namespaceURI===qe?"math"===n:t.namespaceURI===Xe?"math"===n&&lt[o]:Boolean(ut[n]):e.namespaceURI===qe?!(t.namespaceURI===Xe&&!lt[o])&&!(t.namespaceURI===je&&!it[o])&&!ut[n]&&(ct[n]||!st[n]):!("application/xhtml+xml"!==Je||!Ke[e.namespaceURI]))},pt=function _forceRemove(e){p(DOMPurify.removed,{element:e});try{K(e).removeChild(e)}catch(t){q(e)}},dt=function _removeAttribute(e,t){try{p(DOMPurify.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){p(DOMPurify.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!ge[e])if(Oe||Le)try{pt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},ft=function _initDocument(e){let t=null,o=null;if(De)e="<remove></remove>"+e;else{const t=h(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===Je&&Ve===qe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=Z?Z.createHTML(e):e;if(Ve===qe)try{t=(new P).parseFromString(r,Je)}catch(e){}if(!t||!t.documentElement){t=Q.createDocument(Ve,"template",null);try{t.documentElement.innerHTML=$e?J:r}catch(e){}}const a=t.body||t.documentElement;return e&&o&&a.insertBefore(n.createTextNode(o),a.childNodes[0]||null),Ve===qe?ne.call(t,ke?"html":"body")[0]:ke?t.documentElement:a},ht=function _createNodeIterator(e){return ee.call(e.ownerDocument||e,e,M.SHOW_ELEMENT|M.SHOW_COMMENT|M.SHOW_TEXT|M.SHOW_PROCESSING_INSTRUCTION|M.SHOW_CDATA_SECTION,null)},gt=function _isClobbered(e){return e instanceof U&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof I)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Tt=function _isNode(e){return"function"==typeof s&&e instanceof s},yt=function _executeHook(e,t,n){re[e]&&u(re[e],(e=>{e.call(DOMPurify,t,n,nt)}))},_t=function _sanitizeElements(e){let t=null;if(yt("beforeSanitizeElements",e,null),gt(e))return pt(e),!0;const n=tt(e.nodeName);if(yt("uponSanitizeElement",e,{tagName:n,allowedTags:fe}),e.hasChildNodes()&&!Tt(e.firstElementChild)&&E(/<[/\w]/g,e.innerHTML)&&E(/<[/\w]/g,e.textContent))return pt(e),!0;if(e.nodeType===Y.progressingInstruction)return pt(e),!0;if(we&&e.nodeType===Y.comment&&E(/<[/\w]/g,e.data))return pt(e),!0;if(!fe[n]||_e[n]){if(!_e[n]&&St(n)){if(ye.tagNameCheck instanceof RegExp&&E(ye.tagNameCheck,n))return!1;if(ye.tagNameCheck instanceof Function&&ye.tagNameCheck(n))return!1}if(Ue&&!He[n]){const t=K(e)||e.parentNode,n=$(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=B(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,V(e))}}return pt(e),!0}return e instanceof x&&!mt(e)?(pt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!E(/<\/no(script|embed|frames)/i,e.innerHTML)?(Re&&e.nodeType===Y.text&&(t=e.textContent,u([ae,ie,le],(e=>{t=g(t,e," ")})),e.textContent!==t&&(p(DOMPurify.removed,{element:e.cloneNode()}),e.textContent=t)),yt("afterSanitizeElements",e,null),!1):(pt(e),!0)},Et=function _isValidAttribute(e,t,o){if(xe&&("id"===t||"name"===t)&&(o in n||o in ot))return!1;if(Ae&&!Ee[t]&&E(ce,t));else if(Se&&E(se,t));else if(!ge[t]||Ee[t]){if(!(St(e)&&(ye.tagNameCheck instanceof RegExp&&E(ye.tagNameCheck,e)||ye.tagNameCheck instanceof Function&&ye.tagNameCheck(e))&&(ye.attributeNameCheck instanceof RegExp&&E(ye.attributeNameCheck,t)||ye.attributeNameCheck instanceof Function&&ye.attributeNameCheck(t))||"is"===t&&ye.allowCustomizedBuiltInElements&&(ye.tagNameCheck instanceof RegExp&&E(ye.tagNameCheck,o)||ye.tagNameCheck instanceof Function&&ye.tagNameCheck(o))))return!1}else if(We[t]);else if(E(de,g(o,me,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==T(o,"data:")||!Ge[e])if(be&&!E(ue,g(o,me,"")));else if(o)return!1;return!0},St=function _isBasicCustomElement(e){return"annotation-xml"!==e&&h(e,pe)},At=function _sanitizeAttributes(e){yt("beforeSanitizeAttributes",e,null);const{attributes:t}=e;if(!t)return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ge};let o=t.length;for(;o--;){const r=t[o],{name:a,namespaceURI:i,value:l}=r,c=tt(a);let s="value"===a?l:y(l);if(n.attrName=c,n.attrValue=s,n.keepAttr=!0,n.forceKeepAttr=void 0,yt("uponSanitizeAttribute",e,n),s=n.attrValue,n.forceKeepAttr)continue;if(dt(a,e),!n.keepAttr)continue;if(!Ne&&E(/\/>/i,s)){dt(a,e);continue}Re&&u([ae,ie,le],(e=>{s=g(s,e," ")}));const p=tt(e.nodeName);if(Et(p,c,s))if(!Me||"id"!==c&&"name"!==c||(dt(a,e),s=Ie+s),we&&E(/((--!?|])>)|<\/(style|title)/i,s))dt(a,e);else{if(Z&&"object"==typeof H&&"function"==typeof H.getAttributeType)if(i);else switch(H.getAttributeType(p,c)){case"TrustedHTML":s=Z.createHTML(s);break;case"TrustedScriptURL":s=Z.createScriptURL(s)}try{i?e.setAttributeNS(i,a,s):e.setAttribute(a,s),gt(e)?pt(e):m(DOMPurify.removed)}catch(e){}}}yt("afterSanitizeAttributes",e,null)},bt=function _sanitizeShadowDOM(e){let t=null;const n=ht(e);for(yt("beforeSanitizeShadowDOM",e,null);t=n.nextNode();)yt("uponSanitizeShadowNode",t,null),_t(t)||(t.content instanceof i&&_sanitizeShadowDOM(t.content),At(t));yt("afterSanitizeShadowDOM",e,null)};return DOMPurify.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,a=null,l=null;if($e=!e,$e&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Tt(e)){if("function"!=typeof e.toString)throw S("toString is not a function");if("string"!=typeof(e=e.toString()))throw S("dirty is not a string, aborting")}if(!DOMPurify.isSupported)return e;if(Ce||at(t),DOMPurify.removed=[],"string"==typeof e&&(Pe=!1),Pe){if(e.nodeName){const t=tt(e.nodeName);if(!fe[t]||_e[t])throw S("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)n=ft("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),r.nodeType===Y.element&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!Oe&&!Re&&!ke&&-1===e.indexOf("<"))return Z&&ve?Z.createHTML(e):e;if(n=ft(e),!n)return Oe?null:ve?J:""}n&&De&&pt(n.firstChild);const c=ht(Pe?e:n);for(;a=c.nextNode();)_t(a)||(a.content instanceof i&&bt(a.content),At(a));if(Pe)return e;if(Oe){if(Le)for(l=te.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(ge.shadowroot||ge.shadowrootmode)&&(l=oe.call(o,l,!0)),l}let m=ke?n.outerHTML:n.innerHTML;return ke&&fe["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&E(G,n.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+m),Re&&u([ae,ie,le],(e=>{m=g(m,e," ")})),Z&&ve?Z.createHTML(m):m},DOMPurify.setConfig=function(){at(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Ce=!0},DOMPurify.clearConfig=function(){nt=null,Ce=!1},DOMPurify.isValidAttribute=function(e,t,n){nt||at({});const o=tt(e),r=tt(t);return Et(o,r,n)},DOMPurify.addHook=function(e,t){"function"==typeof t&&(re[e]=re[e]||[],p(re[e],t))},DOMPurify.removeHook=function(e){if(re[e])return m(re[e])},DOMPurify.removeHooks=function(e){re[e]&&(re[e]=[])},DOMPurify.removeAllHooks=function(){re={}},DOMPurify}return createDOMPurify()}()}}]);