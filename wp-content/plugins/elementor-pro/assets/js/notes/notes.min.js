/*! For license information please see notes.min.js.LICENSE.txt */
(()=>{"use strict";var e={2396:(e,t,r)=>{var o=r(4686),n={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function c(e){return o.isMemo(e)?a:i[e.$$typeof]||n}i[o.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[o.Memo]=a;var d=Object.defineProperty,l=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(t,r,o){if("string"!=typeof r){if(f){var n=m(r);n&&n!==f&&e(t,n,o)}var a=l(r);u&&(a=a.concat(u(r)));for(var i=c(t),y=c(r),h=0;h<a.length;++h){var g=a[h];if(!(s[g]||o&&o[g]||y&&y[g]||i&&i[g])){var w=p(r,g);try{d(t,g,w)}catch(e){}}}}return t}},946:(e,t)=>{var r="function"==typeof Symbol&&Symbol.for,o=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,s=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,i=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,d=r?Symbol.for("react.context"):60110,l=r?Symbol.for("react.async_mode"):60111,u=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,m=r?Symbol.for("react.suspense"):60113,f=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,h=r?Symbol.for("react.lazy"):60116,g=r?Symbol.for("react.block"):60121,w=r?Symbol.for("react.fundamental"):60117,v=r?Symbol.for("react.responder"):60118,_=r?Symbol.for("react.scope"):60119;function b(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:switch(e=e.type){case l:case u:case s:case i:case a:case m:return e;default:switch(e=e&&e.$$typeof){case d:case p:case h:case y:case c:return e;default:return t}}case n:return t}}}function $(e){return b(e)===u}t.AsyncMode=l,t.ConcurrentMode=u,t.ContextConsumer=d,t.ContextProvider=c,t.Element=o,t.ForwardRef=p,t.Fragment=s,t.Lazy=h,t.Memo=y,t.Portal=n,t.Profiler=i,t.StrictMode=a,t.Suspense=m,t.isAsyncMode=function(e){return $(e)||b(e)===l},t.isConcurrentMode=$,t.isContextConsumer=function(e){return b(e)===d},t.isContextProvider=function(e){return b(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===o},t.isForwardRef=function(e){return b(e)===p},t.isFragment=function(e){return b(e)===s},t.isLazy=function(e){return b(e)===h},t.isMemo=function(e){return b(e)===y},t.isPortal=function(e){return b(e)===n},t.isProfiler=function(e){return b(e)===i},t.isStrictMode=function(e){return b(e)===a},t.isSuspense=function(e){return b(e)===m},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===u||e===i||e===a||e===m||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===y||e.$$typeof===c||e.$$typeof===d||e.$$typeof===p||e.$$typeof===w||e.$$typeof===v||e.$$typeof===_||e.$$typeof===g)},t.typeOf=b},4686:(e,t,r)=>{e.exports=r(946)},291:(e,t)=>{var r=60103,o=60106,n=60107,s=60108,a=60114,i=60109,c=60110,d=60112,l=60113,u=60120,p=60115,m=60116,f=60121,y=60122,h=60117,g=60129,w=60131;if("function"==typeof Symbol&&Symbol.for){var v=Symbol.for;r=v("react.element"),o=v("react.portal"),n=v("react.fragment"),s=v("react.strict_mode"),a=v("react.profiler"),i=v("react.provider"),c=v("react.context"),d=v("react.forward_ref"),l=v("react.suspense"),u=v("react.suspense_list"),p=v("react.memo"),m=v("react.lazy"),f=v("react.block"),y=v("react.server.block"),h=v("react.fundamental"),g=v("react.debug_trace_mode"),w=v("react.legacy_hidden")}function _(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case n:case a:case s:case l:case u:return e;default:switch(e=e&&e.$$typeof){case c:case d:case m:case p:case i:return e;default:return t}}case o:return t}}}},9479:(e,t,r)=>{r(291)},2470:e=>{e.exports=wp.i18n}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,r),s.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};r.r(o),r.d(o,{ClearActive:()=>a,Close:()=>i,CopyLink:()=>f,Filter:()=>y,Open:()=>b,SetActive:()=>$,Toggle:()=>S});var n={};r.r(n),r.d(n,{Index:()=>A,ReadStatus:()=>E,Summary:()=>x,Users:()=>C});var s={};r.r(s),r.d(s,{NotesAddPanelMenuItem:()=>M});class a extends $e.modules.CommandBase{apply({id:e=null}={}){const{store:t}=window.top.$e,{actions:r}=t.get("notes");t.dispatch(r.clearActive(e))}}class i extends $e.modules.CommandBase{apply(){this.component.close()}}class c{init(e={}){return Object.entries(e).forEach((([e,t])=>{this[e]=t})),this}}var d=r(2470).__;class l extends c{id=null;name="";slug="";avatarUrls={24:null,48:null,96:null};capabilities={};static createFromResponse(e){return(new l).init({id:e.id,name:e.name,slug:e.slug,avatarUrls:e.avatar_urls,capabilities:{notes:{read:e.capabilities?.notes?.can_read},post:{edit:e.capabilities?.post?.can_edit}}})}static createDeleted(e=""){const{avatar_defaults:t}=window.top.$e.components.get("notes").config.urls;return(new l).init({name:[e,d("(deleted user)","elementor-pro")].join(" "),avatarUrls:t})}}class u extends c{id;type;typeTitle;static createFromResponse(e){return(new u).init({id:e.id,type:e.type,typeTitle:e.type_title})}}class p extends c{id=null;parentId=0;elementId=null;content="";position={x:0,y:0};repliesCount=0;unreadRepliesCount=0;replies=[];author=null;readers=[];isRead=!1;isResolved=!1;routeUrl="";routeTitle="";userCan={};createdAt=null;updatedAt=null;lastActivityAt=null;_formattedLastActivityAt="";_formattedCreatedAt="";static createFromResponse(e){return(new p).init({id:e.id,parentId:e.parent_id,elementId:e.element_id,content:e.content,position:e.position,repliesCount:e.replies_count,unreadRepliesCount:e.unread_replies_count,replies:e.replies.map((e=>p.createFromResponse(e))),author:e.author?l.createFromResponse(e.author):l.createDeleted(e.author_display_name),document:e.document?u.createFromResponse(e.document):null,readers:e.readers?e.readers.map((e=>l.createFromResponse(e))):[],isRead:e.is_read,isResolved:e.is_resolved,routeUrl:e.route_url,routeTitle:e.route_title,userCan:e.user_can,createdAt:new Date(e.created_at),updatedAt:new Date(e.updated_at),lastActivityAt:new Date(e.last_activity_at)})}getFormattedLastActivityAt(){return this._formattedLastActivityAt||(this._formattedLastActivityAt=this.lastActivityAt.toLocaleString()),this._formattedLastActivityAt}getFormattedCreatedAt(){return this._formattedCreatedAt||(this._formattedCreatedAt=this.createdAt.toLocaleString()),this._formattedCreatedAt}getURL(){const e=this.isReply()?this.parentId:this.id;return this.constructor.getURL(e)}static getURL(e){const{route:t}=window.top.$e.components.get("notes").config;return t.note_url_pattern.replace("{{NOTE_ID}}",e)}isUnreadThread(){return this.isThread()&&(!this.isRead||this.unreadRepliesCount>0)}isThread(){return 0===this.parentId}isReply(){return!this.isThread()}}function m(e){if(!navigator?.clipboard)throw new Error("Cannot copy to clipboard, please make sure you are using SSL in your website.");navigator.clipboard.writeText(e)}class f extends $e.modules.CommandBase{validateArgs(e={}){this.requireArgumentType("id","number",e)}apply(e){return m(p.getURL(e.id))}}class y extends $e.modules.CommandBase{validateArgs(e={}){this.requireArgument("filters",e)}apply(e){const{store:t}=window.top.$e,{actions:r}=t.get("notes"),o=e.overwrite?r.setFilters:r.modifyFilters;t.dispatch(o(e.filters))}}const h=React;"undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?h.useLayoutEffect:h.useEffect;r(2396),r(9479);const g=ReactDOM;var w;w=g.unstable_batchedUpdates;const v="thread";class b extends $e.modules.CommandBase{static getInfo(){return{isSafe:!0,isSafeWithArgs:!0}}apply(e){window.top.$e.route(this.component.getNamespace());const t=parseInt(e.id||"0");t>0&&window.top.$e.run("notes/set-active",{type:v,data:{noteId:t}})}}class $ extends $e.modules.CommandBase{validateArgs(e={}){this.requireArgument("type",e),this.requireArgument("data",e)}apply({type:e,data:t}){const{store:r}=window.top.$e;if(r.getState().notes.formsInWritingMode.length>0)return;const{actions:o}=r.get("notes");r.dispatch(o.setActive({type:e,data:t}))}}class S extends $e.modules.CommandBase{apply(){this.component.isOpen?window.top.$e.run("notes/close"):window.top.$e.run("notes/open")}}class E extends $e.modules.CommandData{static getEndpointFormat(){return"notes/read-status"}}class x extends $e.modules.CommandData{static getEndpointFormat(){return"notes/summary"}}class C extends $e.modules.CommandData{static getEndpointFormat(){return"notes/users"}}class A extends $e.modules.CommandData{static getEndpointFormat(){return"notes/{id}"}}var O=r(2470).__;class M extends $e.modules.hookUI.After{getCommand(){return"panel/state-ready"}getId(){return"notes-add-panel-menu-item"}apply(){elementor.modules.layouts.panel.pages.menu.Menu.addItem({name:"notes",icon:"eicon-commenting-o",title:O("Notes","elementor-pro"),callback:()=>window.top.$e.run("notes/open")},"navigate_from_page","finder")}}var F=r(2470).__;const I=class{constructor(){["widget","section","column","container"].forEach((e=>{elementor.hooks.addFilter(`elements/${e}/contextMenuGroups`,this.notesContextMenuAddGroup)}))}notesContextMenuAddGroup(e){const t=_.findWhere(e,{name:"notes"}),r=e.indexOf(t),o={name:"open_notes",title:F("Notes","elementor-pro"),shortcut:"⇧+C",isEnabled:()=>!0,callback:()=>$e.route("notes")};if(elementorPro.config.should_show_promotion){const e='<i class="eicon-advanced"></i><a class="elementor-context-menu-list__item__shortcut--link-fullwidth" href="https://go.elementor.com/go-pro-advanced-notes-context-menu/" target="_blank" rel="noopener noreferrer"></a>';o.shortcut=jQuery(e),o.isEnabled=()=>!1,delete o.callback}if(-1===r){const t=_.findWhere(e,{name:"delete"}),r=e.indexOf(t),n=-1!==r?r:e.length;return e.splice(n,0,{name:"notes",actions:[o]}),e}const n=_.findWhere(t.actions,{name:"open_notes"}),s=t.actions.indexOf(n);return e[r].actions[s]=o,e}};class P extends $e.modules.ComponentBase{static NOTES_MODE_OPEN="open";static NOTES_MODE_CLOSE="close";config={};constructor(e){super(e),window.addEventListener("message",(e=>{"elementor-pro/notes/config"===e.data.name&&(this.config={...this.config,...e.data.payload},this.isInEditor()||window.top.$e.extras.hashCommands.runOnce(),this.contextMenuNotesGroup())})),window.addEventListener("DOMContentLoaded",(()=>{const e=document.getElementById("wp-admin-bar-elementor_notes");e&&e.addEventListener("click",(e=>{e.preventDefault(),window.top.$e.run("notes/toggle")}))}))}getNamespace(){return"notes"}defaultRoutes(){return{"":()=>{}}}defaultCommands(){const e=["create","reply","edit","delete","resolve","re-open","cancel-create","cancel-reply","cancel-edit","choose-mention","open-note-actions","close-note-actions","open-panel-filters","close-panel-filters","refresh-panel"].reduce(((e,t)=>({...e,[t]:()=>{}})),{});return{...this.importCommands(o),...e}}defaultData(){return this.importCommands(n)}defaultHooks(){return this.importHooks(s)}defaultShortcuts(){return{toggle:{keys:"shift+c",exclude:["input","textarea"]}}}defaultStates(){return{"":{initialState:{active:null,formsInWritingMode:[],filters:{is_resolved:null,only_unread:null,only_relevant:null}},reducers:{setFilters:(e,{payload:t})=>({...e,filters:t}),modifyFilters:(e,{payload:t})=>({...e,filters:{...e.filters,...t}}),setActive:(e,{payload:t})=>({...e,active:{type:t.type,data:t.data},formsInWritingMode:[]}),clearActive:(e,{payload:t}={})=>!t||e.active?.data?.noteId===t?{...e,active:null,formsInWritingMode:[]}:e,addFormToWritingMode:(e,{payload:t})=>({...e,formsInWritingMode:[...e.formsInWritingMode,t]}),removeFormFromWritingMode:(e,{payload:t})=>({...e,formsInWritingMode:e.formsInWritingMode.filter((e=>e!==t))})}}}}open(){return!this.isOpen&&(this.isInEditor()&&this.updateEditorState(this.constructor.NOTES_MODE_OPEN),this.getPreviewFrame().postMessage({name:"elementor-pro/notes/open"},"*"),!0)}close(){return!!super.close()&&(this.isInEditor()&&this.updateEditorState(this.constructor.NOTES_MODE_CLOSE),this.getPreviewFrame().postMessage({name:"elementor-pro/notes/close"},"*"),!0)}isInEditor(){return!!window.elementor}updateEditorState(e){switch(e){case this.constructor.NOTES_MODE_OPEN:elementor.getPanelView().modeSwitcher.currentView.setMode("preview"),elementor.channels.dataEditMode.once("switch",(()=>{this.isOpen&&window.top.$e.run("notes/close")}));break;case this.constructor.NOTES_MODE_CLOSE:elementor.getPanelView().modeSwitcher.currentView.setMode("editor");break;default:throw new Error(`mode '${e}' is not supported.`)}}getPreviewFrame(){return this.isInEditor()?elementor.$preview[0].contentWindow:window}contextMenuNotesGroup(){this.isInEditor()&&new I}}window.top.$e.components.register(new P)})();