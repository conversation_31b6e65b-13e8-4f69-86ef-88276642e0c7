/*! For license information please see 363.min.js.LICENSE.txt */
(self.webpackChunkelementor_pro_notes=self.webpackChunkelementor_pro_notes||[]).push([[363],{1575:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}n.d(t,{m:()=>r})},2053:(e,t,n)=>{"use strict";n.d(t,{rc:()=>ue,ZD:()=>ce,UC:()=>ae,VY:()=>de,hJ:()=>se,ZL:()=>ie,bL:()=>re,hE:()=>le,l9:()=>oe});var r=n(3274),o=n(1575),i=n(5031),s=n(6549),a=n(1027),u=n(5397),c=n(6751),l=n(9320),d=n(1533),f=n(3476),p=n(851),h=n(563),v=n(535),m=n(707),y=n(1594),g=n(7940);const[b,w]=(0,v.A)("Dialog"),[E,C]=b("Dialog"),S=y.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,i=C("DialogTrigger",n),s=(0,m.s)(t,i.triggerRef);return y.createElement(u.s.button,(0,g.A)({type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":I(i.open)},r,{ref:s,onClick:(0,o.m)(e.onClick,i.onOpenToggle)}))})),O=y.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=C("DialogOverlay",e.__scopeDialog);return o.modal?y.createElement(c.C,{present:n||o.open},y.createElement(A,(0,g.A)({},r,{ref:t}))):null})),A=y.forwardRef(((e,t)=>{const{__scopeDialog:n,...o}=e,i=C("DialogOverlay",n);return y.createElement(s.A,{as:r.DX,allowPinchZoom:i.allowPinchZoom,shards:[i.contentRef]},y.createElement(u.s.div,(0,g.A)({"data-state":I(i.open)},o,{ref:t,style:{pointerEvents:"auto",...o.style}})))})),x=y.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=C("DialogContent",e.__scopeDialog);return y.createElement(c.C,{present:n||o.open},o.modal?y.createElement(R,(0,g.A)({},r,{ref:t})):y.createElement(P,(0,g.A)({},r,{ref:t})))})),R=y.forwardRef(((e,t)=>{const n=C("DialogContent",e.__scopeDialog),r=y.useRef(null),s=(0,m.s)(t,n.contentRef,r);return y.useEffect((()=>{const e=r.current;if(e)return(0,i.Eq)(e)}),[]),y.createElement(k,(0,g.A)({},e,{ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:(0,o.m)(e.onFocusOutside,(e=>e.preventDefault()))}))})),P=y.forwardRef(((e,t)=>{const n=C("DialogContent",e.__scopeDialog),r=y.useRef(!1);return y.createElement(k,(0,g.A)({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,i;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1},onInteractOutside:t=>{var o,i;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0);const s=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(s))&&t.preventDefault()}}))})),k=y.forwardRef(((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,u=C("DialogContent",n),c=y.useRef(null),l=(0,m.s)(t,c);return(0,a.Oh)(),y.createElement(y.Fragment,null,y.createElement(d.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i},y.createElement(f.qW,(0,g.A)({role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":I(u.open)},s,{ref:l,onDismiss:()=>u.onOpenChange(!1)}))),!1)})),D=y.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=C("DialogTitle",n);return y.createElement(u.s.h2,(0,g.A)({id:o.titleId},r,{ref:t}))})),T=y.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=C("DialogDescription",n);return y.createElement(u.s.p,(0,g.A)({id:o.descriptionId},r,{ref:t}))})),M=y.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,i=C("DialogClose",n);return y.createElement(u.s.button,(0,g.A)({type:"button"},r,{ref:t,onClick:(0,o.m)(e.onClick,(()=>i.onOpenChange(!1)))}))}));function I(e){return e?"open":"closed"}const[_,L]=(0,v.q)("DialogTitleWarning",{contentName:"DialogContent",titleName:"DialogTitle",docsSlug:"dialog"}),F=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0,allowPinchZoom:a}=e,u=y.useRef(null),c=y.useRef(null),[l=!1,d]=(0,p.i)({prop:r,defaultProp:o,onChange:i});return y.createElement(E,{scope:t,triggerRef:u,contentRef:c,contentId:(0,h.B)(),titleId:(0,h.B)(),descriptionId:(0,h.B)(),open:l,onOpenChange:d,onOpenToggle:y.useCallback((()=>d((e=>!e))),[d]),modal:s,allowPinchZoom:a},n)},j=S,N=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=C("DialogPortal",t);return y.createElement(y.Fragment,null,y.Children.map(r,(e=>y.createElement(c.C,{present:n||i.open},y.createElement(l.V$,{asChild:!0,container:o},e)))))},q=O,U=x,B=D,$=T,Q=M,[K,z]=(0,v.A)("AlertDialog",[w]),W=w(),H=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=W(n);return y.createElement(j,(0,g.A)({},o,r,{ref:t}))})),G=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=W(n);return y.createElement(q,(0,g.A)({},o,r,{ref:t}))})),[X,Y]=K("AlertDialogContent"),V=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,children:i,...s}=e,a=W(n),u=y.useRef(null),c=(0,m.s)(t,u),l=y.useRef(null);return y.createElement(_,{contentName:"AlertDialogContent",titleName:Z,docsSlug:"alert-dialog"},y.createElement(X,{scope:n,cancelRef:l},y.createElement(U,(0,g.A)({role:"alertdialog"},a,s,{ref:c,onOpenAutoFocus:(0,o.m)(s.onOpenAutoFocus,(e=>{var t;e.preventDefault(),null===(t=l.current)||void 0===t||t.focus({preventScroll:!0})})),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault()}),y.createElement(r.xV,null,i),!1)))})),Z="AlertDialogTitle",J=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=W(n);return y.createElement(B,(0,g.A)({},o,r,{ref:t}))})),ee=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=W(n);return y.createElement($,(0,g.A)({},o,r,{ref:t}))})),te=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=W(n);return y.createElement(Q,(0,g.A)({},o,r,{ref:t}))})),ne=y.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=Y("AlertDialogCancel",n),i=W(n),s=(0,m.s)(t,o);return y.createElement(Q,(0,g.A)({},i,r,{ref:s}))})),re=e=>{const{__scopeAlertDialog:t,...n}=e,r=W(t);return y.createElement(F,(0,g.A)({},r,n,{modal:!0}))},oe=H,ie=e=>{const{__scopeAlertDialog:t,...n}=e,r=W(t);return y.createElement(N,(0,g.A)({},r,n))},se=G,ae=V,ue=te,ce=ne,le=J,de=ee},5835:(e,t,n)=>{"use strict";n.d(t,{C1:()=>S,bL:()=>C});var r=n(5397),o=n(6751),i=n(8430),s=n(6785),a=n(8365),u=n(851),c=n(1575),l=n(535),d=n(707),f=n(1594),p=n(7940);const[h,v]=(0,l.A)("Checkbox"),[m,y]=h("Checkbox"),g=f.forwardRef(((e,t)=>{const{__scopeCheckbox:n,"aria-labelledby":o,name:s,checked:a,defaultChecked:l,required:h,disabled:v,value:y="on",onCheckedChange:g,...C}=e,[S,O]=f.useState(null),A=(0,d.s)(t,(e=>O(e))),x=(0,i.vd)(S),R=o||x,P=f.useRef(!1),k=!S||Boolean(S.closest("form")),[D=!1,T]=(0,u.i)({prop:a,defaultProp:l,onChange:g});return f.createElement(m,{scope:n,state:D,disabled:v},f.createElement(r.s.button,(0,p.A)({type:"button",role:"checkbox","aria-checked":w(D)?"mixed":D,"aria-labelledby":R,"aria-required":h,"data-state":E(D),"data-disabled":v?"":void 0,disabled:v,value:y},C,{ref:A,onKeyDown:(0,c.m)(e.onKeyDown,(e=>{"Enter"===e.key&&e.preventDefault()})),onClick:(0,c.m)(e.onClick,(e=>{T((e=>!!w(e)||!e)),k&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())}))})),k&&f.createElement(b,{control:S,bubbles:!P.current,name:s,value:y,checked:D,required:h,disabled:v,style:{transform:"translateX(-100%)"}}))})),b=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,i=f.useRef(null),u=(0,a.Z)(n),c=(0,s.X)(t);return f.useEffect((()=>{const e=i.current,t=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(t,"checked").set;if(u!==n&&o){const t=new Event("click",{bubbles:r});e.indeterminate=w(n),o.call(e,!w(n)&&n),e.dispatchEvent(t)}}),[u,n,r]),f.createElement("input",(0,p.A)({type:"checkbox","aria-hidden":!0,defaultChecked:!w(n)&&n},o,{tabIndex:-1,ref:i,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}}))};function w(e){return"indeterminate"===e}function E(e){return w(e)?"indeterminate":e?"checked":"unchecked"}const C=g,S=f.forwardRef(((e,t)=>{const{__scopeCheckbox:n,forceMount:i,...s}=e,a=y("CheckboxIndicator",n);return f.createElement(o.C,{present:i||w(a.state)||!0===a.state},f.createElement(r.s.span,(0,p.A)({"data-state":E(a.state),"data-disabled":a.disabled?"":void 0},s,{ref:t,style:{pointerEvents:"none",...e.style}})))}))},3994:(e,t,n)=>{"use strict";n.d(t,{N:()=>u});var r=n(3274),o=n(707),i=n(535),s=n(1594),a=n.n(s);function u(e){const t=e+"CollectionProvider",[n,s]=(0,i.A)(t),[u,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e+"CollectionSlot",d=a().forwardRef(((e,t)=>{const{scope:n,children:i}=e,s=c(l,n),u=(0,o.s)(t,s.collectionRef);return a().createElement(r.DX,{ref:u},i)})),f=e+"CollectionItemSlot",p="data-radix-collection-item",h=a().forwardRef(((e,t)=>{const{scope:n,children:i,...s}=e,u=a().useRef(null),l=(0,o.s)(t,u),d=c(f,n);return a().useEffect((()=>(d.itemMap.set(u,{ref:u,...s}),()=>{d.itemMap.delete(u)}))),a().createElement(r.DX,{[p]:"",ref:l},i)}));return[{Provider:e=>{const{scope:t,children:n}=e,r=a().useRef(null),o=a().useRef(new Map).current;return a().createElement(u,{scope:t,itemMap:o,collectionRef:r},n)},Slot:d,ItemSlot:h},function(t){const n=c(e+"CollectionConsumer",t);return a().useCallback((()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(n.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[n.collectionRef,n.itemMap])},s]}},707:(e,t,n)=>{"use strict";n.d(t,{s:()=>i,t:()=>o});var r=n(1594);function o(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function i(...e){return r.useCallback(o(...e),e)}},535:(e,t,n)=>{"use strict";n.d(t,{A:()=>i,q:()=>o});var r=n(1594);function o(e,t){const n=r.createContext(t);function o(e){const{children:t,...o}=e,i=r.useMemo((()=>o),Object.values(o));return r.createElement(n.Provider,{value:i},t)}return o.displayName=e+"Provider",[o,function(o){const i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw new Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[];const o=()=>{const t=n.map((e=>r.createContext(e)));return function(n){const o=(null==n?void 0:n[e])||t;return r.useMemo((()=>({[`__scope${e}`]:{...n,[e]:o}})),[n,o])}};return o.scopeName=e,[function(t,o){const i=r.createContext(o),s=n.length;function a(t){const{scope:n,children:o,...a}=t,u=(null==n?void 0:n[e][s])||i,c=r.useMemo((()=>a),Object.values(a));return r.createElement(u.Provider,{value:c},o)}return n=[...n,o],a.displayName=t+"Provider",[a,function(n,a){const u=(null==a?void 0:a[e][s])||i,c=r.useContext(u);if(c)return c;if(void 0!==o)return o;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},s(o,...t)]}function s(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(e){const o=n.reduce(((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]})),{});return r.useMemo((()=>({[`__scope${t.scopeName}`]:o})),[o])}};return n.scopeName=t.scopeName,n}},3476:(e,t,n)=>{"use strict";n.d(t,{lg:()=>b,qW:()=>h,bL:()=>g});var r=n(6697),o=n(7775),i=n(7246),s=n(1594);let a,u=0;var c=n(707),l=n(5397),d=n(1575),f=n(7940);const p=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=s.forwardRef(((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:h,onPointerDownOutside:v,onFocusOutside:g,onInteractOutside:b,onDismiss:w,...E}=e,C=s.useContext(p),[S,O]=s.useState(null),[,A]=s.useState({}),x=(0,c.s)(t,(e=>O(e))),R=Array.from(C.layers),[P]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),k=R.indexOf(P),D=S?R.indexOf(S):-1,T=C.layersWithOutsidePointerEventsDisabled.size>0,M=D>=k,I=function(){const e=(0,o.c)((e=>{const t=e.target,n=[...C.branches].some((e=>e.contains(t)));M&&!n&&(null==v||v(e),null==b||b(e),e.defaultPrevented||null==w||w())})),t=s.useRef(!1);return s.useEffect((()=>{const n=n=>{n.target&&!t.current&&y("dismissableLayer.pointerDownOutside",e,{originalEvent:n}),t.current=!1},r=window.setTimeout((()=>{document.addEventListener("pointerdown",n)}),0);return()=>{window.clearTimeout(r),document.removeEventListener("pointerdown",n)}}),[e]),{onPointerDownCapture:()=>t.current=!0}}(),_=function(){const e=(0,o.c)((e=>{const t=e.target;[...C.branches].some((e=>e.contains(t)))||(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())})),t=s.useRef(!1);return s.useEffect((()=>{const n=n=>{n.target&&!t.current&&y("dismissableLayer.focusOutside",e,{originalEvent:n})};return document.addEventListener("focusin",n),()=>document.removeEventListener("focusin",n)}),[e]),{onFocusCapture:()=>t.current=!0,onBlurCapture:()=>t.current=!1}}();return(0,r.U)((e=>{D===C.layers.size-1&&(null==h||h(e),e.defaultPrevented||null==w||w())})),function({disabled:e}){const t=s.useRef(!1);(0,i.N)((()=>{if(e){function n(){u--,0===u&&(document.body.style.pointerEvents=a)}function r(e){t.current="mouse"!==e.pointerType}return 0===u&&(a=document.body.style.pointerEvents),document.body.style.pointerEvents="none",u++,document.addEventListener("pointerup",r),()=>{t.current?document.addEventListener("click",n,{once:!0}):n(),document.removeEventListener("pointerup",r)}}}),[e])}({disabled:n}),s.useEffect((()=>{S&&(n&&C.layersWithOutsidePointerEventsDisabled.add(S),C.layers.add(S),m())}),[S,n,C]),s.useEffect((()=>()=>{S&&(C.layers.delete(S),C.layersWithOutsidePointerEventsDisabled.delete(S),m())}),[S,C]),s.useEffect((()=>{const e=()=>A({});return document.addEventListener("dismissableLayer.update",e),()=>document.removeEventListener("dismissableLayer.update",e)}),[]),s.createElement(l.s.div,(0,f.A)({},E,{ref:x,style:{pointerEvents:T?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,d.m)(e.onFocusCapture,_.onFocusCapture),onBlurCapture:(0,d.m)(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:(0,d.m)(e.onPointerDownCapture,I.onPointerDownCapture)}))})),v=s.forwardRef(((e,t)=>{const n=s.useContext(p),r=s.useRef(null),o=(0,c.s)(t,r);return s.useEffect((()=>{const e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}}),[n.branches]),s.createElement(l.s.div,(0,f.A)({},e,{ref:o}))}));function m(){const e=new Event("dismissableLayer.update");document.dispatchEvent(e)}function y(e,t,n){const r=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});return t&&r.addEventListener(e,t,{once:!0}),!r.dispatchEvent(o)}const g=h,b=v},2886:(e,t,n)=>{"use strict";n.d(t,{i3:()=>Ee,UC:()=>ge,q7:()=>be,bL:()=>me,wv:()=>we,l9:()=>ye});var r=n(563),o=n(1027),i=n(7775),s=n(1594);var a=n(5666),u=n(9320),c=n(1568),l=n(5397),d=n(6751),f=n(1533),p=n(3476),h=n(535),v=n(707),m=n(3994),y=n(1575),g=n(5031),b=n(6549),w=n(7940);const E=["Enter"," "],C=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...C],O={ltr:["ArrowLeft"],rtl:["ArrowRight"]},[A,x,R]=(0,m.N)("Menu"),[P,k]=(0,h.A)("Menu",[R,c.Bk,a.RG]),D=(0,c.Bk)(),T=(0,a.RG)(),[M,I]=P("Menu"),_=s.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e,o=D(n);return s.createElement(c.Mz,(0,w.A)({},o,r,{ref:t}))})),[L,F]=P("MenuContent"),j=s.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=I("MenuContent",e.__scopeMenu);return s.createElement(A.Provider,{scope:e.__scopeMenu},s.createElement(d.C,{present:n||o.open},s.createElement(A.Slot,{scope:e.__scopeMenu},o.isSubmenu?s.createElement(B,(0,w.A)({},r,{ref:t})):s.createElement(N,(0,w.A)({},r,{ref:t})))))})),N=s.forwardRef(((e,t)=>I("MenuContent",e.__scopeMenu).modal?s.createElement(q,(0,w.A)({},e,{ref:t})):s.createElement(U,(0,w.A)({},e,{ref:t})))),q=s.forwardRef(((e,t)=>{const n=I("MenuContent",e.__scopeMenu),r=s.useRef(null),o=(0,v.s)(t,r);return s.useEffect((()=>{const e=r.current;if(e)return(0,g.Eq)(e)}),[]),s.createElement($,(0,w.A)({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,y.m)(e.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)}))})),U=s.forwardRef(((e,t)=>{const n=I("MenuContent",e.__scopeMenu);return s.createElement($,(0,w.A)({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)}))})),B=s.forwardRef(((e,t)=>{const n=I("MenuContent",e.__scopeMenu),r=s.useRef(null),o=(0,v.s)(t,r);return n.isSubmenu?s.createElement($,(0,w.A)({id:n.contentId,"aria-labelledby":n.triggerId},e,{ref:o,align:"start",side:"rtl"===n.dir?"left":"right",portalled:!0,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;n.isUsingKeyboardRef.current&&(null===(t=r.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,y.m)(e.onFocusOutside,(e=>{e.target!==n.trigger&&n.onOpenChange(!1)})),onEscapeKeyDown:(0,y.m)(e.onEscapeKeyDown,n.onRootClose),onKeyDown:(0,y.m)(e.onKeyDown,(e=>{const t=e.currentTarget.contains(e.target),r=O[n.dir].includes(e.key);var o;t&&r&&(n.onOpenChange(!1),null===(o=n.trigger)||void 0===o||o.focus(),e.preventDefault())}))})):null})),$=s.forwardRef(((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:d,disableOutsidePointerEvents:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:E,onInteractOutside:O,onDismiss:A,disableOutsideScroll:R,allowPinchZoom:P,portalled:k,...M}=e,_=I("MenuContent",n),F=D(n),j=T(n),N=x(n),[q,U]=s.useState(null),B=s.useRef(null),$=(0,v.s)(t,B,_.onContentChange),Q=s.useRef(0),K=s.useRef(""),z=s.useRef(0),W=s.useRef(null),H=s.useRef("right"),G=s.useRef(0),V=k?u.ZL:s.Fragment,Z=R?b.A:s.Fragment,J=R?{allowPinchZoom:P}:void 0;s.useEffect((()=>()=>window.clearTimeout(Q.current)),[]),(0,o.Oh)();const ee=s.useCallback((e=>{var t,n;return H.current===(null===(t=W.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){const{x:n,y:r}=e;let o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){const s=t[e].x,a=t[e].y,u=t[i].x,c=t[i].y;a>r!=c>r&&n<(u-s)*(r-a)/(c-a)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(n=W.current)||void 0===n?void 0:n.area)}),[]);return s.createElement(V,null,s.createElement(Z,J,s.createElement(L,{scope:n,searchRef:K,onItemEnter:s.useCallback((e=>{ee(e)&&e.preventDefault()}),[ee]),onItemLeave:s.useCallback((e=>{var t;ee(e)||(null===(t=B.current)||void 0===t||t.focus(),U(null))}),[ee]),onTriggerLeave:s.useCallback((e=>{ee(e)&&e.preventDefault()}),[ee]),pointerGraceTimerRef:z,onPointerGraceIntentChange:s.useCallback((e=>{W.current=e}),[])},s.createElement(f.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,y.m)(l,(e=>{var t;e.preventDefault(),null===(t=B.current)||void 0===t||t.focus()})),onUnmountAutoFocus:d},s.createElement(p.qW,{asChild:!0,disableOutsidePointerEvents:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:E,onInteractOutside:O,onDismiss:A},s.createElement(a.bL,(0,w.A)({asChild:!0},j,{dir:_.dir,orientation:"vertical",loop:r,currentTabStopId:q,onCurrentTabStopIdChange:U,onEntryFocus:e=>{_.isUsingKeyboardRef.current||e.preventDefault()}}),s.createElement(c.UC,(0,w.A)({role:"menu","aria-orientation":"vertical","data-state":X(_.open),dir:_.dir},F,M,{ref:$,style:{outline:"none",...M.style},onKeyDown:(0,y.m)(M.onKeyDown,(e=>{const t=e.target,n=e.currentTarget.contains(t),r=e.ctrlKey||e.altKey||e.metaKey,o=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&o&&(e=>{var t,n;const r=K.current+e,o=N().filter((e=>!e.disabled)),i=document.activeElement,s=null===(t=o.find((e=>e.ref.current===i)))||void 0===t?void 0:t.textValue,a=function(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let i=(s=e,a=Math.max(o,0),s.map(((e,t)=>s[(a+t)%s.length])));var s,a;1===r.length&&(i=i.filter((e=>e!==n)));const u=i.find((e=>e.toLowerCase().startsWith(r.toLowerCase())));return u!==n?u:void 0}(o.map((e=>e.textValue)),r,s),u=null===(n=o.find((e=>e.textValue===a)))||void 0===n?void 0:n.ref.current;!function e(t){K.current=t,window.clearTimeout(Q.current),""!==t&&(Q.current=window.setTimeout((()=>e("")),1e3))}(r),u&&setTimeout((()=>u.focus()))})(e.key));const i=B.current;if(e.target!==i)return;if(!S.includes(e.key))return;e.preventDefault();const s=N().filter((e=>!e.disabled)).map((e=>e.ref.current));C.includes(e.key)&&s.reverse(),function(e){const t=document.activeElement;for(const n of e){if(n===t)return;if(n.focus(),document.activeElement!==t)return}}(s)})),onBlur:(0,y.m)(e.onBlur,(e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(Q.current),K.current="")})),onPointerMove:(0,y.m)(e.onPointerMove,Y((e=>{const t=e.target,n=G.current!==e.clientX;if(e.currentTarget.contains(t)&&n){const t=e.clientX>G.current?"right":"left";H.current=t,G.current=e.clientX}})))}))))))))})),Q=s.forwardRef(((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,i=s.useRef(null),a=I("MenuItem",e.__scopeMenu),u=F("MenuItem",e.__scopeMenu),c=(0,v.s)(t,i),l=s.useRef(!1);return s.createElement(K,(0,w.A)({},o,{ref:c,disabled:n,onClick:(0,y.m)(e.onClick,(()=>{const e=i.current;if(!n&&e){const t=new Event("menu.itemSelect",{bubbles:!0,cancelable:!0});e.addEventListener("menu.itemSelect",(e=>null==r?void 0:r(e)),{once:!0}),e.dispatchEvent(t),t.defaultPrevented?l.current=!1:a.onRootClose()}})),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),l.current=!0},onPointerUp:(0,y.m)(e.onPointerUp,(e=>{var t;l.current||null===(t=e.currentTarget)||void 0===t||t.click()})),onKeyDown:(0,y.m)(e.onKeyDown,(e=>{const t=""!==u.searchRef.current;n||t&&" "===e.key||E.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())}))}))})),K=s.forwardRef(((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,u=F("MenuItem",n),c=T(n),d=s.useRef(null),f=(0,v.s)(t,d),[p,h]=s.useState("");return s.useEffect((()=>{const e=d.current;var t;e&&h((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}),[i.children]),s.createElement(A.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:p},s.createElement(a.q7,(0,w.A)({asChild:!0},c,{focusable:!r}),s.createElement(l.s.div,(0,w.A)({role:"menuitem","aria-disabled":r||void 0,"data-disabled":r?"":void 0},i,{ref:f,onPointerMove:(0,y.m)(e.onPointerMove,Y((e=>{r?u.onItemLeave(e):(u.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus())}))),onPointerLeave:(0,y.m)(e.onPointerLeave,Y((e=>u.onItemLeave(e))))}))))})),[z,W]=P("MenuRadioGroup",{value:void 0,onValueChange:()=>{}}),[H,G]=P("MenuItemIndicator",{checked:!1});function X(e){return e?"open":"closed"}function Y(e){return t=>"mouse"===t.pointerType?e(t):void 0}const V=e=>{const{__scopeMenu:t,open:n=!1,children:r,onOpenChange:o,modal:a=!0}=e,u=D(t),[l,d]=s.useState(null),f=s.useRef(!1),p=(0,i.c)(o),h=function(e,t){const[n,r]=s.useState("ltr"),[o,i]=s.useState(),a=s.useRef(0);return s.useEffect((()=>{if(void 0===t&&null!=e&&e.parentElement){const t=getComputedStyle(e.parentElement);i(t)}}),[e,t]),s.useEffect((()=>(void 0===t&&function e(){a.current=requestAnimationFrame((()=>{const t=null==o?void 0:o.direction;t&&r(t),e()}))}(),()=>cancelAnimationFrame(a.current))),[o,t,r]),t||n}(l,e.dir);return s.useEffect((()=>{const e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}}),[]),s.createElement(c.bL,u,s.createElement(M,{scope:t,isSubmenu:!1,isUsingKeyboardRef:f,dir:h,open:n,onOpenChange:p,content:l,onContentChange:d,onRootClose:s.useCallback((()=>p(!1)),[p]),modal:a},r))},Z=e=>{const{__scopeMenu:t,children:n,open:o=!1,onOpenChange:a}=e,u=I("MenuSub",t),l=D(t),[d,f]=s.useState(null),[p,h]=s.useState(null),v=(0,i.c)(a);return s.useEffect((()=>(!1===u.open&&v(!1),()=>v(!1))),[u.open,v]),s.createElement(c.bL,l,s.createElement(M,{scope:t,isSubmenu:!0,isUsingKeyboardRef:u.isUsingKeyboardRef,dir:u.dir,open:o,onOpenChange:v,content:p,onContentChange:h,onRootClose:u.onRootClose,contentId:(0,r.B)(),trigger:d,onTriggerChange:f,triggerId:(0,r.B)(),modal:!1},n))},J=_,ee=j,te=Q,ne=s.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e;return s.createElement(l.s.div,(0,w.A)({role:"separator","aria-orientation":"horizontal"},r,{ref:t}))})),re=s.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e,o=D(n);return s.createElement(c.i3,(0,w.A)({},o,r,{ref:t}))}));var oe=n(851);const[ie,se]=(0,h.A)("DropdownMenu",[k]),ae=k(),[ue,ce]=ie("DropdownMenu"),le=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:i,onOpenChange:a,onOpenToggle:u,modal:c=!0}=e,l=ae(t),d=s.useRef(null);return s.createElement(ue,{scope:t,isRootMenu:!0,triggerId:(0,r.B)(),triggerRef:d,contentId:(0,r.B)(),open:i,onOpenChange:a,onOpenToggle:u,modal:c},s.createElement(V,(0,w.A)({},l,{open:i,onOpenChange:a,dir:o,modal:c}),n))},de=s.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=ce("DropdownMenuTrigger",n),a=ae(n);return i.isRootMenu?s.createElement(J,(0,w.A)({asChild:!0},a),s.createElement(l.s.button,(0,w.A)({type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":!!i.open||void 0,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r},o,{ref:(0,v.t)(t,i.triggerRef),onPointerDown:(0,y.m)(e.onPointerDown,(e=>{r||0!==e.button||!1!==e.ctrlKey||(i.open||e.preventDefault(),i.onOpenToggle())})),onKeyDown:(0,y.m)(e.onKeyDown,(e=>{r||(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),[" ","ArrowDown"].includes(e.key)&&e.preventDefault())}))}))):null})),[fe,pe]=ie("DropdownMenuContent",{isInsideContent:!1}),he=s.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ce("DropdownMenuContent",n),i=ae(n),a={...r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)"}};return s.createElement(fe,{scope:n,isInsideContent:!0},o.isRootMenu?s.createElement(ve,(0,w.A)({__scopeDropdownMenu:n},a,{ref:t})):s.createElement(ee,(0,w.A)({},i,a,{ref:t})))})),ve=s.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,portalled:r=!0,...o}=e,i=ce("DropdownMenuContent",n),a=ae(n),u=s.useRef(!1);return i.isRootMenu?s.createElement(ee,(0,w.A)({id:i.contentId,"aria-labelledby":i.triggerId},a,o,{ref:t,portalled:r,onCloseAutoFocus:(0,y.m)(e.onCloseAutoFocus,(e=>{var t;u.current||null===(t=i.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()})),onInteractOutside:(0,y.m)(e.onInteractOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;i.modal&&!r||(u.current=!0)}))})):null})),me=e=>{const{__scopeDropdownMenu:t,children:n,open:r,defaultOpen:o,onOpenChange:i}=e,a=pe("DropdownMenu",t),u=ae(t),[c=!1,l]=(0,oe.i)({prop:r,defaultProp:o,onChange:i}),d=s.useCallback((()=>l((e=>!e))),[l]);return a.isInsideContent?s.createElement(ue,{scope:t,isRootMenu:!1,open:c,onOpenChange:l,onOpenToggle:d},s.createElement(Z,(0,w.A)({},u,{open:c,onOpenChange:l}),n)):s.createElement(le,(0,w.A)({},e,{open:c,onOpenChange:l,onOpenToggle:d}),n)},ye=de,ge=he,be=s.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ae(n);return s.createElement(te,(0,w.A)({},o,r,{ref:t}))})),we=s.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ae(n);return s.createElement(ne,(0,w.A)({},o,r,{ref:t}))})),Ee=s.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ae(n);return s.createElement(re,(0,w.A)({},o,r,{ref:t}))}))},1027:(e,t,n)=>{"use strict";n.d(t,{Oh:()=>i});var r=n(1594);let o=0;function i(){r.useEffect((()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:s()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:s()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),o--}}),[])}function s(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}},1533:(e,t,n)=>{"use strict";n.d(t,{n:()=>c});var r=n(7775),o=n(5397),i=n(707),s=n(1594),a=n(7940);const u={bubbles:!1,cancelable:!0},c=s.forwardRef(((e,t)=>{const{loop:n=!1,trapped:c=!1,onMountAutoFocus:f,onUnmountAutoFocus:v,...m}=e,[y,g]=s.useState(null),b=(0,r.c)(f),w=(0,r.c)(v),E=s.useRef(null),C=(0,i.s)(t,(e=>g(e))),S=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect((()=>{if(c){function e(e){if(S.paused||!y)return;const t=e.target;y.contains(t)?E.current=t:p(E.current,{select:!0})}function t(e){!S.paused&&y&&(y.contains(e.relatedTarget)||p(E.current,{select:!0}))}return document.addEventListener("focusin",e),document.addEventListener("focusout",t),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t)}}}),[c,y,S.paused]),s.useEffect((()=>{if(y){h.add(S);const e=document.activeElement;if(!y.contains(e)){const t=new Event("focusScope.autoFocusOnMount",u);y.addEventListener("focusScope.autoFocusOnMount",b),y.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(p(r,{select:t}),document.activeElement!==n)return}(l(y).filter((e=>"A"!==e.tagName)),{select:!0}),document.activeElement===e&&p(y))}return()=>{y.removeEventListener("focusScope.autoFocusOnMount",b),setTimeout((()=>{const t=new Event("focusScope.autoFocusOnUnmount",u);y.addEventListener("focusScope.autoFocusOnUnmount",w),y.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),y.removeEventListener("focusScope.autoFocusOnUnmount",w),h.remove(S)}),0)}}}),[y,b,w,S]);const O=s.useCallback((e=>{if(!n&&!c)return;if(S.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){const t=e.currentTarget,[o,i]=function(e){const t=l(e);return[d(t,e),d(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&p(i,{select:!0})):(e.preventDefault(),n&&p(o,{select:!0})):r===t&&e.preventDefault()}}),[n,c,S.paused]);return s.createElement(o.s.div,(0,a.A)({tabIndex:-1},m,{ref:C,onKeyDown:O}))}));function l(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function d(e,t){for(const n of e)if(!f(n,{upTo:t}))return n}function f(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function p(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}const h=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=v(e,t),e.unshift(t)},remove(t){var n;e=v(e,t),null===(n=e[0])||void 0===n||n.resume()}}}();function v(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}},563:(e,t,n)=>{"use strict";n.d(t,{B:()=>a});var r=n(7246),o=n(1594);const i=o["useId".toString()]||(()=>{});let s=0;function a(e){const[t,n]=o.useState(i());return(0,r.N)((()=>{e||n((e=>null!=e?e:String(s++)))}),[e]),e||(t?`radix-${t}`:"")}},8430:(e,t,n)=>{"use strict";n.d(t,{JU:()=>d,vd:()=>f});var r=n(563),o=n(5397),i=n(707),s=n(535),a=n(1594),u=n(7940);const[c,l]=(0,s.q)("Label",{id:void 0,controlRef:{current:null}}),d=a.forwardRef(((e,t)=>{const{htmlFor:n,id:s,...l}=e,d=a.useRef(null),f=a.useRef(null),p=(0,i.s)(t,f),h=(0,r.B)(s);return a.useEffect((()=>{if(n){const e=document.getElementById(n);if(f.current&&e){const t=()=>e.getAttribute("aria-labelledby"),n=[h,t()].filter(Boolean).join(" ");return e.setAttribute("aria-labelledby",n),d.current=e,()=>{var n;const r=null===(n=t())||void 0===n?void 0:n.replace(h,"");""===r?e.removeAttribute("aria-labelledby"):r&&e.setAttribute("aria-labelledby",r)}}}}),[h,n]),a.createElement(c,{id:h,controlRef:d},a.createElement(o.s.span,(0,u.A)({role:"label",id:h},l,{ref:p,onMouseDown:t=>{var n;null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault()},onClick:t=>{var n;if(null===(n=e.onClick)||void 0===n||n.call(e,t),!d.current||t.defaultPrevented)return;const r=d.current.contains(t.target),o=!0===t.isTrusted;!r&&o&&(d.current.click(),d.current.focus())}})))})),f=e=>{const t=l("LabelConsumer"),{controlRef:n}=t;return a.useEffect((()=>{e&&(n.current=e)}),[e,n]),t.id}},1121:(e,t,n)=>{"use strict";n.d(t,{UC:()=>M,bL:()=>D,bm:()=>I,i3:()=>_,l9:()=>T});var r=n(5031),o=n(6549),i=n(563),s=n(5397),a=n(6751),u=n(1027),c=n(9320),l=n(1533),d=n(3476),f=n(1568),p=n(851),h=n(535),v=n(707),m=n(1575),y=n(1594),g=n(7940);const[b,w]=(0,h.A)("Popover",[f.Bk]),E=(0,f.Bk)(),[C,S]=b("Popover"),O=y.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,o=S("PopoverTrigger",n),i=E(n),a=(0,v.s)(t,o.triggerRef),u=y.createElement(s.s.button,(0,g.A)({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":k(o.open)},r,{ref:a,onClick:(0,m.m)(e.onClick,o.onOpenToggle)}));return o.hasCustomAnchor?u:y.createElement(f.Mz,(0,g.A)({asChild:!0},i),u)})),A=y.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=S("PopoverContent",e.__scopePopover);return y.createElement(a.C,{present:n||o.open},o.modal?y.createElement(x,(0,g.A)({},r,{ref:t})):y.createElement(R,(0,g.A)({},r,{ref:t})))})),x=y.forwardRef(((e,t)=>{const{allowPinchZoom:n,portalled:i=!0,...s}=e,a=S("PopoverContent",e.__scopePopover),u=y.useRef(null),l=(0,v.s)(t,u),d=y.useRef(!1);y.useEffect((()=>{const e=u.current;if(e)return(0,r.Eq)(e)}),[]);const f=i?c.ZL:y.Fragment;return y.createElement(f,null,y.createElement(o.A,{allowPinchZoom:n},y.createElement(P,(0,g.A)({},s,{ref:l,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,m.m)(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),d.current||null===(t=a.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:(0,m.m)(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;d.current=r}),{checkForDefaultPrevented:!1}),onFocusOutside:(0,m.m)(e.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1})}))))})),R=y.forwardRef(((e,t)=>{const{portalled:n=!0,...r}=e,o=S("PopoverContent",e.__scopePopover),i=y.useRef(!1),s=n?c.ZL:y.Fragment;return y.createElement(s,null,y.createElement(P,(0,g.A)({},r,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,r;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current||null===(r=o.triggerRef.current)||void 0===r||r.focus(),t.preventDefault()),i.current=!1},onInteractOutside:t=>{var n,r;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current=!0);const s=t.target;(null===(r=o.triggerRef.current)||void 0===r?void 0:r.contains(s))&&t.preventDefault()}})))})),P=y.forwardRef(((e,t)=>{const{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:s,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:p,onInteractOutside:h,...v}=e,m=S("PopoverContent",n),b=E(n);return(0,u.Oh)(),y.createElement(l.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i},y.createElement(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:h,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:p,onDismiss:()=>m.onOpenChange(!1)},y.createElement(f.UC,(0,g.A)({"data-state":k(m.open),role:"dialog",id:m.contentId},b,v,{ref:t,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)"}}))))}));function k(e){return e?"open":"closed"}const D=e=>{const{__scopePopover:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:a=!1}=e,u=E(t),c=y.useRef(null),[l,d]=y.useState(!1),[h=!1,v]=(0,p.i)({prop:r,defaultProp:o,onChange:s});return y.createElement(f.bL,u,y.createElement(C,{scope:t,contentId:(0,i.B)(),triggerRef:c,open:h,onOpenChange:v,onOpenToggle:y.useCallback((()=>v((e=>!e))),[v]),hasCustomAnchor:l,onCustomAnchorAdd:y.useCallback((()=>d(!0)),[]),onCustomAnchorRemove:y.useCallback((()=>d(!1)),[]),modal:a},n))},T=O,M=A,I=y.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,o=S("PopoverClose",n);return y.createElement(s.s.button,(0,g.A)({type:"button"},r,{ref:t,onClick:(0,m.m)(e.onClick,(()=>o.onOpenChange(!1)))}))})),_=y.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,o=E(n);return y.createElement(f.i3,(0,g.A)({},o,r,{ref:t}))}))},1568:(e,t,n)=>{"use strict";n.d(t,{Mz:()=>M,i3:()=>_,UC:()=>I,bL:()=>T,Bk:()=>S});var r=n(5397),o=n(1594),i=n(7940);const s=o.forwardRef(((e,t)=>{const{children:n,width:s=10,height:a=5,...u}=e;return o.createElement(r.s.svg,(0,i.A)({},u,{ref:t,width:s,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none"}),e.asChild?n:o.createElement("polygon",{points:"0,0 30,0 15,10"}))})),a=s;var u=n(6785),c=n(8373),l=n(535),d=n(707);function f({anchorRect:e,popperSize:t,arrowSize:n,arrowOffset:r=0,side:o,sideOffset:i=0,align:s,alignOffset:a=0,shouldAvoidCollisions:u=!0,collisionBoundariesRect:c,collisionTolerance:l=0}){if(!e||!t||!c)return{popperStyles:m,arrowStyles:y};const d=function(e,t,n=0,r=0,o){const i=o?o.height:0,s=p(t,e,"x"),a=p(t,e,"y"),u=a.before-n-i,c=a.after+n+i,l=s.before-n-i,d=s.after+n+i;return{top:{start:{x:s.start+r,y:u},center:{x:s.center,y:u},end:{x:s.end-r,y:u}},right:{start:{x:d,y:a.start+r},center:{x:d,y:a.center},end:{x:d,y:a.end-r}},bottom:{start:{x:s.start+r,y:c},center:{x:s.center,y:c},end:{x:s.end-r,y:c}},left:{start:{x:l,y:a.start+r},center:{x:l,y:a.center},end:{x:l,y:a.end-r}}}}(t,e,i,a,n),f=d[o][s];if(!1===u){const e=h(f);let i=y;return n&&(i=g({popperSize:t,arrowSize:n,arrowOffset:r,side:o,align:s})),{popperStyles:{...e,"--radix-popper-transform-origin":v(t,o,s,r,n)},arrowStyles:i,placedSide:o,placedAlign:s}}const b=DOMRect.fromRect({...t,...f}),C=(S=c,O=l,DOMRect.fromRect({width:S.width-2*O,height:S.height-2*O,x:S.left+O,y:S.top+O}));var S,O;const A=E(b,C),x=d[w(o)][s],R=function(e,t,n){const r=w(e);return t[e]&&!n[r]?r:e}(o,A,E(DOMRect.fromRect({...t,...x}),C)),P=function(e,t,n,r,o){const i="top"===n||"bottom"===n,s=i?"left":"top",a=i?"right":"bottom",u=i?"width":"height",c=t[u]>e[u];return"start"!==r&&"center"!==r||!(o[s]&&c||o[a]&&!c)?"end"!==r&&"center"!==r||!(o[a]&&c||o[s]&&!c)?r:"start":"end"}(t,e,o,s,A),k=h(d[R][P]);let D=y;return n&&(D=g({popperSize:t,arrowSize:n,arrowOffset:r,side:R,align:P})),{popperStyles:{...k,"--radix-popper-transform-origin":v(t,R,P,r,n)},arrowStyles:D,placedSide:R,placedAlign:P}}function p(e,t,n){const r=e["x"===n?"left":"top"],o="x"===n?"width":"height",i=e[o],s=t[o];return{before:r-s,start:r,center:r+(i-s)/2,end:r+i-s,after:r+i}}function h(e){return{position:"absolute",top:0,left:0,minWidth:"max-content",willChange:"transform",transform:`translate3d(${Math.round(e.x+window.scrollX)}px, ${Math.round(e.y+window.scrollY)}px, 0)`}}function v(e,t,n,r,o){const i="top"===t||"bottom"===t,s=o?o.width:0,a=o?o.height:0,u=s/2+r;let c="",l="";return i?(c={start:`${u}px`,center:"center",end:e.width-u+"px"}[n],l="top"===t?`${e.height+a}px`:-a+"px"):(c="left"===t?`${e.width+a}px`:-a+"px",l={start:`${u}px`,center:"center",end:e.height-u+"px"}[n]),`${c} ${l}`}const m={position:"fixed",top:0,left:0,opacity:0,transform:"translate3d(0, -200%, 0)"},y={position:"absolute",opacity:0};function g({popperSize:e,arrowSize:t,arrowOffset:n,side:r,align:o}){const i=(e.width-t.width)/2,s=(e.height-t.width)/2,a={top:0,right:90,bottom:180,left:-90}[r],u=Math.max(t.width,t.height),c={width:`${u}px`,height:`${u}px`,transform:`rotate(${a}deg)`,willChange:"transform",position:"absolute",[r]:"100%",direction:b(r,o)};return"top"!==r&&"bottom"!==r||("start"===o&&(c.left=`${n}px`),"center"===o&&(c.left=`${i}px`),"end"===o&&(c.right=`${n}px`)),"left"!==r&&"right"!==r||("start"===o&&(c.top=`${n}px`),"center"===o&&(c.top=`${s}px`),"end"===o&&(c.bottom=`${n}px`)),c}function b(e,t){return("top"!==e&&"right"!==e||"end"!==t)&&("bottom"!==e&&"left"!==e||"end"===t)?"ltr":"rtl"}function w(e){return{top:"bottom",right:"left",bottom:"top",left:"right"}[e]}function E(e,t){return{top:e.top<t.top,right:e.right>t.right,bottom:e.bottom>t.bottom,left:e.left<t.left}}const[C,S]=(0,l.A)("Popper"),[O,A]=C("Popper"),x=o.forwardRef(((e,t)=>{const{__scopePopper:n,virtualRef:s,...a}=e,u=A("PopperAnchor",n),c=o.useRef(null),l=(0,d.s)(t,c);return o.useEffect((()=>{u.onAnchorChange((null==s?void 0:s.current)||c.current)})),s?null:o.createElement(r.s.div,(0,i.A)({},a,{ref:l}))})),[R,P]=C("PopperContent"),k=o.forwardRef(((e,t)=>{const{__scopePopper:n,side:s="bottom",sideOffset:a,align:l="center",alignOffset:p,collisionTolerance:h,avoidCollisions:v=!0,...m}=e,y=A("PopperContent",n),[g,b]=o.useState(),w=(0,c.y)(y.anchor),[E,C]=o.useState(null),S=(0,u.X)(E),[O,x]=o.useState(null),P=(0,u.X)(O),k=(0,d.s)(t,(e=>C(e))),D=function(){const[e,t]=o.useState(void 0);return o.useEffect((()=>{let e;function n(){t({width:window.innerWidth,height:window.innerHeight})}function r(){window.clearTimeout(e),e=window.setTimeout(n,100)}return n(),window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)}),[]),e}(),T=D?DOMRect.fromRect({...D,x:0,y:0}):void 0,{popperStyles:M,arrowStyles:I,placedSide:_,placedAlign:L}=f({anchorRect:w,popperSize:S,arrowSize:P,arrowOffset:g,side:s,sideOffset:a,align:l,alignOffset:p,shouldAvoidCollisions:v,collisionBoundariesRect:T,collisionTolerance:h}),F=void 0!==_;return o.createElement("div",{style:M,"data-radix-popper-content-wrapper":""},o.createElement(R,{scope:n,arrowStyles:I,onArrowChange:x,onArrowOffsetChange:b},o.createElement(r.s.div,(0,i.A)({"data-side":_,"data-align":L},m,{style:{...m.style,animation:F?void 0:"none"},ref:k}))))})),D=o.forwardRef((function(e,t){const{__scopePopper:n,offset:r,...s}=e,u=P("PopperArrow",n),{onArrowOffsetChange:c}=u;return o.useEffect((()=>c(r)),[c,r]),o.createElement("span",{style:{...u.arrowStyles,pointerEvents:"none"}},o.createElement("span",{ref:u.onArrowChange,style:{display:"inline-block",verticalAlign:"top",pointerEvents:"auto"}},o.createElement(a,(0,i.A)({},s,{ref:t,style:{...s.style,display:"block"}}))))})),T=e=>{const{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return o.createElement(O,{scope:t,anchor:r,onAnchorChange:i},n)},M=x,I=k,_=D},9320:(e,t,n)=>{"use strict";n.d(t,{V$:()=>l,ZL:()=>c});var r=n(5397),o=n(7246),i=n(5206),s=n.n(i),a=n(1594),u=n(7940);const c=a.forwardRef(((e,t)=>{var n,i;const{containerRef:c,style:l,...d}=e,f=null!==(n=null==c?void 0:c.current)&&void 0!==n?n:null===globalThis||void 0===globalThis||null===(i=globalThis.document)||void 0===i?void 0:i.body,[,p]=a.useState({});return(0,o.N)((()=>{p({})}),[]),f?s().createPortal(a.createElement(r.s.div,(0,u.A)({"data-radix-portal":""},d,{ref:t,style:f===document.body?{position:"absolute",top:0,left:0,zIndex:2147483647,...l}:void 0})),f):null})),l=a.forwardRef(((e,t)=>{var n;const{container:o=(null===globalThis||void 0===globalThis||null===(n=globalThis.document)||void 0===n?void 0:n.body),...i}=e;return o?s().createPortal(a.createElement(r.s.div,(0,u.A)({},i,{ref:t})),o):null}))},6751:(e,t,n)=>{"use strict";n.d(t,{C:()=>s});var r=n(7246),o=n(707),i=n(1594);const s=e=>{const{present:t,children:n}=e,s=function(e){const[t,n]=i.useState(),o=i.useRef({}),s=i.useRef(e),u=i.useRef("none"),c=e?"mounted":"unmounted",[l,d]=function(e,t){return i.useReducer(((e,n)=>{const r=t[e][n];return null!=r?r:e}),e)}(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect((()=>{const e=a(o.current);u.current="mounted"===l?e:"none"}),[l]),(0,r.N)((()=>{const t=o.current,n=s.current;if(n!==e){const r=u.current,o=a(t);if(e)d("MOUNT");else if("none"===o||"none"===(null==t?void 0:t.display))d("UNMOUNT");else{d(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}s.current=e}}),[e,d]),(0,r.N)((()=>{if(t){const e=e=>{const n=a(o.current).includes(e.animationName);e.target===t&&n&&d("ANIMATION_END")},n=e=>{e.target===t&&(u.current=a(o.current))};return t.addEventListener("animationstart",n),t.addEventListener("animationcancel",e),t.addEventListener("animationend",e),()=>{t.removeEventListener("animationstart",n),t.removeEventListener("animationcancel",e),t.removeEventListener("animationend",e)}}d("ANIMATION_END")}),[t,d]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:i.useCallback((e=>{e&&(o.current=getComputedStyle(e)),n(e)}),[])}}(t),u="function"==typeof n?n({present:s.isPresent}):i.Children.only(n),c=(0,o.s)(s.ref,u.ref);return"function"==typeof n||s.isPresent?i.cloneElement(u,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence"},5397:(e,t,n)=>{"use strict";n.d(t,{s:()=>s});var r=n(3274),o=n(1594),i=n(7940);const s=["a","button","div","h2","h3","img","li","nav","ol","p","span","svg","ul"].reduce(((e,t)=>({...e,[t]:o.forwardRef(((e,n)=>{const{asChild:s,...a}=e,u=s?r.DX:t;return o.useEffect((()=>{window[Symbol.for("radix-ui")]=!0}),[]),o.createElement(u,(0,i.A)({},a,{ref:n}))}))})),{})},8489:(e,t,n)=>{"use strict";n.d(t,{C1:()=>I,q7:()=>M,z6:()=>D});var r=n(6751),o=n(8365),i=n(6785),s=n(851),a=n(5666),u=n(5397),c=n(535),l=n(707),d=n(8430),f=n(1575),p=n(1594),h=n(7940);const[v,m]=(0,c.A)("Radio"),[y,g]=v("Radio"),b=p.forwardRef(((e,t)=>{const{__scopeRadio:n,"aria-labelledby":r,name:o,checked:i=!1,required:s,disabled:a,value:c="on",onCheck:v,...m}=e,[g,b]=p.useState(null),w=(0,l.s)(t,(e=>b(e))),S=(0,d.vd)(g),O=r||S,A=p.useRef(!1),x=!g||Boolean(g.closest("form"));return p.createElement(y,{scope:n,checked:i,disabled:a},p.createElement(u.s.button,(0,h.A)({type:"button",role:"radio","aria-checked":i,"aria-labelledby":O,"data-state":C(i),"data-disabled":a?"":void 0,disabled:a,value:c},m,{ref:w,onClick:(0,f.m)(e.onClick,(e=>{i||null==v||v(),x&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())}))})),x&&p.createElement(E,{control:g,bubbles:!A.current,name:o,value:c,checked:i,required:s,disabled:a,style:{transform:"translateX(-100%)"}}))})),w=p.forwardRef(((e,t)=>{const{__scopeRadio:n,forceMount:o,...i}=e,s=g("RadioIndicator",n);return p.createElement(r.C,{present:o||s.checked},p.createElement(u.s.span,(0,h.A)({"data-state":C(s.checked),"data-disabled":s.disabled?"":void 0},i,{ref:t})))})),E=e=>{const{control:t,checked:n,bubbles:r=!0,...s}=e,a=p.useRef(null),u=(0,o.Z)(n),c=(0,i.X)(t);return p.useEffect((()=>{const e=a.current,t=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(t,"checked").set;if(u!==n&&o){const t=new Event("click",{bubbles:r});o.call(e,n),e.dispatchEvent(t)}}),[u,n,r]),p.createElement("input",(0,h.A)({type:"radio","aria-hidden":!0,defaultChecked:n},s,{tabIndex:-1,ref:a,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}}))};function C(e){return e?"checked":"unchecked"}const S=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],[O,A]=(0,c.A)("RadioGroup",[a.RG,m]),x=(0,a.RG)(),R=m(),[P,k]=O("RadioGroup"),D=p.forwardRef(((e,t)=>{const{__scopeRadioGroup:n,name:r,"aria-labelledby":o,defaultValue:i,value:c,required:l=!1,orientation:f,dir:v="ltr",loop:m=!0,onValueChange:y,...g}=e,b=(0,d.vd)(),w=o||b,E=x(n),[C,S]=(0,s.i)({prop:c,defaultProp:i,onChange:y});return p.createElement(P,{scope:n,name:r,required:l,value:C,onValueChange:S},p.createElement(a.bL,(0,h.A)({asChild:!0},E,{orientation:f,dir:v,loop:m}),p.createElement(u.s.div,(0,h.A)({role:"radiogroup","aria-orientation":f,"aria-labelledby":w,dir:v},g,{ref:t}))))})),T=p.forwardRef(((e,t)=>{const{__scopeRadioGroup:n,disabled:r,...o}=e,i=k("RadioGroupItem",n),s=x(n),u=R(n),c=p.useRef(null),d=(0,l.s)(t,c),v=i.value===o.value,m=p.useRef(!1);return p.useEffect((()=>{const e=e=>{S.includes(e.key)&&(m.current=!0)},t=()=>m.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}}),[]),p.createElement(a.q7,(0,h.A)({asChild:!0},s,{focusable:!r,active:v}),p.createElement(b,(0,h.A)({disabled:r,required:i.required,checked:v},u,o,{name:i.name,ref:d,onCheck:()=>i.onValueChange(o.value),onKeyDown:(0,f.m)((e=>{"Enter"===e.key&&e.preventDefault()})),onFocus:(0,f.m)(o.onFocus,(()=>{var e;m.current&&(null===(e=c.current)||void 0===e||e.click())}))})))})),M=T,I=p.forwardRef(((e,t)=>{const{__scopeRadioGroup:n,...r}=e,o=R(n);return p.createElement(w,(0,h.A)({},o,r,{ref:t}))}))},5666:(e,t,n)=>{"use strict";n.d(t,{RG:()=>g,bL:()=>x,q7:()=>R});var r=n(851),o=n(7775),i=n(5397),s=n(563),a=n(535),u=n(707),c=n(3994),l=n(1575),d=n(1594),f=n(7940);const p={bubbles:!1,cancelable:!0},[h,v,m]=(0,c.N)("RovingFocusGroup"),[y,g]=(0,a.A)("RovingFocusGroup",[m]),[b,w]=y("RovingFocusGroup"),E=d.forwardRef(((e,t)=>d.createElement(h.Provider,{scope:e.__scopeRovingFocusGroup},d.createElement(h.Slot,{scope:e.__scopeRovingFocusGroup},d.createElement(C,(0,f.A)({},e,{ref:t})))))),C=d.forwardRef(((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:s,dir:a="ltr",loop:c=!1,currentTabStopId:h,defaultCurrentTabStopId:m,onCurrentTabStopIdChange:y,onEntryFocus:g,...w}=e,E=d.useRef(null),C=(0,u.s)(t,E),[S=null,O]=(0,r.i)({prop:h,defaultProp:m,onChange:y}),[x,R]=d.useState(!1),P=(0,o.c)(g),k=v(n),D=d.useRef(!1);return d.useEffect((()=>{const e=E.current;if(e)return e.addEventListener("rovingFocusGroup.onEntryFocus",P),()=>e.removeEventListener("rovingFocusGroup.onEntryFocus",P)}),[P]),d.createElement(b,{scope:n,orientation:s,dir:a,loop:c,currentTabStopId:S,onItemFocus:d.useCallback((e=>O(e)),[O]),onItemShiftTab:d.useCallback((()=>R(!0)),[])},d.createElement(i.s.div,(0,f.A)({tabIndex:x?-1:0,"data-orientation":s},w,{ref:C,style:{outline:"none",...e.style},onMouseDown:(0,l.m)(e.onMouseDown,(()=>{D.current=!0})),onFocus:(0,l.m)(e.onFocus,(e=>{const t=!D.current;if(e.target===e.currentTarget&&t&&!x){const t=new Event("rovingFocusGroup.onEntryFocus",p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=k().filter((e=>e.focusable));A([e.find((e=>e.active)),e.find((e=>e.id===S)),...e].filter(Boolean).map((e=>e.ref.current)))}}D.current=!1})),onBlur:(0,l.m)(e.onBlur,(()=>R(!1)))})))})),S=d.forwardRef(((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,...a}=e,u=(0,s.B)(),c=w("RovingFocusGroupItem",n),p=c.currentTabStopId===u,m=v(n);return d.createElement(h.ItemSlot,{scope:n,id:u,focusable:r,active:o},d.createElement(i.s.span,(0,f.A)({tabIndex:p?0:-1,"data-orientation":c.orientation},a,{ref:t,onMouseDown:(0,l.m)(e.onMouseDown,(e=>{r?c.onItemFocus(u):e.preventDefault()})),onFocus:(0,l.m)(e.onFocus,(()=>c.onItemFocus(u))),onKeyDown:(0,l.m)(e.onKeyDown,(e=>{if("Tab"===e.key&&e.shiftKey)return void c.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:O[r]}(e,c.orientation,c.dir);if(void 0!==t){e.preventDefault();let o=m().filter((e=>e.focusable)).map((e=>e.ref.current));if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const i=o.indexOf(e.currentTarget);o=c.loop?(r=i+1,(n=o).map(((e,t)=>n[(r+t)%n.length]))):o.slice(i+1)}setTimeout((()=>A(o)))}var n,r}))})))})),O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e){const t=document.activeElement;for(const n of e){if(n===t)return;if(n.focus(),document.activeElement!==t)return}}const x=E,R=S},9719:(e,t,n)=>{"use strict";n.d(t,{b:()=>l});var r=n(5397),o=n(1594),i=n(7940);const s="horizontal",a=["horizontal","vertical"],u=o.forwardRef(((e,t)=>{const{decorative:n,orientation:a=s,...u}=e,l=c(a)?a:s,d=n?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"};return o.createElement(r.s.div,(0,i.A)({"data-orientation":l},d,u,{ref:t}))}));function c(e){return a.includes(e)}u.propTypes={orientation(e,t,n){const r=e[t],o=String(r);return r&&!c(r)?new Error(function(e,t){return`Invalid prop \`orientation\` of value \`${e}\` supplied to \`${t}\`, expected one of:\n  - horizontal\n  - vertical\n\nDefaulting to \`${s}\`.`}(o,n)):null}};const l=u},3274:(e,t,n)=>{"use strict";n.d(t,{DX:()=>s,xV:()=>u});var r=n(707),o=n(1594),i=n(7940);const s=o.forwardRef(((e,t)=>{const{children:n,...r}=e;return o.Children.toArray(n).some(c)?o.createElement(o.Fragment,null,o.Children.map(n,(e=>c(e)?o.createElement(a,(0,i.A)({},r,{ref:t}),e.props.children):e))):o.createElement(a,(0,i.A)({},r,{ref:t}),n)}));s.displayName="Slot";const a=o.forwardRef(((e,t)=>{const{children:n,...i}=e;return o.isValidElement(n)?o.cloneElement(n,{...l(i,n.props),ref:(0,r.t)(t,n.ref)}):o.Children.count(n)>1?o.Children.only(null):null}));a.displayName="SlotClone";const u=({children:e})=>o.createElement(o.Fragment,null,e);function c(e){return o.isValidElement(e)&&e.type===u}function l(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?n[r]=(...e)=>{null==i||i(...e),null==o||o(...e)}:"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}},7619:(e,t,n)=>{"use strict";n.d(t,{Qg:()=>T,Sb:()=>k,US:()=>S,aD:()=>D,eC:()=>M,tE:()=>E,y8:()=>O});var r=n(8058),o=n(7246),i=n(851),s=n(7775),a=n(5397),u=n(6751),c=n(9320),l=n(3476),d=n(535),f=n(707),p=n(1575),h=n(5206),v=n(1594),m=n(7940);const[y,g]=(0,d.A)("Toast"),[b,w]=y("ToastProvider"),E=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,u]=v.useState(null),[c,l]=v.useState(0),d=v.useRef(!1),f=v.useRef(!1);return v.createElement(b,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:c,viewport:a,onViewportChange:u,onToastAdd:v.useCallback((()=>l((e=>e+1))),[]),onToastRemove:v.useCallback((()=>l((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:f},s)},C=["F8"],S=v.forwardRef(((e,t)=>{const{__scopeToast:n,hotkey:r=C,label:o="Notifications ({hotkey})",...i}=e,s=w("ToastViewport",n),u=v.useRef(null),c=v.useRef(null),d=(0,f.s)(t,c,s.onViewportChange),p=r.join("+").replace(/Key/g,"").replace(/Digit/g,"");return v.useEffect((()=>{const e=e=>{var t;r.every((t=>e[t]||e.code===t))&&(null===(t=c.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[r]),v.useEffect((()=>{const e=u.current,t=c.current;if(e&&t){const n=()=>{const e=new Event("toast.viewportPause");t.dispatchEvent(e),s.isClosePausedRef.current=!0},r=()=>{const e=new Event("toast.viewportResume");t.dispatchEvent(e),s.isClosePausedRef.current=!1};return e.addEventListener("focusin",n),e.addEventListener("focusout",r),e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",r),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}}),[s.isClosePausedRef]),v.useEffect((()=>{const e=c.current;if(e){let t=[];const n=new MutationObserver((n=>{const[r]=n;r.addedNodes.forEach((n=>{t.includes(n)||(e.prepend(n),t=[...t,n])}))}));return n.observe(e,{childList:!0}),()=>n.disconnect()}}),[]),v.createElement(l.lg,{ref:u,role:"region","aria-label":o.replace("{hotkey}",p),tabIndex:-1,style:{pointerEvents:s.toastCount>0?void 0:"none"}},v.createElement(a.s.ol,(0,m.A)({tabIndex:-1},i,{ref:d})))})),O=v.forwardRef(((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...a}=e,[c=!0,l]=(0,i.i)({prop:r,defaultProp:o,onChange:s});return v.createElement(u.C,{present:n||c},v.createElement(R,(0,m.A)({open:c},a,{ref:t,onClose:()=>l(!1),onSwipeStart:(0,p.m)(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:(0,p.m)(e.onSwipeMove,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)})),onSwipeCancel:(0,p.m)(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:(0,p.m)(e.onSwipeEnd,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),l(!1)}))})))})),[A,x]=y("Toast",{isInteractive:!1,onClose(){}}),R=v.forwardRef(((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:u,onEscapeKeyDown:c,onSwipeStart:d,onSwipeMove:y,onSwipeCancel:g,onSwipeEnd:b,...E}=e,C=w("Toast",n),S=v.useRef(null),O=(0,f.s)(t,S),x=v.useRef(null),R=v.useRef(null),k=o||C.duration,D=v.useRef(0),T=v.useRef(k),M=v.useRef(0),{onToastAdd:L,onToastRemove:F}=C,j=(0,s.c)((()=>{var e,t;(null===(e=S.current)||void 0===e?void 0:e.contains(document.activeElement))&&(null===(t=C.viewport)||void 0===t||t.focus()),u()})),N=v.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(M.current),D.current=(new Date).getTime(),M.current=window.setTimeout(j,e))}),[j]);return v.useEffect((()=>{const e=C.viewport;if(e){const t=()=>{N(T.current)},n=()=>{const e=(new Date).getTime()-D.current;T.current=T.current-e,window.clearTimeout(M.current)};return e.addEventListener("toast.viewportPause",n),e.addEventListener("toast.viewportResume",t),()=>{e.removeEventListener("toast.viewportPause",n),e.removeEventListener("toast.viewportResume",t)}}}),[C.viewport,k,N]),v.useEffect((()=>{i&&!C.isClosePausedRef.current&&N(k)}),[i,k,C.isClosePausedRef,N]),v.useEffect((()=>(L(),()=>F())),[L,F]),C.viewport?v.createElement(v.Fragment,null,v.createElement(P,{__scopeToast:n,role:"status","aria-live":"foreground"===r?"assertive":"polite","aria-atomic":!0},e.children),v.createElement(A,{scope:n,isInteractive:!0,onClose:j},h.createPortal(v.createElement(l.bL,{asChild:!0,onEscapeKeyDown:(0,p.m)(c,(()=>{C.isFocusedToastEscapeKeyDownRef.current||j(),C.isFocusedToastEscapeKeyDownRef.current=!1}))},v.createElement(a.s.li,(0,m.A)({role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":C.swipeDirection},E,{ref:O,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,p.m)(e.onKeyDown,(e=>{"Escape"===e.key&&(null==c||c(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,j()))})),onPointerDown:(0,p.m)(e.onPointerDown,(e=>{0===e.button&&(x.current={x:e.clientX,y:e.clientY})})),onPointerMove:(0,p.m)(e.onPointerMove,(e=>{if(!x.current)return;const t=e.clientX-x.current.x,n=e.clientY-x.current.y,r=Boolean(R.current),o=["left","right"].includes(C.swipeDirection),i=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,s=o?i(0,t):0,a=o?0:i(0,n),u="touch"===e.pointerType?10:2,c={x:s,y:a},l={originalEvent:e,delta:c};r?(R.current=c,I("toast.swipeMove",y,l)):_(c,C.swipeDirection,u)?(R.current=c,I("toast.swipeStart",d,l),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(n)>u)&&(x.current=null)})),onPointerUp:(0,p.m)(e.onPointerUp,(e=>{const t=R.current;if(e.target.releasePointerCapture(e.pointerId),R.current=null,x.current=null,t){const n=e.currentTarget,r={originalEvent:e,delta:t};_(t,C.swipeDirection,C.swipeThreshold)?I("toast.swipeEnd",b,r):I("toast.swipeCancel",g,r),n.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))}))),C.viewport))):null}));R.propTypes={type(e){if(e.type&&!["foreground","background"].includes(e.type))throw new Error("Invalid prop `type` supplied to `Toast`. Expected `foreground | background`.");return null}};const P=e=>{const{__scopeToast:t,...n}=e,i=w("Toast",t),[a,u]=v.useState(!1),[l,d]=v.useState(!1);return function(e=()=>{}){const t=(0,s.c)(e);(0,o.N)((()=>{let e=0,n=0;return e=window.requestAnimationFrame((()=>n=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}}),[t])}((()=>u(!0))),v.useEffect((()=>{const e=window.setTimeout((()=>d(!0)),1e3);return()=>window.clearTimeout(e)}),[]),l?null:v.createElement(c.V$,{asChild:!0},v.createElement(r.s,{asChild:!0},v.createElement("div",n,a&&v.createElement(v.Fragment,null,i.label," ",e.children))))},k=v.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e;return v.createElement(a.s.div,(0,m.A)({},r,{ref:t}))})),D=v.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e;return v.createElement(a.s.div,(0,m.A)({},r,{ref:t}))})),T=v.forwardRef(((e,t)=>{const{altText:n,...r}=e,o=x("ToastAction",e.__scopeToast);return n?o.isInteractive?v.createElement(M,(0,m.A)({},r,{ref:t})):v.createElement("span",null,n):null}));T.propTypes={altText(e){if(!e.altText)throw new Error("Missing prop `altText` expected on `ToastAction`");return null}};const M=v.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e,o=x("ToastClose",n);return o.isInteractive?v.createElement(a.s.button,(0,m.A)({type:"button"},r,{ref:t,onClick:(0,p.m)(e.onClick,o.onClose)})):null}));function I(e,t,n){const r=n.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),r.dispatchEvent(o)}const _=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n}},8323:(e,t,n)=>{"use strict";n.d(t,{UC:()=>I,bL:()=>T,i3:()=>_,l9:()=>M});var r=n(563),o=n(8058),i=n(3274),s=n(9320),a=n(1568),u=n(5397),c=n(6751),l=n(8373),d=n(8365),f=n(6697),p=n(851),h=n(535),v=n(707),m=n(1575),y=n(1594),g=n(7940);const[b,w]=(0,h.A)("Tooltip",[a.Bk]),E=(0,a.Bk)(),C=700,[S,O]=b("TooltipProvider",{isOpenDelayed:!0,delayDuration:C,onOpen:()=>{},onClose:()=>{}}),[A,x]=b("Tooltip"),R=y.forwardRef(((e,t)=>{const{__scopeTooltip:n,...r}=e,o=x("TooltipTrigger",n),i=E(n),s=(0,v.s)(t,o.onTriggerChange),c=y.useRef(!1),l=y.useCallback((()=>c.current=!1),[]);return y.useEffect((()=>()=>document.removeEventListener("mouseup",l)),[l]),y.createElement(a.Mz,(0,g.A)({asChild:!0},i),y.createElement(u.s.button,(0,g.A)({"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute},r,{ref:s,onMouseEnter:(0,m.m)(e.onMouseEnter,o.onTriggerEnter),onMouseLeave:(0,m.m)(e.onMouseLeave,o.onClose),onMouseDown:(0,m.m)(e.onMouseDown,(()=>{o.onClose(),c.current=!0,document.addEventListener("mouseup",l,{once:!0})})),onFocus:(0,m.m)(e.onFocus,(()=>{c.current||o.onOpen()})),onBlur:(0,m.m)(e.onBlur,o.onClose),onClick:(0,m.m)(e.onClick,(e=>{0===e.detail&&o.onClose()}))})))})),P=y.forwardRef(((e,t)=>{const{forceMount:n,...r}=e,o=x("TooltipContent",e.__scopeTooltip);return y.createElement(c.C,{present:n||o.open},y.createElement(k,(0,g.A)({ref:t},r)))})),k=y.forwardRef(((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":u,portalled:c=!0,...l}=e,d=x("TooltipContent",n),p=E(n),h=c?s.ZL:y.Fragment,{onClose:v}=d;return(0,f.U)((()=>v())),y.useEffect((()=>(document.addEventListener("tooltip.open",v),()=>document.removeEventListener("tooltip.open",v))),[v]),y.createElement(h,null,y.createElement(D,{__scopeTooltip:n}),y.createElement(a.UC,(0,g.A)({"data-state":d.stateAttribute},p,l,{ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)"}}),y.createElement(i.xV,null,r),y.createElement(o.b,{id:d.contentId,role:"tooltip"},u||r)))}));function D(e){const{__scopeTooltip:t}=e,n=x("CheckTriggerMoved",t),r=(0,l.y)(n.trigger),o=null==r?void 0:r.left,i=(0,d.Z)(o),s=null==r?void 0:r.top,a=(0,d.Z)(s),u=n.onClose;return y.useEffect((()=>{(void 0!==i&&i!==o||void 0!==a&&a!==s)&&u()}),[u,i,a,o,s]),null}const T=e=>{const{__scopeTooltip:t,children:n,open:o,defaultOpen:i=!1,onOpenChange:s,delayDuration:u}=e,c=O("Tooltip",t),l=E(t),[d,f]=y.useState(null),h=(0,r.B)(),v=y.useRef(0),m=null!=u?u:c.delayDuration,g=y.useRef(!1),{onOpen:b,onClose:w}=c,[C=!1,S]=(0,p.i)({prop:o,defaultProp:i,onChange:e=>{e&&(document.dispatchEvent(new CustomEvent("tooltip.open")),b()),null==s||s(e)}}),x=y.useMemo((()=>C?g.current?"delayed-open":"instant-open":"closed"),[C]),R=y.useCallback((()=>{window.clearTimeout(v.current),g.current=!1,S(!0)}),[S]),P=y.useCallback((()=>{window.clearTimeout(v.current),v.current=window.setTimeout((()=>{g.current=!0,S(!0)}),m)}),[m,S]);return y.useEffect((()=>()=>window.clearTimeout(v.current)),[]),y.createElement(a.bL,l,y.createElement(A,{scope:t,contentId:h,open:C,stateAttribute:x,trigger:d,onTriggerChange:f,onTriggerEnter:y.useCallback((()=>{c.isOpenDelayed?P():R()}),[c.isOpenDelayed,P,R]),onOpen:y.useCallback(R,[R]),onClose:y.useCallback((()=>{window.clearTimeout(v.current),S(!1),w()}),[S,w])},n))},M=R,I=P,_=y.forwardRef(((e,t)=>{const{__scopeTooltip:n,...r}=e,o=E(n);return y.createElement(a.i3,(0,g.A)({},o,r,{ref:t}))}))},7775:(e,t,n)=>{"use strict";n.d(t,{c:()=>o});var r=n(1594);function o(e){const t=r.useRef(e);return r.useEffect((()=>{t.current=e})),r.useMemo((()=>(...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)}),[])}},851:(e,t,n)=>{"use strict";n.d(t,{i:()=>i});var r=n(7775),o=n(1594);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){const[i,s]=function({defaultProp:e,onChange:t}){const n=o.useState(e),[i]=n,s=o.useRef(i),a=(0,r.c)(t);return o.useEffect((()=>{s.current!==i&&(a(i),s.current=i)}),[i,s,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,u=a?e:i,c=(0,r.c)(n);return[u,o.useCallback((t=>{if(a){const n="function"==typeof t?t(e):t;n!==e&&c(n)}else s(t)}),[a,e,s,c])]}},6697:(e,t,n)=>{"use strict";n.d(t,{U:()=>i});var r=n(7775),o=n(1594);function i(e){const t=(0,r.c)(e);o.useEffect((()=>{const e=e=>{"Escape"===e.key&&t(e)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[t])}},7246:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});var r=n(1594);const o=Boolean(null===globalThis||void 0===globalThis?void 0:globalThis.document)?r.useLayoutEffect:()=>{}},8365:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(1594);function o(e){const t=r.useRef({value:e,previous:e});return r.useMemo((()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous)),[e])}},8373:(e,t,n)=>{"use strict";function r(e,t){const n=i.get(e);return void 0===n?(i.set(e,{rect:{},callbacks:[t]}),1===i.size&&(o=requestAnimationFrame(s))):(n.callbacks.push(t),t(e.getBoundingClientRect())),()=>{const n=i.get(e);if(void 0===n)return;const r=n.callbacks.indexOf(t);r>-1&&n.callbacks.splice(r,1),0===n.callbacks.length&&(i.delete(e),0===i.size&&cancelAnimationFrame(o))}}let o;n.d(t,{y:()=>u});const i=new Map;function s(){const e=[];i.forEach(((t,n)=>{const r=n.getBoundingClientRect();var o,i;i=r,((o=t.rect).width!==i.width||o.height!==i.height||o.top!==i.top||o.right!==i.right||o.bottom!==i.bottom||o.left!==i.left)&&(t.rect=r,e.push(t))})),e.forEach((e=>{e.callbacks.forEach((t=>t(e.rect)))})),o=requestAnimationFrame(s)}var a=n(1594);function u(e){const[t,n]=a.useState();return a.useEffect((()=>{if(e){const t=r(e,n);return()=>{n(void 0),t()}}}),[e]),t}},6785:(e,t,n)=>{"use strict";n.d(t,{X:()=>o});var r=n(1594);function o(e){const[t,n]=r.useState(void 0);return r.useEffect((()=>{if(e){const t=new ResizeObserver((t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let o,i;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else{const t=e.getBoundingClientRect();o=t.width,i=t.height}n({width:o,height:i})}));return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)}),[e]),t}},8058:(e,t,n)=>{"use strict";n.d(t,{b:()=>a,s:()=>s});var r=n(5397),o=n(1594),i=n(7940);const s=o.forwardRef(((e,t)=>o.createElement(r.s.span,(0,i.A)({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})))),a=s},5031:(e,t,n)=>{"use strict";n.d(t,{Eq:()=>l});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,s={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=u(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);s[n]||(s[n]=new WeakMap);var l=s[n],d=[],f=new Set,p=new Set(c),h=function(e){e&&!f.has(e)&&(f.add(e),h(e.parentNode))};c.forEach(h);var v=function(e){e&&!p.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(f.has(e))v(e);else{var t=e.getAttribute(r),s=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(l.get(e)||0)+1;o.set(e,a),l.set(e,u),d.push(e),1===a&&s&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),s||e.setAttribute(r,"true")}}))};return v(t),f.clear(),a++,function(){d.forEach((function(e){var t=o.get(e)-1,s=l.get(e)-1;o.set(e,t),l.set(e,s),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),s||e.removeAttribute(n)})),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,s={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},8859:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function o(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}n.r(t),n.d(t,{clsx:()=>o,default:()=>i});const i=o},2396:(e,t,n)=>{"use strict";var r=n(4686),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function u(e){return r.isMemo(e)?s:a[e.$$typeof]||o}a[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[r.Memo]=s;var c=Object.defineProperty,l=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(h){var o=p(n);o&&o!==h&&e(t,o,r)}var s=l(n);d&&(s=s.concat(d(n)));for(var a=u(t),v=u(n),m=0;m<s.length;++m){var y=s[m];if(!(i[y]||r&&r[y]||v&&v[y]||a&&a[y])){var g=f(n,y);try{c(t,y,g)}catch(e){}}}}return t}},946:(e,t)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,s=n?Symbol.for("react.strict_mode"):60108,a=n?Symbol.for("react.profiler"):60114,u=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,l=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,v=n?Symbol.for("react.memo"):60115,m=n?Symbol.for("react.lazy"):60116,y=n?Symbol.for("react.block"):60121,g=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function E(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case l:case d:case i:case a:case s:case p:return e;default:switch(e=e&&e.$$typeof){case c:case f:case m:case v:case u:return e;default:return t}}case o:return t}}}function C(e){return E(e)===d}t.AsyncMode=l,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=u,t.Element=r,t.ForwardRef=f,t.Fragment=i,t.Lazy=m,t.Memo=v,t.Portal=o,t.Profiler=a,t.StrictMode=s,t.Suspense=p,t.isAsyncMode=function(e){return C(e)||E(e)===l},t.isConcurrentMode=C,t.isContextConsumer=function(e){return E(e)===c},t.isContextProvider=function(e){return E(e)===u},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return E(e)===f},t.isFragment=function(e){return E(e)===i},t.isLazy=function(e){return E(e)===m},t.isMemo=function(e){return E(e)===v},t.isPortal=function(e){return E(e)===o},t.isProfiler=function(e){return E(e)===a},t.isStrictMode=function(e){return E(e)===s},t.isSuspense=function(e){return E(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===d||e===a||e===s||e===p||e===h||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===v||e.$$typeof===u||e.$$typeof===c||e.$$typeof===f||e.$$typeof===g||e.$$typeof===b||e.$$typeof===w||e.$$typeof===y)},t.typeOf=E},4686:(e,t,n)=>{"use strict";e.exports=n(946)},856:(e,t,n)=>{"use strict";var r=n(7183);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,s){if(s!==r){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},7598:(e,t,n)=>{e.exports=n(856)()},7183:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},8105:(e,t,n)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraggableCore",{enumerable:!0,get:function(){return d.default}}),t.default=void 0;var o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=v(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var a=i?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(o,s,a):o[s]=e[s]}o.default=e,n&&n.set(e,o);return o}(n(1594)),i=h(n(7598)),s=h(n(5206)),a=h(n(8859)),u=n(1691),c=n(5272),l=n(5214),d=h(n(2634)),f=h(n(350)),p=["axis","bounds","children","defaultPosition","defaultClassName","defaultClassNameDragging","defaultClassNameDragged","position","positionOffset","scale"];function h(e){return e&&e.__esModule?e:{default:e}}function v(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(v=function(e){return e?n:t})(e)}function m(){return m=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},m.apply(this,arguments)}function y(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){R(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],s=!0,a=!1;try{for(n=n.call(e);!(s=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);s=!0);}catch(e){a=!0,o=e}finally{try{s||null==n.return||n.return()}finally{if(a)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return E(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return E(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function C(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function S(e,t){return S=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},S(e,t)}function O(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=x(e);if(t){var i=x(this).constructor;n=Reflect.construct(o,arguments,i)}else n=o.apply(this,arguments);return function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return A(e)}(this,n)}}function A(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x(e){return x=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},x(e)}function R(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var P=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&S(e,t)}(l,e);var t,n,r,i=O(l);function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),R(A(t=i.call(this,e)),"onDragStart",(function(e,n){if((0,f.default)("Draggable: onDragStart: %j",n),!1===t.props.onStart(e,(0,c.createDraggableData)(A(t),n)))return!1;t.setState({dragging:!0,dragged:!0})})),R(A(t),"onDrag",(function(e,n){if(!t.state.dragging)return!1;(0,f.default)("Draggable: onDrag: %j",n);var r=(0,c.createDraggableData)(A(t),n),o={x:r.x,y:r.y};if(t.props.bounds){var i=o.x,s=o.y;o.x+=t.state.slackX,o.y+=t.state.slackY;var a=w((0,c.getBoundPosition)(A(t),o.x,o.y),2),u=a[0],l=a[1];o.x=u,o.y=l,o.slackX=t.state.slackX+(i-o.x),o.slackY=t.state.slackY+(s-o.y),r.x=o.x,r.y=o.y,r.deltaX=o.x-t.state.x,r.deltaY=o.y-t.state.y}if(!1===t.props.onDrag(e,r))return!1;t.setState(o)})),R(A(t),"onDragStop",(function(e,n){if(!t.state.dragging)return!1;if(!1===t.props.onStop(e,(0,c.createDraggableData)(A(t),n)))return!1;(0,f.default)("Draggable: onDragStop: %j",n);var r={dragging:!1,slackX:0,slackY:0};if(Boolean(t.props.position)){var o=t.props.position,i=o.x,s=o.y;r.x=i,r.y=s}t.setState(r)})),t.state={dragging:!1,dragged:!1,x:e.position?e.position.x:e.defaultPosition.x,y:e.position?e.position.y:e.defaultPosition.y,prevPropsPosition:b({},e.position),slackX:0,slackY:0,isElementSVG:!1},!e.position||e.onDrag||e.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element."),t}return t=l,r=[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.position,r=t.prevPropsPosition;return!n||r&&n.x===r.x&&n.y===r.y?null:((0,f.default)("Draggable: getDerivedStateFromProps %j",{position:n,prevPropsPosition:r}),{x:n.x,y:n.y,prevPropsPosition:b({},n)})}}],(n=[{key:"componentDidMount",value:function(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}},{key:"componentWillUnmount",value:function(){this.setState({dragging:!1})}},{key:"findDOMNode",value:function(){var e,t,n;return null!==(e=null===(t=this.props)||void 0===t||null===(n=t.nodeRef)||void 0===n?void 0:n.current)&&void 0!==e?e:s.default.findDOMNode(this)}},{key:"render",value:function(){var e,t=this.props,n=(t.axis,t.bounds,t.children),r=t.defaultPosition,i=t.defaultClassName,s=t.defaultClassNameDragging,l=t.defaultClassNameDragged,f=t.position,h=t.positionOffset,v=(t.scale,y(t,p)),g={},w=null,E=!Boolean(f)||this.state.dragging,C=f||r,S={x:(0,c.canDragX)(this)&&E?this.state.x:C.x,y:(0,c.canDragY)(this)&&E?this.state.y:C.y};this.state.isElementSVG?w=(0,u.createSVGTransform)(S,h):g=(0,u.createCSSTransform)(S,h);var O=(0,a.default)(n.props.className||"",i,(R(e={},s,this.state.dragging),R(e,l,this.state.dragged),e));return o.createElement(d.default,m({},v,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),o.cloneElement(o.Children.only(n),{className:O,style:b(b({},n.props.style),g),transform:w}))}}])&&C(t.prototype,n),r&&C(t,r),Object.defineProperty(t,"prototype",{writable:!1}),l}(o.Component);t.default=P,R(P,"displayName","Draggable"),R(P,"propTypes",b(b({},d.default.propTypes),{},{axis:i.default.oneOf(["both","x","y","none"]),bounds:i.default.oneOfType([i.default.shape({left:i.default.number,right:i.default.number,top:i.default.number,bottom:i.default.number}),i.default.string,i.default.oneOf([!1])]),defaultClassName:i.default.string,defaultClassNameDragging:i.default.string,defaultClassNameDragged:i.default.string,defaultPosition:i.default.shape({x:i.default.number,y:i.default.number}),positionOffset:i.default.shape({x:i.default.oneOfType([i.default.number,i.default.string]),y:i.default.oneOfType([i.default.number,i.default.string])}),position:i.default.shape({x:i.default.number,y:i.default.number}),className:l.dontSetMe,style:l.dontSetMe,transform:l.dontSetMe})),R(P,"defaultProps",b(b({},d.default.defaultProps),{},{axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1}))},2634:(e,t,n)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=f(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var a=i?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(o,s,a):o[s]=e[s]}o.default=e,n&&n.set(e,o);return o}(n(1594)),i=d(n(7598)),s=d(n(5206)),a=n(1691),u=n(5272),c=n(5214),l=d(n(350));function d(e){return e&&e.__esModule?e:{default:e}}function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(f=function(e){return e?n:t})(e)}function p(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],s=!0,a=!1;try{for(n=n.call(e);!(s=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);s=!0);}catch(e){a=!0,o=e}finally{try{s||null==n.return||n.return()}finally{if(a)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function m(e,t){return m=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},m(e,t)}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=b(e);if(t){var i=b(this).constructor;n=Reflect.construct(o,arguments,i)}else n=o.apply(this,arguments);return function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return g(e)}(this,n)}}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function b(e){return b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},b(e)}function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var E={start:"touchstart",move:"touchmove",stop:"touchend"},C={start:"mousedown",move:"mousemove",stop:"mouseup"},S=C,O=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&m(e,t)}(c,e);var t,n,r,i=y(c);function c(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return w(g(e=i.call.apply(i,[this].concat(n))),"state",{dragging:!1,lastX:NaN,lastY:NaN,touchIdentifier:null}),w(g(e),"mounted",!1),w(g(e),"handleDragStart",(function(t){if(e.props.onMouseDown(t),!e.props.allowAnyClick&&"number"==typeof t.button&&0!==t.button)return!1;var n=e.findDOMNode();if(!n||!n.ownerDocument||!n.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");var r=n.ownerDocument;if(!(e.props.disabled||!(t.target instanceof r.defaultView.Node)||e.props.handle&&!(0,a.matchesSelectorAndParentsTo)(t.target,e.props.handle,n)||e.props.cancel&&(0,a.matchesSelectorAndParentsTo)(t.target,e.props.cancel,n))){"touchstart"===t.type&&t.preventDefault();var o=(0,a.getTouchIdentifier)(t);e.setState({touchIdentifier:o});var i=(0,u.getControlPosition)(t,o,g(e));if(null!=i){var s=i.x,c=i.y,d=(0,u.createCoreData)(g(e),s,c);(0,l.default)("DraggableCore: handleDragStart: %j",d),(0,l.default)("calling",e.props.onStart),!1!==e.props.onStart(t,d)&&!1!==e.mounted&&(e.props.enableUserSelectHack&&(0,a.addUserSelectStyles)(r),e.setState({dragging:!0,lastX:s,lastY:c}),(0,a.addEvent)(r,S.move,e.handleDrag),(0,a.addEvent)(r,S.stop,e.handleDragStop))}}})),w(g(e),"handleDrag",(function(t){var n=(0,u.getControlPosition)(t,e.state.touchIdentifier,g(e));if(null!=n){var r=n.x,o=n.y;if(Array.isArray(e.props.grid)){var i=r-e.state.lastX,s=o-e.state.lastY,a=p((0,u.snapToGrid)(e.props.grid,i,s),2);if(i=a[0],s=a[1],!i&&!s)return;r=e.state.lastX+i,o=e.state.lastY+s}var c=(0,u.createCoreData)(g(e),r,o);if((0,l.default)("DraggableCore: handleDrag: %j",c),!1!==e.props.onDrag(t,c)&&!1!==e.mounted)e.setState({lastX:r,lastY:o});else try{e.handleDragStop(new MouseEvent("mouseup"))}catch(t){var d=document.createEvent("MouseEvents");d.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),e.handleDragStop(d)}}})),w(g(e),"handleDragStop",(function(t){if(e.state.dragging){var n=(0,u.getControlPosition)(t,e.state.touchIdentifier,g(e));if(null!=n){var r=n.x,o=n.y;if(Array.isArray(e.props.grid)){var i=r-e.state.lastX||0,s=o-e.state.lastY||0,c=p((0,u.snapToGrid)(e.props.grid,i,s),2);i=c[0],s=c[1],r=e.state.lastX+i,o=e.state.lastY+s}var d=(0,u.createCoreData)(g(e),r,o);if(!1===e.props.onStop(t,d)||!1===e.mounted)return!1;var f=e.findDOMNode();f&&e.props.enableUserSelectHack&&(0,a.removeUserSelectStyles)(f.ownerDocument),(0,l.default)("DraggableCore: handleDragStop: %j",d),e.setState({dragging:!1,lastX:NaN,lastY:NaN}),f&&((0,l.default)("DraggableCore: Removing handlers"),(0,a.removeEvent)(f.ownerDocument,S.move,e.handleDrag),(0,a.removeEvent)(f.ownerDocument,S.stop,e.handleDragStop))}}})),w(g(e),"onMouseDown",(function(t){return S=C,e.handleDragStart(t)})),w(g(e),"onMouseUp",(function(t){return S=C,e.handleDragStop(t)})),w(g(e),"onTouchStart",(function(t){return S=E,e.handleDragStart(t)})),w(g(e),"onTouchEnd",(function(t){return S=E,e.handleDragStop(t)})),e}return t=c,(n=[{key:"componentDidMount",value:function(){this.mounted=!0;var e=this.findDOMNode();e&&(0,a.addEvent)(e,E.start,this.onTouchStart,{passive:!1})}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.findDOMNode();if(e){var t=e.ownerDocument;(0,a.removeEvent)(t,C.move,this.handleDrag),(0,a.removeEvent)(t,E.move,this.handleDrag),(0,a.removeEvent)(t,C.stop,this.handleDragStop),(0,a.removeEvent)(t,E.stop,this.handleDragStop),(0,a.removeEvent)(e,E.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,a.removeUserSelectStyles)(t)}}},{key:"findDOMNode",value:function(){var e,t,n;return null!==(e=this.props)&&void 0!==e&&e.nodeRef?null===(t=this.props)||void 0===t||null===(n=t.nodeRef)||void 0===n?void 0:n.current:s.default.findDOMNode(this)}},{key:"render",value:function(){return o.cloneElement(o.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}])&&v(t.prototype,n),r&&v(t,r),Object.defineProperty(t,"prototype",{writable:!1}),c}(o.Component);t.default=O,w(O,"displayName","DraggableCore"),w(O,"propTypes",{allowAnyClick:i.default.bool,disabled:i.default.bool,enableUserSelectHack:i.default.bool,offsetParent:function(e,t){if(e[t]&&1!==e[t].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:i.default.arrayOf(i.default.number),handle:i.default.string,cancel:i.default.string,nodeRef:i.default.object,onStart:i.default.func,onDrag:i.default.func,onStop:i.default.func,onMouseDown:i.default.func,scale:i.default.number,className:c.dontSetMe,style:c.dontSetMe,transform:c.dontSetMe}),w(O,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},9460:(e,t,n)=>{"use strict";var r=n(8105),o=r.default,i=r.DraggableCore;e.exports=o,e.exports.default=o,e.exports.DraggableCore=i},1691:(e,t,n)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.addClassName=p,t.addEvent=function(e,t,n,r){if(!e)return;var o=u({capture:!0},r);e.addEventListener?e.addEventListener(t,n,o):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n},t.addUserSelectStyles=function(e){if(!e)return;var t=e.getElementById("react-draggable-style-el");t||((t=e.createElement("style")).type="text/css",t.id="react-draggable-style-el",t.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",t.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",e.getElementsByTagName("head")[0].appendChild(t));e.body&&p(e.body,"react-draggable-transparent-selection")},t.createCSSTransform=function(e,t){var n=f(e,t,"px");return c({},(0,i.browserPrefixToKey)("transform",i.default),n)},t.createSVGTransform=function(e,t){return f(e,t,"")},t.getTouch=function(e,t){return e.targetTouches&&(0,o.findInArray)(e.targetTouches,(function(e){return t===e.identifier}))||e.changedTouches&&(0,o.findInArray)(e.changedTouches,(function(e){return t===e.identifier}))},t.getTouchIdentifier=function(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier},t.getTranslation=f,t.innerHeight=function(e){var t=e.clientHeight,n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,o.int)(n.paddingTop),t-=(0,o.int)(n.paddingBottom)},t.innerWidth=function(e){var t=e.clientWidth,n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,o.int)(n.paddingLeft),t-=(0,o.int)(n.paddingRight)},t.matchesSelector=d,t.matchesSelectorAndParentsTo=function(e,t,n){var r=e;do{if(d(r,t))return!0;if(r===n)return!1;r=r.parentNode}while(r);return!1},t.offsetXYFromParent=function(e,t,n){var r=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect(),o=(e.clientX+t.scrollLeft-r.left)/n,i=(e.clientY+t.scrollTop-r.top)/n;return{x:o,y:i}},t.outerHeight=function(e){var t=e.clientHeight,n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,o.int)(n.borderTopWidth),t+=(0,o.int)(n.borderBottomWidth)},t.outerWidth=function(e){var t=e.clientWidth,n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,o.int)(n.borderLeftWidth),t+=(0,o.int)(n.borderRightWidth)},t.removeClassName=h,t.removeEvent=function(e,t,n,r){if(!e)return;var o=u({capture:!0},r);e.removeEventListener?e.removeEventListener(t,n,o):e.detachEvent?e.detachEvent("on"+t,n):e["on"+t]=null},t.removeUserSelectStyles=function(e){if(!e)return;try{if(e.body&&h(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{var t=(e.defaultView||window).getSelection();t&&"Caret"!==t.type&&t.removeAllRanges()}}catch(e){}};var o=n(5214),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var n=s(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var u=i?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(o,a,u):o[a]=e[a]}o.default=e,n&&n.set(e,o);return o}(n(6628));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(s=function(e){return e?n:t})(e)}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var l="";function d(e,t){return l||(l=(0,o.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(t){return(0,o.isFunction)(e[t])}))),!!(0,o.isFunction)(e[l])&&e[l](t)}function f(e,t,n){var r=e.x,o=e.y,i="translate(".concat(r).concat(n,",").concat(o).concat(n,")");if(t){var s="".concat("string"==typeof t.x?t.x:t.x+n),a="".concat("string"==typeof t.y?t.y:t.y+n);i="translate(".concat(s,", ").concat(a,")")+i}return i}function p(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)")))||(e.className+=" ".concat(t))}function h(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)"),"g"),"")}},6628:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.browserPrefixToKey=o,t.browserPrefixToStyle=function(e,t){return t?"-".concat(t.toLowerCase(),"-").concat(e):e},t.default=void 0,t.getPrefix=r;var n=["Moz","Webkit","O","ms"];function r(){var e,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";var i=null===(e=window.document)||void 0===e||null===(t=e.documentElement)||void 0===t?void 0:t.style;if(!i)return"";if(r in i)return"";for(var s=0;s<n.length;s++)if(o(r,n[s])in i)return n[s];return""}function o(e,t){return t?"".concat(t).concat(function(e){for(var t="",n=!0,r=0;r<e.length;r++)n?(t+=e[r].toUpperCase(),n=!1):"-"===e[r]?n=!0:t+=e[r];return t}(e)):e}var i=r();t.default=i},350:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){0}},5272:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.canDragX=function(e){return"both"===e.props.axis||"x"===e.props.axis},t.canDragY=function(e){return"both"===e.props.axis||"y"===e.props.axis},t.createCoreData=function(e,t,n){var o=e.state,s=!(0,r.isNum)(o.lastX),a=i(e);return s?{node:a,deltaX:0,deltaY:0,lastX:t,lastY:n,x:t,y:n}:{node:a,deltaX:t-o.lastX,deltaY:n-o.lastY,lastX:o.lastX,lastY:o.lastY,x:t,y:n}},t.createDraggableData=function(e,t){var n=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/n,y:e.state.y+t.deltaY/n,deltaX:t.deltaX/n,deltaY:t.deltaY/n,lastX:e.state.x,lastY:e.state.y}},t.getBoundPosition=function(e,t,n){if(!e.props.bounds)return[t,n];var s=e.props.bounds;s="string"==typeof s?s:function(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}(s);var a=i(e);if("string"==typeof s){var u,c=a.ownerDocument,l=c.defaultView;if(!((u="parent"===s?a.parentNode:c.querySelector(s))instanceof l.HTMLElement))throw new Error('Bounds selector "'+s+'" could not find an element.');var d=u,f=l.getComputedStyle(a),p=l.getComputedStyle(d);s={left:-a.offsetLeft+(0,r.int)(p.paddingLeft)+(0,r.int)(f.marginLeft),top:-a.offsetTop+(0,r.int)(p.paddingTop)+(0,r.int)(f.marginTop),right:(0,o.innerWidth)(d)-(0,o.outerWidth)(a)-a.offsetLeft+(0,r.int)(p.paddingRight)-(0,r.int)(f.marginRight),bottom:(0,o.innerHeight)(d)-(0,o.outerHeight)(a)-a.offsetTop+(0,r.int)(p.paddingBottom)-(0,r.int)(f.marginBottom)}}(0,r.isNum)(s.right)&&(t=Math.min(t,s.right));(0,r.isNum)(s.bottom)&&(n=Math.min(n,s.bottom));(0,r.isNum)(s.left)&&(t=Math.max(t,s.left));(0,r.isNum)(s.top)&&(n=Math.max(n,s.top));return[t,n]},t.getControlPosition=function(e,t,n){var r="number"==typeof t?(0,o.getTouch)(e,t):null;if("number"==typeof t&&!r)return null;var s=i(n),a=n.props.offsetParent||s.offsetParent||s.ownerDocument.body;return(0,o.offsetXYFromParent)(r||e,a,n.props.scale)},t.snapToGrid=function(e,t,n){var r=Math.round(t/e[0])*e[0],o=Math.round(n/e[1])*e[1];return[r,o]};var r=n(5214),o=n(1691);function i(e){var t=e.findDOMNode();if(!t)throw new Error("<DraggableCore>: Unmounted during event!");return t}},5214:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dontSetMe=function(e,t,n){if(e[t])return new Error("Invalid prop ".concat(t," passed to ").concat(n," - do not set this, set it on the child."))},t.findInArray=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t.apply(t,[e[n],n,e]))return e[n]},t.int=function(e){return parseInt(e,10)},t.isFunction=function(e){return"function"==typeof e||"[object Function]"===Object.prototype.toString.call(e)},t.isNum=function(e){return"number"==typeof e&&!isNaN(e)}},4505:(e,t)=>{"use strict";var n,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen");function y(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case a:case s:case f:case p:return e;default:switch(e=e&&e.$$typeof){case l:case c:case d:case v:case h:case u:return e;default:return t}}case o:return t}}}n=Symbol.for("react.module.reference"),t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===a||e===s||e===f||e===p||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===v||e.$$typeof===h||e.$$typeof===u||e.$$typeof===c||e.$$typeof===d||e.$$typeof===n||void 0!==e.getModuleId)},t.typeOf=y},261:(e,t,n)=>{"use strict";e.exports=n(4505)},5534:e=>{e.exports={ReactQueryDevtools:function(){return null},ReactQueryDevtoolsPanel:function(){return null}}},3811:(e,t,n)=>{"use strict";n.d(t,{m:()=>s});var r=n(9408),o=n(8937),i=n(8059),s=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?n.setFocused(e):n.onFocus()}))},n.setFocused=function(e){this.focused=e,e&&this.onFocus()},n.onFocus=function(){this.listeners.forEach((function(e){e()}))},n.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},t}(o.Q))},4746:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.E});var r=n(9928),o=n(4231);n.o(o,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return o.QueryClientProvider}}),n.o(o,"useMutation")&&n.d(t,{useMutation:function(){return o.useMutation}}),n.o(o,"useQuery")&&n.d(t,{useQuery:function(){return o.useQuery}}),n.o(o,"useQueryClient")&&n.d(t,{useQueryClient:function(){return o.useQueryClient}})},416:(e,t,n)=>{"use strict";n.d(t,{B:()=>i,t:()=>o});var r=console;function o(){return r}function i(e){r=e}},3959:(e,t,n)=>{"use strict";n.d(t,{$:()=>c,s:()=>u});var r=n(7940),o=n(416),i=n(3582),s=n(8671),a=n(8059),u=function(){function e(e){this.options=(0,r.A)({},e.defaultOptions,e.options),this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.observers=[],this.state=e.state||c(),this.meta=e.meta}var t=e.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){-1===this.observers.indexOf(e)&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter((function(t){return t!==e}))},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(a.lQ).catch(a.lQ)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e,t=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then((function(){null==t.mutationCache.config.onMutate||t.mutationCache.config.onMutate(t.state.variables,t)})).then((function(){return null==t.options.onMutate?void 0:t.options.onMutate(t.state.variables)})).then((function(e){e!==t.state.context&&t.dispatch({type:"loading",context:e,variables:t.state.variables})}))),r.then((function(){return t.executeMutation()})).then((function(n){e=n,null==t.mutationCache.config.onSuccess||t.mutationCache.config.onSuccess(e,t.state.variables,t.state.context,t)})).then((function(){return null==t.options.onSuccess?void 0:t.options.onSuccess(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(e,null,t.state.variables,t.state.context)})).then((function(){return t.dispatch({type:"success",data:e}),e})).catch((function(e){return null==t.mutationCache.config.onError||t.mutationCache.config.onError(e,t.state.variables,t.state.context,t),(0,o.t)().error(e),Promise.resolve().then((function(){return null==t.options.onError?void 0:t.options.onError(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(void 0,e,t.state.variables,t.state.context)})).then((function(){throw t.dispatch({type:"error",error:e}),e}))}))},t.executeMutation=function(){var e,t=this;return this.retryer=new s.eJ({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var t=this;this.state=function(e,t){switch(t.type){case"failed":return(0,r.A)({},e,{failureCount:e.failureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"loading":return(0,r.A)({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return(0,r.A)({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.A)({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.A)({},e,t.state);default:return e}}(this.state,e),i.j.batch((function(){t.observers.forEach((function(t){t.onMutationUpdate(e)})),t.mutationCache.notify(t)}))},e}();function c(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}},3582:(e,t,n)=>{"use strict";n.d(t,{j:()=>o});var r=n(8059),o=new(function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(e){e()},this.batchNotifyFn=function(e){e()}}var t=e.prototype;return t.batch=function(e){var t;this.transactions++;try{t=e()}finally{this.transactions--,this.transactions||this.flush()}return t},t.schedule=function(e){var t=this;this.transactions?this.queue.push(e):(0,r.G6)((function(){t.notifyFn(e)}))},t.batchCalls=function(e){var t=this;return function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];t.schedule((function(){e.apply(void 0,r)}))}},t.flush=function(){var e=this,t=this.queue;this.queue=[],t.length&&(0,r.G6)((function(){e.batchNotifyFn((function(){t.forEach((function(t){e.notifyFn(t)}))}))}))},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},e}())},7208:(e,t,n)=>{"use strict";n.d(t,{t:()=>s});var r=n(9408),o=n(8937),i=n(8059),s=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?n.setOnline(e):n.onOnline()}))},n.setOnline=function(e){this.online=e,e&&this.onOnline()},n.onOnline=function(){this.listeners.forEach((function(e){e()}))},n.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},t}(o.Q))},9928:(e,t,n)=>{"use strict";n.d(t,{E:()=>g});var r=n(7940),o=n(8059),i=n(9408),s=n(3582),a=n(416),u=n(8671),c=function(){function e(e){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=e.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(e){var t;this.options=(0,r.A)({},this.defaultOptions,e),this.meta=null==e?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(t=this.options.cacheTime)?t:3e5)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e=this;this.clearGcTimeout(),(0,o.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){e.optionalRemove()}),this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,t){var n,r,i=this.state.data,s=(0,o.Zw)(e,i);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,i,s))?s=i:!1!==this.options.structuralSharing&&(s=(0,o.BH)(i,s)),this.dispatch({data:s,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt}),s},t.setState=function(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})},t.cancel=function(e){var t,n=this.promise;return null==(t=this.retryer)||t.cancel(e),n?n.then(o.lQ).catch(o.lQ):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some((function(e){return!1!==e.options.enabled}))},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(e){return e.getCurrentResult().isStale}))},t.isStaleByTime=function(e){return void 0===e&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,o.j3)(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnWindowFocus()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.onOnline=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnReconnect()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.addObserver=function(e){-1===this.observers.indexOf(e)&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){-1!==this.observers.indexOf(e)&&(this.observers=this.observers.filter((function(t){return t!==e})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,t){var n,r,i,s=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var c;return null==(c=this.retryer)||c.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){var l=this.observers.find((function(e){return e.options.queryFn}));l&&this.setOptions(l.options)}var d=(0,o.HN)(this.queryKey),f=(0,o.jY)(),p={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(p,"signal",{enumerable:!0,get:function(){if(f)return s.abortSignalConsumed=!0,f.signal}});var h,v,m={fetchOptions:t,options:this.options,queryKey:d,state:this.state,fetchFn:function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(p)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(h=this.options.behavior)||h.onFetch(m));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=m.fetchOptions)?void 0:r.meta))||this.dispatch({type:"fetch",meta:null==(v=m.fetchOptions)?void 0:v.meta});return this.retryer=new u.eJ({fn:m.fetchFn,abort:null==f||null==(i=f.abort)?void 0:i.bind(f),onSuccess:function(e){s.setData(e),null==s.cache.config.onSuccess||s.cache.config.onSuccess(e,s),0===s.cacheTime&&s.optionalRemove()},onError:function(e){(0,u.wm)(e)&&e.silent||s.dispatch({type:"error",error:e}),(0,u.wm)(e)||(null==s.cache.config.onError||s.cache.config.onError(e,s),(0,a.t)().error(e)),0===s.cacheTime&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:m.options.retry,retryDelay:m.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var t=this;this.state=this.reducer(this.state,e),s.j.batch((function(){t.observers.forEach((function(t){t.onQueryUpdate(e)})),t.cache.notify({query:t,type:"queryUpdated",action:e})}))},t.getDefaultState=function(e){var t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==e.initialData?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,r=void 0!==t;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},t.reducer=function(e,t){var n,o;switch(t.type){case"failed":return(0,r.A)({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"fetch":return(0,r.A)({},e,{fetchFailureCount:0,fetchMeta:null!=(n=t.meta)?n:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,r.A)({},e,{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(o=t.dataUpdatedAt)?o:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var i=t.error;return(0,u.wm)(i)&&i.revert&&this.revertState?(0,r.A)({},this.revertState):(0,r.A)({},e,{error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,r.A)({},e,{isInvalidated:!0});case"setState":return(0,r.A)({},e,t.state);default:return e}},e}(),l=n(8937),d=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.queries=[],n.queriesMap={},n}(0,i.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r,i=t.queryKey,s=null!=(r=t.queryHash)?r:(0,o.F$)(i,t),a=this.get(s);return a||(a=new c({cache:this,queryKey:i,queryHash:s,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(i),meta:t.meta}),this.add(a)),a},n.add=function(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"queryAdded",query:e}))},n.remove=function(e){var t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter((function(t){return t!==e})),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"queryRemoved",query:e}))},n.clear=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(t){e.remove(t)}))}))},n.get=function(e){return this.queriesMap[e]},n.getAll=function(){return this.queries},n.find=function(e,t){var n=(0,o.b_)(e,t)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find((function(e){return(0,o.MK)(n,e)}))},n.findAll=function(e,t){var n=(0,o.b_)(e,t)[0];return Object.keys(n).length>0?this.queries.filter((function(e){return(0,o.MK)(n,e)})):this.queries},n.notify=function(e){var t=this;s.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(e){e.onFocus()}))}))},n.onOnline=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(e){e.onOnline()}))}))},t}(l.Q),f=n(3959),p=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.mutations=[],n.mutationId=0,n}(0,i.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r=new f.s({mutationCache:this,mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:n,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0,meta:t.meta});return this.add(r),r},n.add=function(e){this.mutations.push(e),this.notify(e)},n.remove=function(e){this.mutations=this.mutations.filter((function(t){return t!==e})),e.cancel(),this.notify(e)},n.clear=function(){var e=this;s.j.batch((function(){e.mutations.forEach((function(t){e.remove(t)}))}))},n.getAll=function(){return this.mutations},n.find=function(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find((function(t){return(0,o.nJ)(e,t)}))},n.findAll=function(e){return this.mutations.filter((function(t){return(0,o.nJ)(e,t)}))},n.notify=function(e){var t=this;s.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var e=this.mutations.filter((function(e){return e.state.isPaused}));return s.j.batch((function(){return e.reduce((function(e,t){return e.then((function(){return t.continue().catch(o.lQ)}))}),Promise.resolve())}))},t}(l.Q),h=n(3811),v=n(7208);function m(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}function y(e,t){return null==e.getPreviousPageParam?void 0:e.getPreviousPageParam(t[0],t)}var g=function(){function e(e){void 0===e&&(e={}),this.queryCache=e.queryCache||new d,this.mutationCache=e.mutationCache||new p,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=h.m.subscribe((function(){h.m.isFocused()&&v.t.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())})),this.unsubscribeOnline=v.t.subscribe((function(){h.m.isFocused()&&v.t.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())}))},t.unmount=function(){var e,t;null==(e=this.unsubscribeFocus)||e.call(this),null==(t=this.unsubscribeOnline)||t.call(this)},t.isFetching=function(e,t){var n=(0,o.b_)(e,t)[0];return n.fetching=!0,this.queryCache.findAll(n).length},t.isMutating=function(e){return this.mutationCache.findAll((0,r.A)({},e,{fetching:!0})).length},t.getQueryData=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map((function(e){return[e.queryKey,e.state.data]}))},t.setQueryData=function(e,t,n){var r=(0,o.vh)(e),i=this.defaultQueryOptions(r);return this.queryCache.build(this,i).setData(t,n)},t.setQueriesData=function(e,t,n){var r=this;return s.j.batch((function(){return r.getQueryCache().findAll(e).map((function(e){var o=e.queryKey;return[o,r.setQueryData(o,t,n)]}))}))},t.getQueryState=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state},t.removeQueries=function(e,t){var n=(0,o.b_)(e,t)[0],r=this.queryCache;s.j.batch((function(){r.findAll(n).forEach((function(e){r.remove(e)}))}))},t.resetQueries=function(e,t,n){var i=this,a=(0,o.b_)(e,t,n),u=a[0],c=a[1],l=this.queryCache,d=(0,r.A)({},u,{active:!0});return s.j.batch((function(){return l.findAll(u).forEach((function(e){e.reset()})),i.refetchQueries(d,c)}))},t.cancelQueries=function(e,t,n){var r=this,i=(0,o.b_)(e,t,n),a=i[0],u=i[1],c=void 0===u?{}:u;void 0===c.revert&&(c.revert=!0);var l=s.j.batch((function(){return r.queryCache.findAll(a).map((function(e){return e.cancel(c)}))}));return Promise.all(l).then(o.lQ).catch(o.lQ)},t.invalidateQueries=function(e,t,n){var i,a,u,c=this,l=(0,o.b_)(e,t,n),d=l[0],f=l[1],p=(0,r.A)({},d,{active:null==(i=null!=(a=d.refetchActive)?a:d.active)||i,inactive:null!=(u=d.refetchInactive)&&u});return s.j.batch((function(){return c.queryCache.findAll(d).forEach((function(e){e.invalidate()})),c.refetchQueries(p,f)}))},t.refetchQueries=function(e,t,n){var i=this,a=(0,o.b_)(e,t,n),u=a[0],c=a[1],l=s.j.batch((function(){return i.queryCache.findAll(u).map((function(e){return e.fetch(void 0,(0,r.A)({},c,{meta:{refetchPage:null==u?void 0:u.refetchPage}}))}))})),d=Promise.all(l).then(o.lQ);return(null==c?void 0:c.throwOnError)||(d=d.catch(o.lQ)),d},t.fetchQuery=function(e,t,n){var r=(0,o.vh)(e,t,n),i=this.defaultQueryOptions(r);void 0===i.retry&&(i.retry=!1);var s=this.queryCache.build(this,i);return s.isStaleByTime(i.staleTime)?s.fetch(i):Promise.resolve(s.state.data)},t.prefetchQuery=function(e,t,n){return this.fetchQuery(e,t,n).then(o.lQ).catch(o.lQ)},t.fetchInfiniteQuery=function(e,t,n){var r=(0,o.vh)(e,t,n);return r.behavior={onFetch:function(e){e.fetchFn=function(){var t,n,r,i,s,a,c,l=null==(t=e.fetchOptions)||null==(n=t.meta)?void 0:n.refetchPage,d=null==(r=e.fetchOptions)||null==(i=r.meta)?void 0:i.fetchMore,f=null==d?void 0:d.pageParam,p="forward"===(null==d?void 0:d.direction),h="backward"===(null==d?void 0:d.direction),v=(null==(s=e.state.data)?void 0:s.pages)||[],g=(null==(a=e.state.data)?void 0:a.pageParams)||[],b=(0,o.jY)(),w=null==b?void 0:b.signal,E=g,C=!1,S=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},O=function(e,t,n,r){return E=r?[t].concat(E):[].concat(E,[t]),r?[n].concat(e):[].concat(e,[n])},A=function(t,n,r,o){if(C)return Promise.reject("Cancelled");if(void 0===r&&!n&&t.length)return Promise.resolve(t);var i={queryKey:e.queryKey,signal:w,pageParam:r,meta:e.meta},s=S(i),a=Promise.resolve(s).then((function(e){return O(t,r,e,o)}));return(0,u.dd)(s)&&(a.cancel=s.cancel),a};if(v.length)if(p){var x=void 0!==f,R=x?f:m(e.options,v);c=A(v,x,R)}else if(h){var P=void 0!==f,k=P?f:y(e.options,v);c=A(v,P,k,!0)}else!function(){E=[];var t=void 0===e.options.getNextPageParam,n=!l||!v[0]||l(v[0],0,v);c=n?A([],t,g[0]):Promise.resolve(O([],g[0],v[0]));for(var r=function(n){c=c.then((function(r){if(!l||!v[n]||l(v[n],n,v)){var o=t?g[n]:m(e.options,r);return A(r,t,o)}return Promise.resolve(O(r,g[n],v[n]))}))},o=1;o<v.length;o++)r(o)}();else c=A([]);var D=c.then((function(e){return{pages:e,pageParams:E}}));return D.cancel=function(){C=!0,null==b||b.abort(),(0,u.dd)(c)&&c.cancel()},D}}},this.fetchQuery(r)},t.prefetchInfiniteQuery=function(e,t,n){return this.fetchInfiniteQuery(e,t,n).then(o.lQ).catch(o.lQ)},t.cancelMutations=function(){var e=this,t=s.j.batch((function(){return e.mutationCache.getAll().map((function(e){return e.cancel()}))}));return Promise.all(t).then(o.lQ).catch(o.lQ)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,t){var n=this.queryDefaults.find((function(t){return(0,o.Od)(e)===(0,o.Od)(t.queryKey)}));n?n.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})},t.getQueryDefaults=function(e){var t;return e?null==(t=this.queryDefaults.find((function(t){return(0,o.Cp)(e,t.queryKey)})))?void 0:t.defaultOptions:void 0},t.setMutationDefaults=function(e,t){var n=this.mutationDefaults.find((function(t){return(0,o.Od)(e)===(0,o.Od)(t.mutationKey)}));n?n.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})},t.getMutationDefaults=function(e){var t;return e?null==(t=this.mutationDefaults.find((function(t){return(0,o.Cp)(e,t.mutationKey)})))?void 0:t.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(null==e?void 0:e._defaulted)return e;var t=(0,r.A)({},this.defaultOptions.queries,this.getQueryDefaults(null==e?void 0:e.queryKey),e,{_defaulted:!0});return!t.queryHash&&t.queryKey&&(t.queryHash=(0,o.F$)(t.queryKey,t)),t},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return(null==e?void 0:e._defaulted)?e:(0,r.A)({},this.defaultOptions.mutations,this.getMutationDefaults(null==e?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}()},8671:(e,t,n)=>{"use strict";n.d(t,{dd:()=>a,eJ:()=>l,wm:()=>c});var r=n(3811),o=n(7208),i=n(8059);function s(e){return Math.min(1e3*Math.pow(2,e),3e4)}function a(e){return"function"==typeof(null==e?void 0:e.cancel)}var u=function(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent};function c(e){return e instanceof u}var l=function(e){var t,n,c,l,d=this,f=!1;this.abort=e.abort,this.cancel=function(e){return null==t?void 0:t(e)},this.cancelRetry=function(){f=!0},this.continueRetry=function(){f=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(e,t){c=e,l=t}));var p=function(t){d.isResolved||(d.isResolved=!0,null==e.onSuccess||e.onSuccess(t),null==n||n(),c(t))},h=function(t){d.isResolved||(d.isResolved=!0,null==e.onError||e.onError(t),null==n||n(),l(t))};!function c(){if(!d.isResolved){var l;try{l=e.fn()}catch(e){l=Promise.reject(e)}t=function(e){if(!d.isResolved&&(h(new u(e)),null==d.abort||d.abort(),a(l)))try{l.cancel()}catch(e){}},d.isTransportCancelable=a(l),Promise.resolve(l).then(p).catch((function(t){var a,u;if(!d.isResolved){var l=null!=(a=e.retry)?a:3,p=null!=(u=e.retryDelay)?u:s,v="function"==typeof p?p(d.failureCount,t):p,m=!0===l||"number"==typeof l&&d.failureCount<l||"function"==typeof l&&l(d.failureCount,t);!f&&m?(d.failureCount++,null==e.onFail||e.onFail(d.failureCount,t),(0,i.yy)(v).then((function(){if(!r.m.isFocused()||!o.t.isOnline())return new Promise((function(t){n=t,d.isPaused=!0,null==e.onPause||e.onPause()})).then((function(){n=void 0,d.isPaused=!1,null==e.onContinue||e.onContinue()}))})).then((function(){f?h(t):c()}))):h(t)}}))}}()}},8937:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});var r=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(e){var t=this,n=e||function(){};return this.listeners.push(n),this.onSubscribe(),function(){t.listeners=t.listeners.filter((function(e){return e!==n})),t.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}()},4231:()=>{},8059:(e,t,n)=>{"use strict";n.d(t,{BH:()=>b,Cp:()=>y,F$:()=>v,G6:()=>A,GR:()=>d,HN:()=>u,MK:()=>p,Od:()=>m,S$:()=>o,Zw:()=>s,b_:()=>f,f8:()=>w,gn:()=>a,j3:()=>c,jY:()=>x,lQ:()=>i,nJ:()=>h,vh:()=>l,yy:()=>O});var r=n(7940),o="undefined"==typeof window;function i(){}function s(e,t){return"function"==typeof e?e(t):e}function a(e){return"number"==typeof e&&e>=0&&e!==1/0}function u(e){return Array.isArray(e)?e:[e]}function c(e,t){return Math.max(e+(t||0)-Date.now(),0)}function l(e,t,n){return S(e)?"function"==typeof t?(0,r.A)({},n,{queryKey:e,queryFn:t}):(0,r.A)({},t,{queryKey:e}):e}function d(e,t,n){return S(e)?"function"==typeof t?(0,r.A)({},n,{mutationKey:e,mutationFn:t}):(0,r.A)({},t,{mutationKey:e}):"function"==typeof e?(0,r.A)({},t,{mutationFn:e}):(0,r.A)({},e)}function f(e,t,n){return S(e)?[(0,r.A)({},t,{queryKey:e}),n]:[e||{},t]}function p(e,t){var n=e.active,r=e.exact,o=e.fetching,i=e.inactive,s=e.predicate,a=e.queryKey,u=e.stale;if(S(a))if(r){if(t.queryHash!==v(a,t.options))return!1}else if(!y(t.queryKey,a))return!1;var c=function(e,t){return!0===e&&!0===t||null==e&&null==t?"all":!1===e&&!1===t?"none":(null!=e?e:!t)?"active":"inactive"}(n,i);if("none"===c)return!1;if("all"!==c){var l=t.isActive();if("active"===c&&!l)return!1;if("inactive"===c&&l)return!1}return("boolean"!=typeof u||t.isStale()===u)&&(("boolean"!=typeof o||t.isFetching()===o)&&!(s&&!s(t)))}function h(e,t){var n=e.exact,r=e.fetching,o=e.predicate,i=e.mutationKey;if(S(i)){if(!t.options.mutationKey)return!1;if(n){if(m(t.options.mutationKey)!==m(i))return!1}else if(!y(t.options.mutationKey,i))return!1}return("boolean"!=typeof r||"loading"===t.state.status===r)&&!(o&&!o(t))}function v(e,t){return((null==t?void 0:t.queryKeyHashFn)||m)(e)}function m(e){var t,n=u(e);return t=n,JSON.stringify(t,(function(e,t){return E(t)?Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{}):t}))}function y(e,t){return g(u(e),u(t))}function g(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((function(n){return!g(e[n],t[n])})))}function b(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||E(e)&&E(t)){for(var r=n?e.length:Object.keys(e).length,o=n?t:Object.keys(t),i=o.length,s=n?[]:{},a=0,u=0;u<i;u++){var c=n?u:o[u];s[c]=b(e[c],t[c]),s[c]===e[c]&&a++}return r===i&&a===r?e:s}return t}function w(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function E(e){if(!C(e))return!1;var t=e.constructor;if(void 0===t)return!0;var n=t.prototype;return!!C(n)&&!!n.hasOwnProperty("isPrototypeOf")}function C(e){return"[object Object]"===Object.prototype.toString.call(e)}function S(e){return"string"==typeof e||Array.isArray(e)}function O(e){return new Promise((function(t){setTimeout(t,e)}))}function A(e){Promise.resolve().then(e).catch((function(e){return setTimeout((function(){throw e}))}))}function x(){if("function"==typeof AbortController)return new AbortController}},872:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.QueryClient,QueryClientProvider:()=>o.QueryClientProvider,useMutation:()=>o.useMutation,useQuery:()=>o.useQuery,useQueryClient:()=>o.useQueryClient});var r=n(4746);n.o(r,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return r.QueryClientProvider}}),n.o(r,"useMutation")&&n.d(t,{useMutation:function(){return r.useMutation}}),n.o(r,"useQuery")&&n.d(t,{useQuery:function(){return r.useQuery}}),n.o(r,"useQueryClient")&&n.d(t,{useQueryClient:function(){return r.useQueryClient}});var o=n(3122)},3122:(e,t,n)=>{"use strict";n.d(t,{QueryClientProvider:()=>h,useMutation:()=>C,useQuery:()=>I,useQueryClient:()=>p});var r=n(3582),o=n(5206),i=n.n(o)().unstable_batchedUpdates;r.j.setBatchNotifyFunction(i);var s=n(416),a=console;(0,s.B)(a);var u=n(1594),c=n.n(u),l=c().createContext(void 0),d=c().createContext(!1);function f(e){return e&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=l),window.ReactQueryClientContext):l}var p=function(){var e=c().useContext(f(c().useContext(d)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},h=function(e){var t=e.client,n=e.contextSharing,r=void 0!==n&&n,o=e.children;c().useEffect((function(){return t.mount(),function(){t.unmount()}}),[t]);var i=f(r);return c().createElement(d.Provider,{value:r},c().createElement(i.Provider,{value:t},o))},v=n(7940),m=n(8059),y=n(9408),g=n(3959),b=n(8937),w=function(e){function t(t,n){var r;return(r=e.call(this)||this).client=t,r.setOptions(n),r.bindMethods(),r.updateResult(),r}(0,y.A)(t,e);var n=t.prototype;return n.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},n.setOptions=function(e){this.options=this.client.defaultMutationOptions(e)},n.onUnsubscribe=function(){var e;this.listeners.length||(null==(e=this.currentMutation)||e.removeObserver(this))},n.onMutationUpdate=function(e){this.updateResult();var t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)},n.getCurrentResult=function(){return this.currentResult},n.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},n.mutate=function(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,(0,v.A)({},this.options,{variables:void 0!==e?e:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},n.updateResult=function(){var e=this.currentMutation?this.currentMutation.state:(0,g.$)(),t=(0,v.A)({},e,{isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset});this.currentResult=t},n.notify=function(e){var t=this;r.j.batch((function(){t.mutateOptions&&(e.onSuccess?(null==t.mutateOptions.onSuccess||t.mutateOptions.onSuccess(t.currentResult.data,t.currentResult.variables,t.currentResult.context),null==t.mutateOptions.onSettled||t.mutateOptions.onSettled(t.currentResult.data,null,t.currentResult.variables,t.currentResult.context)):e.onError&&(null==t.mutateOptions.onError||t.mutateOptions.onError(t.currentResult.error,t.currentResult.variables,t.currentResult.context),null==t.mutateOptions.onSettled||t.mutateOptions.onSettled(void 0,t.currentResult.error,t.currentResult.variables,t.currentResult.context))),e.listeners&&t.listeners.forEach((function(e){e(t.currentResult)}))}))},t}(b.Q);function E(e,t,n){return"function"==typeof t?t.apply(void 0,n):"boolean"==typeof t?t:!!e}function C(e,t,n){var o=c().useRef(!1),i=c().useState(0)[1],s=(0,m.GR)(e,t,n),a=p(),u=c().useRef();u.current?u.current.setOptions(s):u.current=new w(a,s);var l=u.current.getCurrentResult();c().useEffect((function(){o.current=!0;var e=u.current.subscribe(r.j.batchCalls((function(){o.current&&i((function(e){return e+1}))})));return function(){o.current=!1,e()}}),[]);var d=c().useCallback((function(e,t){u.current.mutate(e,t).catch(m.lQ)}),[]);if(l.error&&E(void 0,u.current.options.useErrorBoundary,[l.error]))throw l.error;return(0,v.A)({},l,{mutate:d,mutateAsync:l.mutate})}var S=n(3811),O=n(8671),A=function(e){function t(t,n){var r;return(r=e.call(this)||this).client=t,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}(0,y.A)(t,e);var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),x(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return R(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return R(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(e,t){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var o=this.hasListeners();o&&P(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(t),!o||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var i=this.computeRefetchInterval();!o||this.currentQuery===r&&this.options.enabled===n.enabled&&i===this.currentRefetchInterval||this.updateRefetchInterval(i)},n.getOptimisticResult=function(e){var t=this.client.defaultQueryObserverOptions(e),n=this.client.getQueryCache().build(this.client,t);return this.createResult(n,t)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(e,t){var n=this,r={},o=function(e){n.trackedProps.includes(e)||n.trackedProps.push(e)};return Object.keys(e).forEach((function(t){Object.defineProperty(r,t,{configurable:!1,enumerable:!0,get:function(){return o(t),e[t]}})})),(t.useErrorBoundary||t.suspense)&&o("error"),r},n.getNextResult=function(e){var t=this;return new Promise((function(n,r){var o=t.subscribe((function(t){t.isFetching||(o(),t.isError&&(null==e?void 0:e.throwOnError)?r(t.error):n(t))}))}))},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(e){return this.fetch((0,v.A)({},e,{meta:{refetchPage:null==e?void 0:e.refetchPage}}))},n.fetchOptimistic=function(e){var t=this,n=this.client.defaultQueryObserverOptions(e),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then((function(){return t.createResult(r,n)}))},n.fetch=function(e){var t=this;return this.executeFetch(e).then((function(){return t.updateResult(),t.currentResult}))},n.executeFetch=function(e){this.updateQuery();var t=this.currentQuery.fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(m.lQ)),t},n.updateStaleTimeout=function(){var e=this;if(this.clearStaleTimeout(),!m.S$&&!this.currentResult.isStale&&(0,m.gn)(this.options.staleTime)){var t=(0,m.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){e.currentResult.isStale||e.updateResult()}),t)}},n.computeRefetchInterval=function(){var e;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(e=this.options.refetchInterval)&&e},n.updateRefetchInterval=function(e){var t=this;this.clearRefetchInterval(),this.currentRefetchInterval=e,!m.S$&&!1!==this.options.enabled&&(0,m.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(t.options.refetchIntervalInBackground||S.m.isFocused())&&t.executeFetch()}),this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(e,t){var n,r=this.currentQuery,o=this.options,i=this.currentResult,a=this.currentResultState,u=this.currentResultOptions,c=e!==r,l=c?e.state:this.currentQueryInitialState,d=c?this.currentResult:this.previousQueryResult,f=e.state,p=f.dataUpdatedAt,h=f.error,v=f.errorUpdatedAt,y=f.isFetching,g=f.status,b=!1,w=!1;if(t.optimisticResults){var E=this.hasListeners(),C=!E&&x(e,t),S=E&&P(e,r,t,o);(C||S)&&(y=!0,p||(g="loading"))}if(t.keepPreviousData&&!f.dataUpdateCount&&(null==d?void 0:d.isSuccess)&&"error"!==g)n=d.data,p=d.dataUpdatedAt,g=d.status,b=!0;else if(t.select&&void 0!==f.data)if(i&&f.data===(null==a?void 0:a.data)&&t.select===this.selectFn)n=this.selectResult;else try{this.selectFn=t.select,n=t.select(f.data),!1!==t.structuralSharing&&(n=(0,m.BH)(null==i?void 0:i.data,n)),this.selectResult=n,this.selectError=null}catch(e){(0,s.t)().error(e),this.selectError=e}else n=f.data;if(void 0!==t.placeholderData&&void 0===n&&("loading"===g||"idle"===g)){var O;if((null==i?void 0:i.isPlaceholderData)&&t.placeholderData===(null==u?void 0:u.placeholderData))O=i.data;else if(O="function"==typeof t.placeholderData?t.placeholderData():t.placeholderData,t.select&&void 0!==O)try{O=t.select(O),!1!==t.structuralSharing&&(O=(0,m.BH)(null==i?void 0:i.data,O)),this.selectError=null}catch(e){(0,s.t)().error(e),this.selectError=e}void 0!==O&&(g="success",n=O,w=!0)}return this.selectError&&(h=this.selectError,n=this.selectResult,v=Date.now(),g="error"),{status:g,isLoading:"loading"===g,isSuccess:"success"===g,isError:"error"===g,isIdle:"idle"===g,data:n,dataUpdatedAt:p,error:h,errorUpdatedAt:v,failureCount:f.fetchFailureCount,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>l.dataUpdateCount||f.errorUpdateCount>l.errorUpdateCount,isFetching:y,isRefetching:y&&"loading"!==g,isLoadingError:"error"===g&&0===f.dataUpdatedAt,isPlaceholderData:w,isPreviousData:b,isRefetchError:"error"===g&&0!==f.dataUpdatedAt,isStale:k(e,t),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(e,t){if(!t)return!0;var n=this.options,r=n.notifyOnChangeProps,o=n.notifyOnChangePropsExclusions;if(!r&&!o)return!0;if("tracked"===r&&!this.trackedProps.length)return!0;var i="tracked"===r?this.trackedProps:r;return Object.keys(e).some((function(n){var r=n,s=e[r]!==t[r],a=null==i?void 0:i.some((function(e){return e===n})),u=null==o?void 0:o.some((function(e){return e===n}));return s&&!u&&(!i||a)}))},n.updateResult=function(e){var t=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,m.f8)(this.currentResult,t)){var n={cache:!0};!1!==(null==e?void 0:e.listeners)&&this.shouldNotifyListeners(this.currentResult,t)&&(n.listeners=!0),this.notify((0,v.A)({},n,e))}},n.updateQuery=function(){var e=this.client.getQueryCache().build(this.client,this.options);if(e!==this.currentQuery){var t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==t||t.removeObserver(this),e.addObserver(this))}},n.onQueryUpdate=function(e){var t={};"success"===e.type?t.onSuccess=!0:"error"!==e.type||(0,O.wm)(e.error)||(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()},n.notify=function(e){var t=this;r.j.batch((function(){e.onSuccess?(null==t.options.onSuccess||t.options.onSuccess(t.currentResult.data),null==t.options.onSettled||t.options.onSettled(t.currentResult.data,null)):e.onError&&(null==t.options.onError||t.options.onError(t.currentResult.error),null==t.options.onSettled||t.options.onSettled(void 0,t.currentResult.error)),e.listeners&&t.listeners.forEach((function(e){e(t.currentResult)})),e.cache&&t.client.getQueryCache().notify({query:t.currentQuery,type:"observerResultsUpdated"})}))},t}(b.Q);function x(e,t){return function(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&R(e,t,t.refetchOnMount)}function R(e,t,n){if(!1!==t.enabled){var r="function"==typeof n?n(e):n;return"always"===r||!1!==r&&k(e,t)}return!1}function P(e,t,n,r){return!1!==n.enabled&&(e!==t||!1===r.enabled)&&(!n.suspense||"error"!==e.state.status)&&k(e,n)}function k(e,t){return e.isStaleByTime(t.staleTime)}function D(){var e=!1;return{clearReset:function(){e=!1},reset:function(){e=!0},isReset:function(){return e}}}var T=c().createContext(D()),M=function(){return c().useContext(T)};function I(e,t,n){return function(e,t){var n=c().useRef(!1),o=c().useState(0)[1],i=p(),s=M(),a=i.defaultQueryObserverOptions(e);a.optimisticResults=!0,a.onError&&(a.onError=r.j.batchCalls(a.onError)),a.onSuccess&&(a.onSuccess=r.j.batchCalls(a.onSuccess)),a.onSettled&&(a.onSettled=r.j.batchCalls(a.onSettled)),a.suspense&&("number"!=typeof a.staleTime&&(a.staleTime=1e3),0===a.cacheTime&&(a.cacheTime=1)),(a.suspense||a.useErrorBoundary)&&(s.isReset()||(a.retryOnMount=!1));var u=c().useState((function(){return new t(i,a)}))[0],l=u.getOptimisticResult(a);if(c().useEffect((function(){n.current=!0,s.clearReset();var e=u.subscribe(r.j.batchCalls((function(){n.current&&o((function(e){return e+1}))})));return u.updateResult(),function(){n.current=!1,e()}}),[s,u]),c().useEffect((function(){u.setOptions(a,{listeners:!1})}),[a,u]),a.suspense&&l.isLoading)throw u.fetchOptimistic(a).then((function(e){var t=e.data;null==a.onSuccess||a.onSuccess(t),null==a.onSettled||a.onSettled(t,null)})).catch((function(e){s.clearReset(),null==a.onError||a.onError(e),null==a.onSettled||a.onSettled(void 0,e)}));if(l.isError&&!s.isReset()&&!l.isFetching&&E(a.suspense,a.useErrorBoundary,[l.error,u.getCurrentQuery()]))throw l.error;return"tracked"===a.notifyOnChangeProps&&(l=u.trackResult(l,a)),l}((0,m.vh)(e,t,n),A)}},2228:(e,t,n)=>{"use strict";n.d(t,{t:()=>o});var r=n(1594),o=n.n(r)().createContext(null)},9674:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(1594),o=n.n(r),i=n(2228),s=n(4283),a=n(9105);const u=function(e){var t=e.store,n=e.context,u=e.children,c=(0,r.useMemo)((function(){var e=(0,s.K)(t);return{store:t,subscription:e}}),[t]),l=(0,r.useMemo)((function(){return t.getState()}),[t]);(0,a.E)((function(){var e=c.subscription;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),l!==t.getState()&&e.notifyNestedSubs(),function(){e.tryUnsubscribe(),e.onStateChange=null}}),[c,l]);var d=n||i.t;return o().createElement(d.Provider,{value:c},u)}},7671:(e,t,n)=>{"use strict";n(2396),n(1594),n(9479),n(4283),n(9105),n(2228)},573:(e,t,n)=>{"use strict";n(7671),n(1314),n(5935),n(4801)},1314:(e,t,n)=>{"use strict";n(6822),n(7700)},5935:(e,t,n)=>{"use strict";n(7700)},4801:(e,t,n)=>{"use strict";n(7940)},7700:(e,t,n)=>{"use strict";function r(e){return function(t,n){var r=e(t,n);function o(){return r}return o.dependsOnOwnProps=!1,o}}function o(e){return null!==e.dependsOnOwnProps&&void 0!==e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function i(e,t){return function(t,n){n.displayName;var r=function(e,t){return r.dependsOnOwnProps?r.mapToProps(e,t):r.mapToProps(e)};return r.dependsOnOwnProps=!0,r.mapToProps=function(t,n){r.mapToProps=e,r.dependsOnOwnProps=o(e);var i=r(t,n);return"function"==typeof i&&(r.mapToProps=i,r.dependsOnOwnProps=o(i),i=r(t,n)),i},r}}n.d(t,{Qb:()=>i,o6:()=>r})},9277:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>r.A,d4:()=>i.d,wA:()=>o.w});var r=n(9674),o=(n(7671),n(2228),n(573),n(408)),i=n(211);n(9457)},408:(e,t,n)=>{"use strict";n.d(t,{w:()=>s});var r=n(2228),o=n(9457);function i(e){void 0===e&&(e=r.t);var t=e===r.t?o.P:(0,o.N)(e);return function(){return t().dispatch}}var s=i()},1111:(e,t,n)=>{"use strict";n.d(t,{n:()=>i});var r=n(1594),o=n(2228);function i(){return(0,r.useContext)(o.t)}},211:(e,t,n)=>{"use strict";n.d(t,{d:()=>l});var r=n(1594),o=n(1111),i=n(4283),s=n(9105),a=n(2228),u=function(e,t){return e===t};function c(e){void 0===e&&(e=a.t);var t=e===a.t?o.n:function(){return(0,r.useContext)(e)};return function(e,n){void 0===n&&(n=u);var o=t(),a=function(e,t,n,o){var a,u=(0,r.useReducer)((function(e){return e+1}),0)[1],c=(0,r.useMemo)((function(){return(0,i.K)(n,o)}),[n,o]),l=(0,r.useRef)(),d=(0,r.useRef)(),f=(0,r.useRef)(),p=(0,r.useRef)(),h=n.getState();try{if(e!==d.current||h!==f.current||l.current){var v=e(h);a=void 0!==p.current&&t(v,p.current)?p.current:v}else a=p.current}catch(e){throw l.current&&(e.message+="\nThe error may be correlated with this previous error:\n"+l.current.stack+"\n\n"),e}return(0,s.E)((function(){d.current=e,f.current=h,p.current=a,l.current=void 0})),(0,s.E)((function(){function e(){try{var e=n.getState();if(e===f.current)return;var r=d.current(e);if(t(r,p.current))return;p.current=r,f.current=e}catch(e){l.current=e}u()}return c.onStateChange=e,c.trySubscribe(),e(),function(){return c.tryUnsubscribe()}}),[n,c]),a}(e,n,o.store,o.subscription);return(0,r.useDebugValue)(a),a}}var l=c()},9457:(e,t,n)=>{"use strict";n.d(t,{N:()=>s,P:()=>a});var r=n(1594),o=n(2228),i=n(1111);function s(e){void 0===e&&(e=o.t);var t=e===o.t?i.n:function(){return(0,r.useContext)(e)};return function(){return t().store}}var a=s()},8790:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>r.Kq,d4:()=>r.d4,wA:()=>r.wA});var r=n(9277),o=n(9430);(0,n(9136).d)(o.r)},4283:(e,t,n)=>{"use strict";n.d(t,{K:()=>i});var r=n(9136);var o={notify:function(){},get:function(){return[]}};function i(e,t){var n,i=o;function s(){u.onStateChange&&u.onStateChange()}function a(){var o,a,u;n||(n=t?t.addNestedSub(s):e.subscribe(s),o=(0,r.f)(),a=null,u=null,i={clear:function(){a=null,u=null},notify:function(){o((function(){for(var e=a;e;)e.callback(),e=e.next}))},get:function(){for(var e=[],t=a;t;)e.push(t),t=t.next;return e},subscribe:function(e){var t=!0,n=u={callback:e,next:null,prev:u};return n.prev?n.prev.next=n:a=n,function(){t&&null!==a&&(t=!1,n.next?n.next.prev=n.prev:u=n.prev,n.prev?n.prev.next=n.next:a=n.next)}}})}var u={addNestedSub:function(e){return a(),i.subscribe(e)},notifyNestedSubs:function(){i.notify()},handleChangeWrapper:s,isSubscribed:function(){return Boolean(n)},trySubscribe:a,tryUnsubscribe:function(){n&&(n(),n=void 0,i.clear(),i=o)},getListeners:function(){return i}};return u}},9136:(e,t,n)=>{"use strict";n.d(t,{d:()=>o,f:()=>i});var r=function(e){e()},o=function(e){return r=e},i=function(){return r}},6822:(e,t,n)=>{"use strict";function r(e,t){var n={},r=function(r){var o=e[r];"function"==typeof o&&(n[r]=function(){return t(o.apply(void 0,arguments))})};for(var o in e)r(o);return n}n.d(t,{A:()=>r})},9430:(e,t,n)=>{"use strict";n.d(t,{r:()=>r.unstable_batchedUpdates});var r=n(5206)},9105:(e,t,n)=>{"use strict";n.d(t,{E:()=>o});var r=n(1594),o="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?r.useLayoutEffect:r.useEffect},291:(e,t)=>{"use strict";var n=60103,r=60106,o=60107,i=60108,s=60114,a=60109,u=60110,c=60112,l=60113,d=60120,f=60115,p=60116,h=60121,v=60122,m=60117,y=60129,g=60131;if("function"==typeof Symbol&&Symbol.for){var b=Symbol.for;n=b("react.element"),r=b("react.portal"),o=b("react.fragment"),i=b("react.strict_mode"),s=b("react.profiler"),a=b("react.provider"),u=b("react.context"),c=b("react.forward_ref"),l=b("react.suspense"),d=b("react.suspense_list"),f=b("react.memo"),p=b("react.lazy"),h=b("react.block"),v=b("react.server.block"),m=b("react.fundamental"),y=b("react.debug_trace_mode"),g=b("react.legacy_hidden")}function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case o:case s:case i:case l:case d:return e;default:switch(e=e&&e.$$typeof){case u:case c:case p:case f:case a:return e;default:return t}}case r:return t}}}},9479:(e,t,n)=>{"use strict";n(291)},6549:(e,t,n)=>{"use strict";n.d(t,{A:()=>$});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}Object.create;function i(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"==typeof SuppressedError&&SuppressedError;var s=n(1594),a="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return n=t||null,r=function(t){return e.forEach((function(e){return function(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}(e,t)}))},(o=(0,s.useState)((function(){return{value:n,callback:r,facade:{get current(){return o.value},set current(e){var t=o.value;t!==e&&(o.value=e,o.callback(e,t))}}}}))[0]).callback=r,o.facade;var n,r,o}function l(e){return e}function d(e,t){void 0===t&&(t=l);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(i)};s(),n={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),n}}}}}var f=function(e){void 0===e&&(e={});var t=d(null);return t.options=r({async:!0,ssr:!1},e),t}(),p=function(){},h=s.forwardRef((function(e,t){var n=s.useRef(null),i=s.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),a=i[0],u=i[1],l=e.forwardProps,d=e.children,h=e.className,v=e.removeScrollBar,m=e.enabled,y=e.shards,g=e.sideCar,b=e.noIsolation,w=e.inert,E=e.allowPinchZoom,C=e.as,S=void 0===C?"div":C,O=o(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),A=g,x=c([n,t]),R=r(r({},O),a);return s.createElement(s.Fragment,null,m&&s.createElement(A,{sideCar:f,removeScrollBar:v,shards:y,noIsolation:b,inert:w,setCallbacks:u,allowPinchZoom:!!E,lockRef:n}),l?s.cloneElement(s.Children.only(d),r(r({},R),{ref:x})):s.createElement(S,r({},R,{className:h,ref:x}),d))}));h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:a};var v,m=function(e){var t=e.sideCar,n=o(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw new Error("Sidecar medium not found");return s.createElement(i,r({},n))};m.isSideCarExport=!0;function y(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=v||n.nc;return t&&e.setAttribute("nonce",t),e}var g=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=y())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e,t=(e=g(),function(t,n){s.useEffect((function(){return e.add(t),function(){e.remove()}}),[t&&n])});return function(e){var n=e.styles,r=e.dynamic;return t(n,r),null}},w={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=b(),O=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r,i=s.useMemo((function(){return C(o)}),[o]);return s.createElement(S,{styles:O(i,!t,o,n?"":"!important")})},x=!1;if("undefined"!=typeof window)try{var R=Object.defineProperty({},"passive",{get:function(){return x=!0,!0}});window.addEventListener("test",R,R),window.removeEventListener("test",R,R)}catch(e){x=!1}var P=!!x&&{passive:!1},k=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},D=function(e,t){var n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),T(e,n)){var r=M(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},T=function(e,t){return"v"===e?function(e){return k(e,"overflowY")}(t):function(e){return k(e,"overflowX")}(t)},M=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},L=function(e){return e&&"current"in e?e.current:e},F=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},j=0,N=[];const q=(U=function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),o=s.useState(j++)[0],a=s.useState((function(){return b()}))[0],u=s.useRef(e);s.useEffect((function(){u.current=e}),[e]),s.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=i([e.lockRef.current],(e.shards||[]).map(L),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(o))})),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(o))}))}}}),[e.inert,e.lockRef.current,e.shards]);var c=s.useCallback((function(e,t){if("touches"in e&&2===e.touches.length)return!u.current.allowPinchZoom;var o,i=I(e),s=n.current,a="deltaX"in e?e.deltaX:s[0]-i[0],c="deltaY"in e?e.deltaY:s[1]-i[1],l=e.target,d=Math.abs(a)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===l.type)return!1;var f=D(d,l);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,l)),!f)return!1;if(!r.current&&"changedTouches"in e&&(a||c)&&(r.current=o),!o)return!0;var p=r.current||o;return function(e,t,n,r,o){var i=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),s=i*r,a=n.target,u=t.contains(a),c=!1,l=s>0,d=0,f=0;do{var p=M(e,a),h=p[0],v=p[1]-p[2]-i*h;(h||v)&&T(e,a)&&(d+=v,f+=h),a=a.parentNode}while(!u&&a!==document.body||u&&(t.contains(a)||t===a));return(l&&(o&&0===d||!o&&s>d)||!l&&(o&&0===f||!o&&-s>f))&&(c=!0),c}(p,t,e,"h"===p?a:c,!0)}),[]),l=s.useCallback((function(e){var n=e;if(N.length&&N[N.length-1]===a){var r="deltaY"in n?_(n):I(n),o=t.current.filter((function(e){return e.name===n.type&&e.target===n.target&&function(e,t){return e[0]===t[0]&&e[1]===t[1]}(e.delta,r)}))[0];if(o&&o.should)n.cancelable&&n.preventDefault();else if(!o){var i=(u.current.shards||[]).map(L).filter(Boolean).filter((function(e){return e.contains(n.target)}));(i.length>0?c(n,i[0]):!u.current.noIsolation)&&n.cancelable&&n.preventDefault()}}}),[]),d=s.useCallback((function(e,n,r,o){var i={name:e,delta:n,target:r,should:o};t.current.push(i),setTimeout((function(){t.current=t.current.filter((function(e){return e!==i}))}),1)}),[]),f=s.useCallback((function(e){n.current=I(e),r.current=void 0}),[]),p=s.useCallback((function(t){d(t.type,_(t),t.target,c(t,e.lockRef.current))}),[]),h=s.useCallback((function(t){d(t.type,I(t),t.target,c(t,e.lockRef.current))}),[]);s.useEffect((function(){return N.push(a),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",l,P),document.addEventListener("touchmove",l,P),document.addEventListener("touchstart",f,P),function(){N=N.filter((function(e){return e!==a})),document.removeEventListener("wheel",l,P),document.removeEventListener("touchmove",l,P),document.removeEventListener("touchstart",f,P)}}),[]);var v=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(a,{styles:F(o)}):null,v?s.createElement(A,{gapMode:"margin"}):null)},f.useMedium(U),m);var U,B=s.forwardRef((function(e,t){return s.createElement(h,r({},e,{ref:t,sideCar:q}))}));B.classNames=h.classNames;const $=B},7499:e=>{e.exports=function(e,t,n,r){var o=n?n.call(r,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var i=Object.keys(e),s=Object.keys(t);if(i.length!==s.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(t),u=0;u<i.length;u++){var c=i[u];if(!a(c))return!1;var l=e[c],d=t[c];if(!1===(o=n?n.call(r,l,d,c):void 0)||void 0===o&&l!==d)return!1}return!0}},3254:(e,t,n)=>{"use strict";n.d(t,{AH:()=>be,Ay:()=>Le,i7:()=>_e});var r=n(261),o=n(1594),i=n.n(o),s=n(7499),a=n.n(s);const u=function(e){function t(e,r,u,c,f){for(var p,h,v,m,w,C=0,S=0,O=0,A=0,x=0,M=0,_=v=p=0,F=0,j=0,N=0,q=0,U=u.length,B=U-1,$="",Q="",K="",z="";F<U;){if(h=u.charCodeAt(F),F===B&&0!==S+A+O+C&&(0!==S&&(h=47===S?10:47),A=O=C=0,U++,B++),0===S+A+O+C){if(F===B&&(0<j&&($=$.replace(d,"")),0<$.trim().length)){switch(h){case 32:case 9:case 59:case 13:case 10:break;default:$+=u.charAt(F)}h=59}switch(h){case 123:for(p=($=$.trim()).charCodeAt(0),v=1,q=++F;F<U;){switch(h=u.charCodeAt(F)){case 123:v++;break;case 125:v--;break;case 47:switch(h=u.charCodeAt(F+1)){case 42:case 47:e:{for(_=F+1;_<B;++_)switch(u.charCodeAt(_)){case 47:if(42===h&&42===u.charCodeAt(_-1)&&F+2!==_){F=_+1;break e}break;case 10:if(47===h){F=_+1;break e}}F=_}}break;case 91:h++;case 40:h++;case 34:case 39:for(;F++<B&&u.charCodeAt(F)!==h;);}if(0===v)break;F++}if(v=u.substring(q,F),0===p&&(p=($=$.replace(l,"").trim()).charCodeAt(0)),64===p){switch(0<j&&($=$.replace(d,"")),h=$.charCodeAt(1)){case 100:case 109:case 115:case 45:j=r;break;default:j=T}if(q=(v=t(r,j,v,h,f+1)).length,0<I&&(w=a(3,v,j=n(T,$,N),r,P,R,q,h,f,c),$=j.join(""),void 0!==w&&0===(q=(v=w.trim()).length)&&(h=0,v="")),0<q)switch(h){case 115:$=$.replace(E,s);case 100:case 109:case 45:v=$+"{"+v+"}";break;case 107:v=($=$.replace(y,"$1 $2"))+"{"+v+"}",v=1===D||2===D&&i("@"+v,3)?"@-webkit-"+v+"@"+v:"@"+v;break;default:v=$+v,112===c&&(Q+=v,v="")}else v=""}else v=t(r,n(r,$,N),v,c,f+1);K+=v,v=N=j=_=p=0,$="",h=u.charCodeAt(++F);break;case 125:case 59:if(1<(q=($=(0<j?$.replace(d,""):$).trim()).length))switch(0===_&&(p=$.charCodeAt(0),45===p||96<p&&123>p)&&(q=($=$.replace(" ",":")).length),0<I&&void 0!==(w=a(1,$,r,e,P,R,Q.length,c,f,c))&&0===(q=($=w.trim()).length)&&($="\0\0"),p=$.charCodeAt(0),h=$.charCodeAt(1),p){case 0:break;case 64:if(105===h||99===h){z+=$+u.charAt(F);break}default:58!==$.charCodeAt(q-1)&&(Q+=o($,p,h,$.charCodeAt(2)))}N=j=_=p=0,$="",h=u.charCodeAt(++F)}}switch(h){case 13:case 10:47===S?S=0:0===1+p&&107!==c&&0<$.length&&(j=1,$+="\0"),0<I*L&&a(0,$,r,e,P,R,Q.length,c,f,c),R=1,P++;break;case 59:case 125:if(0===S+A+O+C){R++;break}default:switch(R++,m=u.charAt(F),h){case 9:case 32:if(0===A+C+S)switch(x){case 44:case 58:case 9:case 32:m="";break;default:32!==h&&(m=" ")}break;case 0:m="\\0";break;case 12:m="\\f";break;case 11:m="\\v";break;case 38:0===A+S+C&&(j=N=1,m="\f"+m);break;case 108:if(0===A+S+C+k&&0<_)switch(F-_){case 2:112===x&&58===u.charCodeAt(F-3)&&(k=x);case 8:111===M&&(k=M)}break;case 58:0===A+S+C&&(_=F);break;case 44:0===S+O+A+C&&(j=1,m+="\r");break;case 34:case 39:0===S&&(A=A===h?0:0===A?h:A);break;case 91:0===A+S+O&&C++;break;case 93:0===A+S+O&&C--;break;case 41:0===A+S+C&&O--;break;case 40:if(0===A+S+C){if(0===p)if(2*x+3*M==533);else p=1;O++}break;case 64:0===S+O+A+C+_+v&&(v=1);break;case 42:case 47:if(!(0<A+C+O))switch(S){case 0:switch(2*h+3*u.charCodeAt(F+1)){case 235:S=47;break;case 220:q=F,S=42}break;case 42:47===h&&42===x&&q+2!==F&&(33===u.charCodeAt(q+2)&&(Q+=u.substring(q,F+1)),m="",S=0)}}0===S&&($+=m)}M=x,x=h,F++}if(0<(q=Q.length)){if(j=r,0<I&&(void 0!==(w=a(2,Q,j,e,P,R,q,c,f,c))&&0===(Q=w).length))return z+Q+K;if(Q=j.join(",")+"{"+Q+"}",0!=D*k){switch(2!==D||i(Q,2)||(k=0),k){case 111:Q=Q.replace(b,":-moz-$1")+Q;break;case 112:Q=Q.replace(g,"::-webkit-input-$1")+Q.replace(g,"::-moz-$1")+Q.replace(g,":-ms-input-$1")+Q}k=0}}return z+Q+K}function n(e,t,n){var o=t.trim().split(v);t=o;var i=o.length,s=e.length;switch(s){case 0:case 1:var a=0;for(e=0===s?"":e[0]+" ";a<i;++a)t[a]=r(e,t[a],n).trim();break;default:var u=a=0;for(t=[];a<i;++a)for(var c=0;c<s;++c)t[u++]=r(e[c]+" ",o[a],n).trim()}return t}function r(e,t,n){var r=t.charCodeAt(0);switch(33>r&&(r=(t=t.trim()).charCodeAt(0)),r){case 38:return t.replace(m,"$1"+e.trim());case 58:return e.trim()+t.replace(m,"$1"+e.trim());default:if(0<1*n&&0<t.indexOf("\f"))return t.replace(m,(58===e.charCodeAt(0)?"":"$1")+e.trim())}return e+t}function o(e,t,n,r){var s=e+";",a=2*t+3*n+4*r;if(944===a){e=s.indexOf(":",9)+1;var u=s.substring(e,s.length-1).trim();return u=s.substring(0,e).trim()+u+";",1===D||2===D&&i(u,1)?"-webkit-"+u+u:u}if(0===D||2===D&&!i(s,1))return s;switch(a){case 1015:return 97===s.charCodeAt(10)?"-webkit-"+s+s:s;case 951:return 116===s.charCodeAt(3)?"-webkit-"+s+s:s;case 963:return 110===s.charCodeAt(5)?"-webkit-"+s+s:s;case 1009:if(100!==s.charCodeAt(4))break;case 969:case 942:return"-webkit-"+s+s;case 978:return"-webkit-"+s+"-moz-"+s+s;case 1019:case 983:return"-webkit-"+s+"-moz-"+s+"-ms-"+s+s;case 883:if(45===s.charCodeAt(8))return"-webkit-"+s+s;if(0<s.indexOf("image-set(",11))return s.replace(x,"$1-webkit-$2")+s;break;case 932:if(45===s.charCodeAt(4))switch(s.charCodeAt(5)){case 103:return"-webkit-box-"+s.replace("-grow","")+"-webkit-"+s+"-ms-"+s.replace("grow","positive")+s;case 115:return"-webkit-"+s+"-ms-"+s.replace("shrink","negative")+s;case 98:return"-webkit-"+s+"-ms-"+s.replace("basis","preferred-size")+s}return"-webkit-"+s+"-ms-"+s+s;case 964:return"-webkit-"+s+"-ms-flex-"+s+s;case 1023:if(99!==s.charCodeAt(8))break;return"-webkit-box-pack"+(u=s.substring(s.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+s+"-ms-flex-pack"+u+s;case 1005:return p.test(s)?s.replace(f,":-webkit-")+s.replace(f,":-moz-")+s:s;case 1e3:switch(t=(u=s.substring(13).trim()).indexOf("-")+1,u.charCodeAt(0)+u.charCodeAt(t)){case 226:u=s.replace(w,"tb");break;case 232:u=s.replace(w,"tb-rl");break;case 220:u=s.replace(w,"lr");break;default:return s}return"-webkit-"+s+"-ms-"+u+s;case 1017:if(-1===s.indexOf("sticky",9))break;case 975:switch(t=(s=e).length-10,a=(u=(33===s.charCodeAt(t)?s.substring(0,t):s).substring(e.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|u.charCodeAt(7))){case 203:if(111>u.charCodeAt(8))break;case 115:s=s.replace(u,"-webkit-"+u)+";"+s;break;case 207:case 102:s=s.replace(u,"-webkit-"+(102<a?"inline-":"")+"box")+";"+s.replace(u,"-webkit-"+u)+";"+s.replace(u,"-ms-"+u+"box")+";"+s}return s+";";case 938:if(45===s.charCodeAt(5))switch(s.charCodeAt(6)){case 105:return u=s.replace("-items",""),"-webkit-"+s+"-webkit-box-"+u+"-ms-flex-"+u+s;case 115:return"-webkit-"+s+"-ms-flex-item-"+s.replace(S,"")+s;default:return"-webkit-"+s+"-ms-flex-line-pack"+s.replace("align-content","").replace(S,"")+s}break;case 973:case 989:if(45!==s.charCodeAt(3)||122===s.charCodeAt(4))break;case 931:case 953:if(!0===A.test(e))return 115===(u=e.substring(e.indexOf(":")+1)).charCodeAt(0)?o(e.replace("stretch","fill-available"),t,n,r).replace(":fill-available",":stretch"):s.replace(u,"-webkit-"+u)+s.replace(u,"-moz-"+u.replace("fill-",""))+s;break;case 962:if(s="-webkit-"+s+(102===s.charCodeAt(5)?"-ms-"+s:"")+s,211===n+r&&105===s.charCodeAt(13)&&0<s.indexOf("transform",10))return s.substring(0,s.indexOf(";",27)+1).replace(h,"$1-webkit-$2")+s}return s}function i(e,t){var n=e.indexOf(1===t?":":"{"),r=e.substring(0,3!==t?n:10);return n=e.substring(n+1,e.length-1),_(2!==t?r:r.replace(O,"$1"),n,t)}function s(e,t){var n=o(t,t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2));return n!==t+";"?n.replace(C," or ($1)").substring(4):"("+t+")"}function a(e,t,n,r,o,i,s,a,u,l){for(var d,f=0,p=t;f<I;++f)switch(d=M[f].call(c,e,p,n,r,o,i,s,a,u,l)){case void 0:case!1:case!0:case null:break;default:p=d}if(p!==t)return p}function u(e){return void 0!==(e=e.prefix)&&(_=null,e?"function"!=typeof e?D=1:(D=2,_=e):D=0),u}function c(e,n){var r=e;if(33>r.charCodeAt(0)&&(r=r.trim()),r=[r],0<I){var o=a(-1,n,r,r,P,R,0,0,0,0);void 0!==o&&"string"==typeof o&&(n=o)}var i=t(T,r,n,0,0);return 0<I&&(void 0!==(o=a(-2,i,r,r,P,R,i.length,0,0,0))&&(i=o)),k=0,R=P=1,i}var l=/^\0+/g,d=/[\0\r\f]/g,f=/: */g,p=/zoo|gra/,h=/([,: ])(transform)/g,v=/,\r+?/g,m=/([\t\r\n ])*\f?&/g,y=/@(k\w+)\s*(\S*)\s*/,g=/::(place)/g,b=/:(read-only)/g,w=/[svh]\w+-[tblr]{2}/,E=/\(\s*(.*)\s*\)/g,C=/([\s\S]*?);/g,S=/-self|flex-/g,O=/[^]*?(:[rp][el]a[\w-]+)[^]*/,A=/stretch|:\s*\w+\-(?:conte|avail)/,x=/([^-])(image-set\()/,R=1,P=1,k=0,D=1,T=[],M=[],I=0,_=null,L=0;return c.use=function e(t){switch(t){case void 0:case null:I=M.length=0;break;default:if("function"==typeof t)M[I++]=t;else if("object"==typeof t)for(var n=0,r=t.length;n<r;++n)e(t[n]);else L=0|!!t}return e},c.set=u,void 0!==e&&u(e),c};const c={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function l(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var d=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,f=l((function(e){return d.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),p=n(2396),h=n.n(p);function v(){return(v=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var m=function(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n},y=function(e){return null!==e&&"object"==typeof e&&"[object Object]"===(e.toString?e.toString():Object.prototype.toString.call(e))&&!(0,r.typeOf)(e)},g=Object.freeze([]),b=Object.freeze({});function w(e){return"function"==typeof e}function E(e){return e.displayName||e.name||"Component"}function C(e){return e&&"string"==typeof e.styledComponentId}var S="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",O="undefined"!=typeof window&&"HTMLElement"in window,A=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&(void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)));function x(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(n.length>0?" Args: "+n.join(", "):""))}var R=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}var t=e.prototype;return t.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},t.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,o=r;e>=o;)(o<<=1)<0&&x(16,""+e);this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var i=r;i<o;i++)this.groupSizes[i]=0}for(var s=this.indexOfGroup(e+1),a=0,u=t.length;a<u;a++)this.tag.insertRule(s,t[a])&&(this.groupSizes[e]++,s++)},t.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var o=n;o<r;o++)this.tag.deleteRule(n)}},t.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),o=r+n,i=r;i<o;i++)t+=this.tag.getRule(i)+"/*!sc*/\n";return t},e}(),P=new Map,k=new Map,D=1,T=function(e){if(P.has(e))return P.get(e);for(;k.has(D);)D++;var t=D++;return P.set(e,t),k.set(t,e),t},M=function(e){return k.get(e)},I=function(e,t){t>=D&&(D=t+1),P.set(e,t),k.set(t,e)},_="style["+S+'][data-styled-version="5.3.8"]',L=new RegExp("^"+S+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),F=function(e,t,n){for(var r,o=n.split(","),i=0,s=o.length;i<s;i++)(r=o[i])&&e.registerName(t,r)},j=function(e,t){for(var n=(t.textContent||"").split("/*!sc*/\n"),r=[],o=0,i=n.length;o<i;o++){var s=n[o].trim();if(s){var a=s.match(L);if(a){var u=0|parseInt(a[1],10),c=a[2];0!==u&&(I(c,u),F(e,c,a[3]),e.getTag().insertRules(u,r)),r.length=0}else r.push(s)}}},N=function(){return n.nc},q=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(e){for(var t=e.childNodes,n=t.length;n>=0;n--){var r=t[n];if(r&&1===r.nodeType&&r.hasAttribute(S))return r}}(n),i=void 0!==o?o.nextSibling:null;r.setAttribute(S,"active"),r.setAttribute("data-styled-version","5.3.8");var s=N();return s&&r.setAttribute("nonce",s),n.insertBefore(r,i),r},U=function(){function e(e){var t=this.element=q(e);t.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var o=t[n];if(o.ownerNode===e)return o}x(17)}(t),this.length=0}var t=e.prototype;return t.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},t.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},t.getRule=function(e){var t=this.sheet.cssRules[e];return void 0!==t&&"string"==typeof t.cssText?t.cssText:""},e}(),B=function(){function e(e){var t=this.element=q(e);this.nodes=t.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t),r=this.nodes[e];return this.element.insertBefore(n,r||null),this.length++,!0}return!1},t.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},t.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),$=function(){function e(e){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},t.deleteRule=function(e){this.rules.splice(e,1),this.length--},t.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),Q=O,K={isServer:!O,useCSSOMInjection:!A},z=function(){function e(e,t,n){void 0===e&&(e=b),void 0===t&&(t={}),this.options=v({},K,{},e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&O&&Q&&(Q=!1,function(e){for(var t=document.querySelectorAll(_),n=0,r=t.length;n<r;n++){var o=t[n];o&&"active"!==o.getAttribute(S)&&(j(e,o),o.parentNode&&o.parentNode.removeChild(o))}}(this))}e.registerId=function(e){return T(e)};var t=e.prototype;return t.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(v({},this.options,{},t),this.gs,n&&this.names||void 0)},t.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},t.getTag=function(){return this.tag||(this.tag=(n=(t=this.options).isServer,r=t.useCSSOMInjection,o=t.target,e=n?new $(o):r?new U(o):new B(o),new R(e)));var e,t,n,r,o},t.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},t.registerName=function(e,t){if(T(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},t.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(T(e),n)},t.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},t.clearRules=function(e){this.getTag().clearGroup(T(e)),this.clearNames(e)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(e){for(var t=e.getTag(),n=t.length,r="",o=0;o<n;o++){var i=M(o);if(void 0!==i){var s=e.names.get(i),a=t.getGroup(o);if(s&&a&&s.size){var u=S+".g"+o+'[id="'+i+'"]',c="";void 0!==s&&s.forEach((function(e){e.length>0&&(c+=e+",")})),r+=""+a+u+'{content:"'+c+'"}/*!sc*/\n'}}}return r}(this)},e}(),W=/(a)(d)/gi,H=function(e){return String.fromCharCode(e+(e>25?39:97))};function G(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=H(t%52)+n;return(H(t%52)+n).replace(W,"$1-$2")}var X=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},Y=function(e){return X(5381,e)};function V(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(w(n)&&!C(n))return!1}return!0}var Z=Y("5.3.8"),J=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&V(e),this.componentId=t,this.baseHash=X(Z,t),this.baseStyle=n,z.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.componentId,o=[];if(this.baseStyle&&o.push(this.baseStyle.generateAndInjectStyles(e,t,n)),this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(r,this.staticRulesId))o.push(this.staticRulesId);else{var i=ye(this.rules,e,t,n).join(""),s=G(X(this.baseHash,i)>>>0);if(!t.hasNameForId(r,s)){var a=n(i,"."+s,void 0,r);t.insertRules(r,s,a)}o.push(s),this.staticRulesId=s}else{for(var u=this.rules.length,c=X(this.baseHash,n.hash),l="",d=0;d<u;d++){var f=this.rules[d];if("string"==typeof f)l+=f;else if(f){var p=ye(f,e,t,n),h=Array.isArray(p)?p.join(""):p;c=X(c,h+d),l+=h}}if(l){var v=G(c>>>0);if(!t.hasNameForId(r,v)){var m=n(l,"."+v,void 0,r);t.insertRules(r,v,m)}o.push(v)}}return o.join(" ")},e}(),ee=/^\s*\/\/.*$/gm,te=[":","[",".","#"];function ne(e){var t,n,r,o,i=void 0===e?b:e,s=i.options,a=void 0===s?b:s,c=i.plugins,l=void 0===c?g:c,d=new u(a),f=[],p=function(e){function t(t){if(t)try{e(t+"}")}catch(e){}}return function(n,r,o,i,s,a,u,c,l,d){switch(n){case 1:if(0===l&&64===r.charCodeAt(0))return e(r+";"),"";break;case 2:if(0===c)return r+"/*|*/";break;case 3:switch(c){case 102:case 112:return e(o[0]+r),"";default:return r+(0===d?"/*|*/":"")}case-2:r.split("/*|*/}").forEach(t)}}}((function(e){f.push(e)})),h=function(e,r,i){return 0===r&&-1!==te.indexOf(i[n.length])||i.match(o)?e:"."+t};function v(e,i,s,a){void 0===a&&(a="&");var u=e.replace(ee,""),c=i&&s?s+" "+i+" { "+u+" }":u;return t=a,n=i,r=new RegExp("\\"+n+"\\b","g"),o=new RegExp("(\\"+n+"\\b){2,}"),d(s||!i?"":i,c)}return d.use([].concat(l,[function(e,t,o){2===e&&o.length&&o[0].lastIndexOf(n)>0&&(o[0]=o[0].replace(r,h))},p,function(e){if(-2===e){var t=f;return f=[],t}}])),v.hash=l.length?l.reduce((function(e,t){return t.name||x(15),X(e,t.name)}),5381).toString():"",v}var re=i().createContext(),oe=(re.Consumer,i().createContext()),ie=(oe.Consumer,new z),se=ne();function ae(){return(0,o.useContext)(re)||ie}function ue(){return(0,o.useContext)(oe)||se}function ce(e){var t=(0,o.useState)(e.stylisPlugins),n=t[0],r=t[1],s=ae(),u=(0,o.useMemo)((function(){var t=s;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t}),[e.disableCSSOMInjection,e.sheet,e.target]),c=(0,o.useMemo)((function(){return ne({options:{prefix:!e.disableVendorPrefixes},plugins:n})}),[e.disableVendorPrefixes,n]);return(0,o.useEffect)((function(){a()(n,e.stylisPlugins)||r(e.stylisPlugins)}),[e.stylisPlugins]),i().createElement(re.Provider,{value:u},i().createElement(oe.Provider,{value:c},e.children))}var le=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=se);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.toString=function(){return x(12,String(n.name))},this.name=e,this.id="sc-keyframes-"+e,this.rules=t}return e.prototype.getName=function(e){return void 0===e&&(e=se),this.name+e.hash},e}(),de=/([A-Z])/,fe=/([A-Z])/g,pe=/^ms-/,he=function(e){return"-"+e.toLowerCase()};function ve(e){return de.test(e)?e.replace(fe,he).replace(pe,"-ms-"):e}var me=function(e){return null==e||!1===e||""===e};function ye(e,t,n,r){if(Array.isArray(e)){for(var o,i=[],s=0,a=e.length;s<a;s+=1)""!==(o=ye(e[s],t,n,r))&&(Array.isArray(o)?i.push.apply(i,o):i.push(o));return i}return me(e)?"":C(e)?"."+e.styledComponentId:w(e)?"function"!=typeof(u=e)||u.prototype&&u.prototype.isReactComponent||!t?e:ye(e(t),t,n,r):e instanceof le?n?(e.inject(n,r),e.getName(r)):e:y(e)?function e(t,n){var r,o,i=[];for(var s in t)t.hasOwnProperty(s)&&!me(t[s])&&(Array.isArray(t[s])&&t[s].isCss||w(t[s])?i.push(ve(s)+":",t[s],";"):y(t[s])?i.push.apply(i,e(t[s],s)):i.push(ve(s)+": "+(r=s,(null==(o=t[s])||"boolean"==typeof o||""===o?"":"number"!=typeof o||0===o||r in c?String(o).trim():o+"px")+";")));return n?[n+" {"].concat(i,["}"]):i}(e):e.toString();var u}var ge=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function be(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return w(e)||y(e)?ge(ye(m(g,[e].concat(n)))):0===n.length&&1===e.length&&"string"==typeof e[0]?e:ge(ye(m(e,n)))}new Set;var we=function(e,t,n){return void 0===n&&(n=b),e.theme!==n.theme&&e.theme||t||n.theme},Ee=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Ce=/(^-|-$)/g;function Se(e){return e.replace(Ee,"-").replace(Ce,"")}var Oe=function(e){return G(Y(e)>>>0)};function Ae(e){return"string"==typeof e&&!0}var xe=function(e){return"function"==typeof e||"object"==typeof e&&null!==e&&!Array.isArray(e)},Re=function(e){return"__proto__"!==e&&"constructor"!==e&&"prototype"!==e};function Pe(e,t,n){var r=e[n];xe(t)&&xe(r)?ke(r,t):e[n]=t}function ke(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var o=0,i=n;o<i.length;o++){var s=i[o];if(xe(s))for(var a in s)Re(a)&&Pe(e,s[a],a)}return e}var De=i().createContext();De.Consumer;var Te={};function Me(e,t,n){var r=C(e),s=!Ae(e),a=t.attrs,u=void 0===a?g:a,c=t.componentId,l=void 0===c?function(e,t){var n="string"!=typeof e?"sc":Se(e);Te[n]=(Te[n]||0)+1;var r=n+"-"+Oe("5.3.8"+n+Te[n]);return t?t+"-"+r:r}(t.displayName,t.parentComponentId):c,d=t.displayName,p=void 0===d?function(e){return Ae(e)?"styled."+e:"Styled("+E(e)+")"}(e):d,m=t.displayName&&t.componentId?Se(t.displayName)+"-"+t.componentId:t.componentId||l,y=r&&e.attrs?Array.prototype.concat(e.attrs,u).filter(Boolean):u,S=t.shouldForwardProp;r&&e.shouldForwardProp&&(S=t.shouldForwardProp?function(n,r,o){return e.shouldForwardProp(n,r,o)&&t.shouldForwardProp(n,r,o)}:e.shouldForwardProp);var O,A=new J(n,m,r?e.componentStyle:void 0),x=A.isStatic&&0===u.length,R=function(e,t){return function(e,t,n,r){var i=e.attrs,s=e.componentStyle,a=e.defaultProps,u=e.foldedComponentIds,c=e.shouldForwardProp,l=e.styledComponentId,d=e.target,p=function(e,t,n){void 0===e&&(e=b);var r=v({},t,{theme:e}),o={};return n.forEach((function(e){var t,n,i,s=e;for(t in w(s)&&(s=s(r)),s)r[t]=o[t]="className"===t?(n=o[t],i=s[t],n&&i?n+" "+i:n||i):s[t]})),[r,o]}(we(t,(0,o.useContext)(De),a)||b,t,i),h=p[0],m=p[1],y=function(e,t,n){var r=ae(),o=ue();return t?e.generateAndInjectStyles(b,r,o):e.generateAndInjectStyles(n,r,o)}(s,r,h),g=n,E=m.$as||t.$as||m.as||t.as||d,C=Ae(E),S=m!==t?v({},t,{},m):t,O={};for(var A in S)"$"!==A[0]&&"as"!==A&&("forwardedAs"===A?O.as=S[A]:(c?c(A,f,E):!C||f(A))&&(O[A]=S[A]));return t.style&&m.style!==t.style&&(O.style=v({},t.style,{},m.style)),O.className=Array.prototype.concat(u,l,y!==l?y:null,t.className,m.className).filter(Boolean).join(" "),O.ref=g,(0,o.createElement)(E,O)}(O,e,t,x)};return R.displayName=p,(O=i().forwardRef(R)).attrs=y,O.componentStyle=A,O.displayName=p,O.shouldForwardProp=S,O.foldedComponentIds=r?Array.prototype.concat(e.foldedComponentIds,e.styledComponentId):g,O.styledComponentId=m,O.target=r?e.target:e,O.withComponent=function(e){var r=t.componentId,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(t,["componentId"]),i=r&&r+"-"+(Ae(e)?e:Se(E(e)));return Me(e,v({},o,{attrs:y,componentId:i}),n)},Object.defineProperty(O,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(t){this._foldedDefaultProps=r?ke({},e.defaultProps,t):t}}),O.toString=function(){return"."+O.styledComponentId},s&&h()(O,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),O}var Ie=function(e){return function e(t,n,o){if(void 0===o&&(o=b),!(0,r.isValidElementType)(n))return x(1,String(n));var i=function(){return t(n,o,be.apply(void 0,arguments))};return i.withConfig=function(r){return e(t,n,v({},o,{},r))},i.attrs=function(r){return e(t,n,v({},o,{attrs:Array.prototype.concat(o.attrs,r).filter(Boolean)}))},i}(Me,e)};["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach((function(e){Ie[e]=Ie(e)}));!function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=V(e),z.registerId(this.componentId+1)}var t=e.prototype;t.createStyles=function(e,t,n,r){var o=r(ye(this.rules,t,n,r).join(""),""),i=this.componentId+e;n.insertRules(i,i,o)},t.removeStyles=function(e,t){t.clearRules(this.componentId+e)},t.renderStyles=function(e,t,n,r){e>2&&z.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}();function _e(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=be.apply(void 0,[e].concat(n)).join(""),i=Oe(o);return new le(i,o)}!function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=N();return"<style "+[n&&'nonce="'+n+'"',S+'="true"','data-styled-version="5.3.8"'].filter(Boolean).join(" ")+">"+t+"</style>"},this.getStyleTags=function(){return e.sealed?x(2):e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)return x(2);var n=((t={})[S]="",t["data-styled-version"]="5.3.8",t.dangerouslySetInnerHTML={__html:e.instance.toString()},t),r=N();return r&&(n.nonce=r),[i().createElement("style",v({},n,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new z({isServer:!0}),this.sealed=!1}var t=e.prototype;t.collectStyles=function(e){return this.sealed?x(2):i().createElement(ce,{sheet:this.instance},e)},t.interleaveWithNodeStream=function(e){return x(3)}}();const Le=Ie},7940:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},9408:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,r(e,t)}n.d(t,{A:()=>o})},2313:()=>{"use strict";class e{constructor(e,t,{tabInsertsSuggestions:r,defaultFirstOption:o,scrollIntoViewOptions:i}={}){this.input=e,this.list=t,this.tabInsertsSuggestions=null==r||r,this.defaultFirstOption=null!=o&&o,this.scrollIntoViewOptions=null!=i?i:{block:"nearest",inline:"nearest"},this.isComposing=!1,t.id||(t.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=e=>function(e,t){if(e.shiftKey||e.metaKey||e.altKey)return;if(!t.ctrlBindings&&e.ctrlKey)return;if(t.isComposing)return;switch(e.key){case"Enter":n(t.input,t.list)&&e.preventDefault();break;case"Tab":t.tabInsertsSuggestions&&n(t.input,t.list)&&e.preventDefault();break;case"Escape":t.clearSelection();break;case"ArrowDown":t.navigate(1),e.preventDefault();break;case"ArrowUp":t.navigate(-1),e.preventDefault();break;case"n":t.ctrlBindings&&e.ctrlKey&&(t.navigate(1),e.preventDefault());break;case"p":t.ctrlBindings&&e.ctrlKey&&(t.navigate(-1),e.preventDefault());break;default:if(e.ctrlKey)break;t.clearSelection()}}(e,this),this.compositionEventHandler=e=>function(e,t){t.isComposing="compositionstart"===e.type;const n=document.getElementById(t.input.getAttribute("aria-controls")||"");if(!n)return;t.clearSelection()}(e,this),this.inputHandler=this.clearSelection.bind(this),e.setAttribute("role","combobox"),e.setAttribute("aria-controls",t.id),e.setAttribute("aria-expanded","false"),e.setAttribute("aria-autocomplete","list"),e.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",t),this.indicateDefaultOption()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",t)}indicateDefaultOption(){var e;this.defaultFirstOption&&(null===(e=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(o)[0])||void 0===e||e.setAttribute("data-combobox-option-default","true"))}navigate(e=1){const t=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(o)[0],n=Array.from(this.list.querySelectorAll('[role="option"]')).filter(o),i=n.indexOf(t);if(i===n.length-1&&1===e||0===i&&-1===e)return this.clearSelection(),void this.input.focus();let s=1===e?0:n.length-1;if(t&&i>=0){const t=i+e;t>=0&&t<n.length&&(s=t)}const a=n[s];if(a)for(const e of n)e.removeAttribute("data-combobox-option-default"),a===e?(this.input.setAttribute("aria-activedescendant",a.id),a.setAttribute("aria-selected","true"),r(a),a.scrollIntoView(this.scrollIntoViewOptions)):e.removeAttribute("aria-selected")}clearSelection(){this.input.removeAttribute("aria-activedescendant");for(const e of this.list.querySelectorAll('[aria-selected="true"]'))e.removeAttribute("aria-selected");this.indicateDefaultOption()}}function t(e){if(!(e.target instanceof Element))return;const t=e.target.closest('[role="option"]');t&&"true"!==t.getAttribute("aria-disabled")&&function(e,t){e.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:t}))}(t,{event:e})}function n(e,t){const n=t.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!n&&("true"===n.getAttribute("aria-disabled")||n.click(),!0)}function r(e){e.dispatchEvent(new Event("combobox-select",{bubbles:!0}))}function o(e){return!e.hidden&&!(e instanceof HTMLInputElement&&"hidden"===e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}const i=/\s|\(|\[/;function s(e,t,n,{multiWord:r,lookBackIndex:o,lastMatchPosition:s}={multiWord:!1,lookBackIndex:0,lastMatchPosition:null}){let a=e.lastIndexOf(t,n-1);if(-1===a)return;if(a<o)return;if(r){if(null!=s){if(s===a)return;a=s-t.length}if(" "===e[a+1]&&n>=a+t.length+1)return;if(e.lastIndexOf("\n",n-1)>a)return;if(e.lastIndexOf(".",n-1)>a)return}else{if(e.lastIndexOf(" ",n-1)>a)return}const u=e[a-1];if(u&&!i.test(u))return;return{text:e.substring(a+t.length,n),position:a+t.length}}class a extends HTMLElement{}class u extends Event{constructor(){super("update")}}const c=new WeakMap;class l extends a{#e=new MutationObserver((()=>this.#t()));#n=new ResizeObserver((()=>this.#r()));#o;#i;static for(e){let t=c.get(e);return t||(t=new l,t.connect(e),c.set(e,t)),t}connect(e){this.#o=new WeakRef(e),this.#i=document.createElement("div"),this.#i.style.position="absolute",this.#i.style.pointerEvents="none",e.after(this.#i),this.#i.appendChild(this)}forceUpdate(){this.#t(),this.#s()}connectedCallback(){this.#a((e=>{this.style.pointerEvents="none",this.style.userSelect="none",this.style.overflow="hidden",this.style.display="block",this.style.visibility="hidden",e instanceof HTMLTextAreaElement?(this.style.whiteSpace="pre-wrap",this.style.wordWrap="break-word"):(this.style.whiteSpace="nowrap",this.style.display="table-cell",this.style.verticalAlign="middle"),this.setAttribute("aria-hidden","true"),this.#t(),this.#s(),this.#e.observe(e,{attributeFilter:["style","dir"]}),this.#n.observe(e),document.addEventListener("scroll",this.#u,{capture:!0}),window.addEventListener("resize",this.#u,{capture:!0}),e.addEventListener("input",this.#c,{capture:!0})}))}disconnectedCallback(){this.#i?.remove(),this.#e.disconnect(),this.#n.disconnect(),document.removeEventListener("scroll",this.#u,{capture:!0}),window.removeEventListener("resize",this.#u,{capture:!0});const e=this.#l;e&&(e.removeEventListener("input",this.#c,{capture:!0}),c.delete(e))}get#l(){return this.#o?.deref()}#a(e){const t=this.#l;return t?e(t):this.remove()}#d=0;#f=0;#p(){this.#a((e=>{const t=window.getComputedStyle(e);this.style.height=t.height,this.style.width=t.width,e.clientHeight!==this.clientHeight&&(this.style.height=`calc(${t.height} + ${e.clientHeight-this.clientHeight}px)`),e.clientWidth!==this.clientWidth&&(this.style.width=`calc(${t.width} + ${e.clientWidth-this.clientWidth}px)`);const n=e.getBoundingClientRect(),r=this.getBoundingClientRect();this.#d=this.#d+n.left-r.left,this.#f=this.#f+n.top-r.top,this.style.transform=`translate(${this.#d}px, ${this.#f}px)`,this.scrollTop=e.scrollTop,this.scrollLeft=e.scrollLeft,this.dispatchEvent(new u)}))}#h=!1;#r(){this.#h||(this.#h=!0,requestAnimationFrame((()=>{this.#p(),this.#h=!1})))}#t(){this.#a((e=>{const t=window.getComputedStyle(e);for(const e of d)this.style[e]=t[e];this.#r()}))}#s(){this.#a((e=>{this.textContent=e.value,this.#p()}))}#c=()=>this.#s();#u=e=>{this.#a((t=>{(e.target===document||e.target===window||e.target instanceof Node&&e.target.contains(t))&&this.#r()}))}}const d=["direction","writingMode","unicodeBidi","textOrientation","boxSizing","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderStyle","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing","tabSize","MozTabSize"];try{customElements.define("input-style-clone",l)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}class f{#v;#m;#y;constructor(e,t=0,n=t){this.#v=e,this.#m=t,this.#y=n}static fromSelection(e){const{selectionStart:t,selectionEnd:n}=e;return new f(e,t??void 0,n??void 0)}get collapsed(){return this.startOffset===this.endOffset}get commonAncestorContainer(){return this.#v}get endContainer(){return this.#v}get startContainer(){return this.#v}get startOffset(){return this.#m}get endOffset(){return this.#y}setStartOffset(e){this.#m=this.#g(e)}setEndOffset(e){this.#y=this.#g(e)}collapse(e=!1){e?this.setEndOffset(this.startOffset):this.setStartOffset(this.endOffset)}cloneContents(){return this.#b().cloneContents()}cloneRange(){return new f(this.#v,this.startOffset,this.endOffset)}getBoundingClientRect(){return this.#b().getBoundingClientRect()}getClientRects(){return this.#b().getClientRects()}toString(){return this.#b().toString()}getStyleClone(){return this.#w}get#w(){return l.for(this.#v)}get#E(){return this.#w}#g(e){return Math.max(0,Math.min(e,this.#v.value.length))}#b(){const e=document.createRange(),t=this.#E.childNodes[0];return t&&(e.setStart(t,this.startOffset),e.setEnd(t,this.endOffset)),e}}const p=new WeakMap;class h{constructor(e,t){this.expander=e,this.input=t,this.combobox=null,this.menu=null,this.match=null,this.justPasted=!1,this.lookBackIndex=0,this.oninput=this.onInput.bind(this),this.onpaste=this.onPaste.bind(this),this.onkeydown=this.onKeydown.bind(this),this.oncommit=this.onCommit.bind(this),this.onmousedown=this.onMousedown.bind(this),this.onblur=this.onBlur.bind(this),this.interactingWithList=!1,t.addEventListener("paste",this.onpaste),t.addEventListener("input",this.oninput),t.addEventListener("keydown",this.onkeydown),t.addEventListener("blur",this.onblur)}destroy(){this.input.removeEventListener("paste",this.onpaste),this.input.removeEventListener("input",this.oninput),this.input.removeEventListener("keydown",this.onkeydown),this.input.removeEventListener("blur",this.onblur)}dismissMenu(){this.deactivate()&&(this.lookBackIndex=this.input.selectionEnd||this.lookBackIndex)}activate(t,n){var r,o;this.input!==document.activeElement&&this.input!==(null===(o=null===(r=document.activeElement)||void 0===r?void 0:r.shadowRoot)||void 0===o?void 0:o.activeElement)||(this.deactivate(),this.menu=n,n.id||(n.id=`text-expander-${Math.floor(1e5*Math.random()).toString()}`),this.expander.append(n),this.combobox=new e(this.input,n),this.expander.dispatchEvent(new Event("text-expander-activate")),this.positionMenu(n,t.position),this.combobox.start(),n.addEventListener("combobox-commit",this.oncommit),n.addEventListener("mousedown",this.onmousedown),this.combobox.navigate(1))}positionMenu(e,t){const n=new f(this.input,t).getBoundingClientRect(),r=n.left,o=n.top+n.height,i=e.getBoundingClientRect(),s=r-i.left,a=o-i.top;if(0!==s||0!==a){const t=getComputedStyle(e);e.style.left=t.left?`calc(${t.left} + ${s}px)`:`${s}px`,e.style.top=t.top?`calc(${t.top} + ${a}px)`:`${a}px`}}deactivate(){const e=this.menu;return!(!e||!this.combobox)&&(this.expander.dispatchEvent(new Event("text-expander-deactivate")),this.menu=null,e.removeEventListener("combobox-commit",this.oncommit),e.removeEventListener("mousedown",this.onmousedown),this.combobox.destroy(),this.combobox=null,e.remove(),!0)}onCommit({target:e}){var t;const n=e;if(!(n instanceof HTMLElement))return;if(!this.combobox)return;const r=this.match;if(!r)return;const o=this.input.value.substring(0,r.position-r.key.length),i=this.input.value.substring(r.position+r.text.length),s={item:n,key:r.key,value:null,continue:!1};if(!this.expander.dispatchEvent(new CustomEvent("text-expander-value",{cancelable:!0,detail:s})))return;if(!s.value)return;let a=null!==(t=this.expander.getAttribute("suffix"))&&void 0!==t?t:" ";s.continue&&(a="");const u=`${s.value}${a}`;this.input.value=o+u+i;const c=o.length+u.length;this.deactivate(),this.input.focus({preventScroll:!0}),this.input.selectionStart=c,this.input.selectionEnd=c,s.continue||(this.lookBackIndex=c,this.match=null),this.expander.dispatchEvent(new CustomEvent("text-expander-committed",{cancelable:!1,detail:{input:this.input}}))}onBlur(){this.interactingWithList?this.interactingWithList=!1:this.deactivate()}onPaste(){this.justPasted=!0}async onInput(){if(this.justPasted)return void(this.justPasted=!1);const e=this.findMatch();if(e){this.match=e;const t=await this.notifyProviders(e);if(!this.match)return;t?this.activate(e,t):this.deactivate()}else this.match=null,this.deactivate()}findMatch(){const e=this.input.selectionEnd||0,t=this.input.value;e<=this.lookBackIndex&&(this.lookBackIndex=e-1);for(const{key:n,multiWord:r}of this.expander.keys){const o=s(t,n,e,{multiWord:r,lookBackIndex:this.lookBackIndex,lastMatchPosition:this.match?this.match.position:null});if(o)return{text:o.text,key:n,position:o.position}}}async notifyProviders(e){const t=[];if(!this.expander.dispatchEvent(new CustomEvent("text-expander-change",{cancelable:!0,detail:{provide:e=>t.push(e),text:e.text,key:e.key}})))return;return(await Promise.all(t)).filter((e=>e.matched)).map((e=>e.fragment))[0]}onMousedown(){this.interactingWithList=!0}onKeydown(e){"Escape"===e.key&&(this.match=null,this.deactivate()&&(this.lookBackIndex=this.input.selectionEnd||this.lookBackIndex,e.stopImmediatePropagation(),e.preventDefault()))}}class v extends HTMLElement{get keys(){const e=this.getAttribute("keys"),t=e?e.split(" "):[],n=this.getAttribute("multiword"),r=n?n.split(" "):[],o=0===r.length&&this.hasAttribute("multiword");return t.map((e=>({key:e,multiWord:o||r.includes(e)})))}connectedCallback(){const e=this.querySelector('input[type="text"], textarea');if(!(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement))return;const t=new h(this,e);p.set(this,t)}disconnectedCallback(){const e=p.get(this);e&&(e.destroy(),p.delete(this))}dismiss(){const e=p.get(this);e&&e.dismissMenu()}}window.customElements.get("text-expander")||(window.TextExpanderElement=v,window.customElements.define("text-expander",v))}}]);