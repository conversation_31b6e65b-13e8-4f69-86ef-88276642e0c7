/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[187],{7112:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(6914);class SearchKeyboardHandler extends elementorModules.frontend.handlers.Base{focusableResultElements;currentResultFocusedIndex=-1;__construct(){super.__construct(...arguments),elementorFrontend.hooks.addAction("search:results-updated",this.loadResultElementsEvents.bind(this))}getDefaultSettings(){return{selectors:{searchWrapper:".e-search",searchField:".e-search-input",resultsContainer:".e-search-results-container",loopItem:".e-loop-item",clearIcon:".e-search-input-wrapper > svg, .e-search-input-wrapper > i"}}}getDefaultElements(){const e=this.getSettings("selectors");return{resultsContainer:this.$element[0].querySelector(e.resultsContainer),searchField:this.$element[0].querySelector(e.searchField),clearIcon:this.$element[0].querySelector(e.clearIcon)}}setFocusableElements(e){const t=(0,n.focusableElementSelectors)(),s=e.querySelectorAll(t);this.focusableResultElements=Array.from(s).filter((e=>!e.disabled&&!e.inert&&-1!==e.tabIndex))}isSearchInputFocused(){return this.elements.searchField===elementorFrontend.elements.window.document.activeElement}bindEvents(){this.boundHandleKeyboardNavigation=this.handleKeyboardNavigation.bind(this),this.boundHandleEscapeKey=this.handleEscapeKey.bind(this),this.elements.searchField.addEventListener("keydown",this.boundHandleKeyboardNavigation),elementorFrontend.elements.window.document.addEventListener("keydown",this.boundHandleEscapeKey),this.elements.searchField.addEventListener("focus",this.openResults.bind(this))}loadResultElementsEvents(){this.setFocusableElements(this.$element[0].querySelector(this.getSettings("selectors.resultsContainer"))),this.focusableResultElements?.forEach((e=>{e.addEventListener("keydown",this.handleKeyboardNavigation.bind(this))}))}unbindEvents(){this.boundHandleKeyboardNavigation&&this.elements.searchField.removeEventListener("keydown",this.boundHandleKeyboardNavigation),elementorFrontend.elements.window.document.removeEventListener("keydown",this.boundHandleEscapeKey),this.elements.searchField.removeEventListener("focus",this.openResults.bind(this))}handleKeyboardNavigation(e){switch(e.key){case"ArrowDown":this.focusNextElement();break;case"ArrowUp":this.focusPreviousElement();break;case"Enter":this.handleEnterKey()}}areResultsClosed(){return 0===this.elements.resultsContainer.querySelectorAll(this.getSettings("selectors.loopItem")).length||this.elements.resultsContainer.classList.contains("hidden")}openResults(){this.areResultsClosed()&&(this.elements.resultsContainer.classList.remove("hidden"),elementorFrontend.hooks.doAction("search:results-displayed",this.getID()))}handleEnterKey(){this.closeResults()}handleEscapeKey(e){if("Escape"!==e.key)return;const t=elementorFrontend.elements.window.document.activeElement;(this.elements.resultsContainer.contains(t)||!1)&&this.elements.searchField.focus(),this.closeResults()}focusNextElement(){this.isSearchInputFocused()?this.currentResultFocusedIndex=0:(this.currentResultFocusedIndex++,this.checkFocusIndexBounds()),this.updateFocus()}focusPreviousElement(){this.isSearchInputFocused()?this.currentResultFocusedIndex=this.focusableResultElements.length-1:(this.currentResultFocusedIndex--,this.checkFocusIndexBounds()),this.updateFocus()}checkFocusIndexBounds(){this.currentResultFocusedIndex>=this.focusableResultElements.length?this.currentResultFocusedIndex=-1:this.currentResultFocusedIndex<-1&&(this.currentResultFocusedIndex=this.focusableResultElements.length-1)}updateFocus(){-1===this.currentResultFocusedIndex?this.focusSearchAndMoveCursorToEnd():this.setFocusToElement(this.focusableResultElements[this.currentResultFocusedIndex])}closeResults(){this.elements.resultsContainer.classList.add("hidden"),this.updateAriaExpanded(!1)}updateAriaExpanded(e){this.elements.searchField.setAttribute("aria-expanded",e)}focusSearchAndMoveCursorToEnd(){const e=this.elements.searchField,t=e.value.length;this.setFocusToElement(this.elements.searchField),e.setSelectionRange(t,t)}setFocusToElement(e){e.focus()}}t.default=SearchKeyboardHandler},6963:(e,t,s)=>{var n=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(s(5012));class Search extends elementorModules.frontend.handlers.Base{debounceTimeoutId;page_number;__construct(){super.__construct(...arguments),elementorFrontend.hooks.addAction("search:results-displayed",this.hideOtherResults.bind(this))}getDefaultSettings(){return{selectors:{searchWrapper:".e-search",searchField:".e-search-input",submitButton:".e-search-submit",clearIcon:".e-search-input-wrapper > svg, .e-search-input-wrapper > i",searchIcon:".e-search-label > svg, .e-search-label > i",resultsContainer:".e-search-results-container",results:".e-search-results",links:"a.page-numbers:not(.current)"},classes:{searchResultsListWrapper:"e-search-results-list",searchResultsPagination:"elementor-pagination"}}}getDefaultElements(){const e=this.getSettings("selectors");return{searchWidget:this.$element[0],searchWrapper:this.$element[0].querySelector(e.searchWrapper),searchField:this.$element[0].querySelector(e.searchField),submitButton:this.$element[0].querySelector(e.submitButton),clearIcon:this.$element[0].querySelector(e.clearIcon),searchIcon:this.$element[0].querySelector(e.searchIcon),resultsContainer:this.$element[0].querySelector(e.resultsContainer),results:this.$element[0].querySelector(e.results),links:this.$element[0].querySelectorAll(e.links)}}onInit(){super.onInit(),this.changeClearIconVisibility(!0),this.updateInputStyle(),this.toggleSearchResultsVisibility=this.toggleSearchResultsVisibility.bind(this),document.addEventListener("click",this.toggleSearchResultsVisibility),document.fonts.ready.then((()=>this.updateInputStyle()))}onDestroy(){document.removeEventListener("click",this.toggleSearchResultsVisibility)}bindEvents(){this.elements.submitButton.addEventListener("click",this.onSubmit.bind(this)),this.elements.searchField.addEventListener("input",(e=>{this.changeClearIconVisibility(!e.target.value.length),this.debounce(this.onType)(e)})),this.elements.searchField.addEventListener("keydown",this.onSearchFieldKeydown.bind(this)),this.elements.searchWidget.addEventListener("click",this.onClick.bind(this)),["focusin","focusout"].forEach((e=>{this.elements.searchField.addEventListener(e,this.toggleWidgetFocusClass.bind(this))})),this.elements.clearIcon?.addEventListener("click",this.onClear.bind(this)),this.linksEventListeners()}linksEventListeners(){this.elements.links.length&&this.elements.links.forEach((e=>{e.addEventListener("click",(e=>{this.handleLinkClick(e)}))}))}handleLinkClick(e){e.preventDefault();const t=e?.target.getAttribute("href"),s=new URLSearchParams(new URL(t).search).get("e-search-page");this.page_number=s?parseInt(s,10):1,this.removeLinksListeners(),this.renderLiveResults()}removeLinksListeners(){this.elements.links.length&&this.elements.links.forEach((e=>{e.removeEventListener("click",this.handleLinkClick)}))}reInitLinks(){this.elements.links=document.querySelectorAll(`[data-id="${this.getID()}"] a.page-numbers:not(.current)`),this.linksEventListeners()}onClick(){this.elements.resultsContainer.classList.add("hide-loader")}onType(e){if(e.preventDefault(),this.updateAriaLabel(this.elements.searchField.value),!this.elements.searchField.value.length)return void this.clearResultsMarkup();const t=this.getMinimumSearchLength();this.shouldShowLiveResults()&&this.elements.searchField.value.length>=t&&(this.page_number=1,this.renderLiveResults())}toggleWidgetFocusClass(e){const t="focusin"===e.type;this.$element[0].classList.toggle("e-focus",t)}onSubmit(e){elementorFrontend.isEditMode()&&e.preventDefault()}onClear(e){e.preventDefault(),this.elements.searchField.value="",this.clearResultsMarkup(),this.elements.searchField.focus(),this.changeClearIconVisibility(!0)}onSearchFieldKeydown(e){"Enter"===e.code&&(this.clearResultsMarkup(),this.onSubmit(e))}fetchUpdatedSearchWidgetMarkup(){return fetch(`${elementorProFrontend.config.urls.rest}elementor-pro/v1/refresh-search`,this.getFetchArgumentsForSearchUpdate())}getMinimumSearchLength(){return this.getElementSettings().minimum_search_characters||3}shouldShowLiveResults(){return this.getElementSettings().live_results&&this.getElementSettings().template_id}renderLiveResults(){if(document.querySelector(`.elementor-element-${this.getID()}`)){if(this.elements.searchField.value)return this.elements.resultsContainer.classList.remove("hide-loader"),this.elements.resultsContainer.classList.remove("hidden"),this.fetchUpdatedSearchWidgetMarkup().then((e=>e instanceof Response&&e?.ok&&!(400<=e?.status)?e.json():{})).catch((()=>({}))).then((e=>{if(!e?.data)return void this.updateAriaExpanded(!1);const t=this.createResultNode(e);this.elements.results.replaceChildren(t.resultContentNode),this.elements.results.append(t.paginationNode),this.elements.resultsContainer.classList.add("hide-loader"),this.maybeHandleNoResults(t.resultContentNode),elementorFrontend.hooks.doAction("search:results-updated")})).finally((()=>{const e=document.querySelectorAll(`[data-id="${this.getID()}"] .e-loop-item`);(0,i.default)(e),this.reInitLinks(),ElementorProFrontendConfig.settings.lazy_load_background_images&&document.dispatchEvent(new Event("elementor/lazyload/observe"))}));this.clearResultsMarkup()}}maybeHandleNoResults(e){const t=!!e.querySelector(".e-search-nothing-found-message");this.elements.results.classList[t?"add":"remove"]("no-results"),t||this.hideOtherResults()}hideOtherResults(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(e&&e!==this.getID())return;const t=document.querySelectorAll(`${this.getSettings("selectors").resultsContainer}:not(.hidden)`);Array.from(t).filter((e=>!e.closest(`.elementor-element-${this.getID()}`))).forEach((e=>e.classList.add("hidden")))}createResultNode(e){const t=document.createElement("div"),s=this.getSettings("classes.searchResultsListWrapper"),n=document.createElement("nav"),i=this.getSettings("classes.searchResultsPagination");n.setAttribute("class",i),n.setAttribute("aria-label","Pagination"),t.setAttribute("class",s),n.innerHTML=e.pagination,t.innerHTML=e.data;const r=t.querySelectorAll(".e-loop-item").length>0;return this.updateAriaExpanded(r),{resultContentNode:t,paginationNode:n}}updateAriaExpanded(e){this.elements.searchField.setAttribute("aria-expanded",e?"true":"false")}updateAriaLabel(e){e?this.elements.resultsContainer.setAttribute("aria-label",`Results for ${e}`):this.elements.resultsContainer.removeAttribute("aria-label")}clearResultsMarkup(){this.elements.results.innerHTML="",this.updateAriaExpanded(!1)}getFetchArgumentsForSearchUpdate(){const e=this.prepareSearchUpdateRequestData(),t={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)};return elementorFrontend.isEditMode()&&elementorPro.config.eSearch?.nonce&&(t.headers["X-WP-Nonce"]=elementorPro.config.eSearch?.nonce),t}prepareSearchUpdateRequestData(){const e=""+this.getID(),t={post_id:this.getClosestDataElementorId(this.$element[0]),widget_id:e,search_term:this.elements.searchField.value||"",page_number:this.page_number};if(elementorFrontend.isEditMode()){const s=window.top.$e.components.get("document").utils.findContainerById(e);t.widget_model=s.model.toJSON({remove:["default","editSettings","defaultEditSettings"]}),t.is_edit_mode=!0}return t}getClosestDataElementorId(e){const t=e.closest("[data-elementor-id]");return t?t.getAttribute("data-elementor-id"):0}debounce(e){var t=this;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:300;return function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];clearTimeout(t.debounceTimeoutId),t.debounceTimeoutId=setTimeout((()=>e.apply(t,i)),s)}}updateInputStyle(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["searchIcon","clearIcon"];const t={searchIcon:"icon-label",clearIcon:"icon-clear"},s=this.$element[0].style,n=this.getAllDisplayNoneParents(this.$element[0].parentNode);this.setElementsDisplay(n,"block");for(const n of e){const{width:e}=this.elements[n]?.getBoundingClientRect()||{width:0},i=t[n];s.setProperty(`--e-search-${i}-absolute-width`,e+"px"),this.elements.searchField.classList[e?"remove":"add"](`no-${i}`)}this.setElementsDisplay(n,""),this.elements.searchWrapper.classList.remove("hidden")}changeClearIconVisibility(e){this.elements.clearIcon?.classList[e?"add":"remove"]("hidden")}toggleSearchResultsVisibility(e){const t=this.getSettings("selectors"),s=`.elementor-element-${this.getID()}`,{target:n}=e,i=!!n?.closest(`${s} ${t.resultsContainer}`)||n?.classList?.contains(t.resultsContainer)&&!!n?.closest(s),r=!!n?.closest(`${s} ${t.searchWrapper}`),l=n?.classList?.contains(t.searchField.replace(".","")),a=this.elements.resultsContainer?.children?.length;i&&this.hideOtherResults(),a&&!i&&(l&&r||this.elements.resultsContainer.classList.add("hidden"))}getAllDisplayNoneParents(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!e||e===document.body)return t;return"none"===window.getComputedStyle(e).display&&t.push(e),this.getAllDisplayNoneParents(e.parentNode,t)}setElementsDisplay(e,t){e.forEach((e=>{e.style.display=t}))}onElementChange(e){const t={search_field_icon_label_size:()=>this.updateInputStyle(["searchIcon"]),icon_clear_size:()=>this.updateInputStyle(["clearIcon"])};t[e]&&t[e]()}}t.default=Search}}]);