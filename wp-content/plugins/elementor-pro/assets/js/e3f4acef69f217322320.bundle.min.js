/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[93],{3093:(e,t,n)=>{var o=n(2470).__,a=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n(8300));class TemplateQueryControl extends i.default{ui(){return{...super.ui(),newButton:'button[data-action="new"]',editButton:'button[data-action="edit"]'}}events(){return{...super.events(),"click @ui.newButton":"onNewButtonClicked","click @ui.editButton":"onEditButtonClicked"}}onRender(){super.onRender(...arguments),this.toggleButtons(this.getControlValue())}onBaseInputChange(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];super.onBaseInputChange(...t),this.toggleButtons(this.getInputValue(t[0].currentTarget))}toggleButtons(e){e?this.showEditTemplateButton():this.showNewTemplateButton()}showNewTemplateButton(){const e=this.ui?.newButton?.get(0),t=this.ui?.editButton?.get(0);e&&(e.style.display="block"),t&&(t.style.display="none")}showEditTemplateButton(){const e=this.ui.newButton.get(0),t=this.ui.editButton.get(0);e&&(e.style.display="none"),t&&(t.style.display="block")}async onNewButtonClicked(){this.createTemplate()}createTemplate(){this.confirmSaveBeforeTemplateCreateDialog||(this.confirmSaveBeforeTemplateCreateDialog=elementorCommon.dialogsManager.createWidget("confirm",{id:"e-confirm-save-before-template-create",headerMessage:o("Save Changes","elementor-pro"),message:o("Would you like to save the changes you've made?","elementor-pro"),position:{my:"center center",at:"center center"},strings:{confirm:o("Save","elementor-pro"),cancel:o("Discard","elementor-pro")},onConfirm:async()=>{await this.onConfirmCreateTemplate()}})),this.confirmSaveBeforeTemplateCreateDialog.show()}async onConfirmCreateTemplate(){$e.internal("panel/state-loading");const e=await this.createAndSetTemplate();this.afterAction("new",e),$e.internal("panel/state-ready")}async createAndSetTemplate(){const e=this.model.get("name"),t=this.options.container.controls[e].actions.new.document_config.type,n=this.getTemplateSourceTypeValue(),o=await $e.data.create("library/templates",{type:t,page_settings:{source:n}}),a=parseInt(o.data.template_id);return this.setValue(a),a}getTemplateSourceTypeValue(){return"repeater"===this.options?.container?.args?.type?this.options.container.renderer.args.settings.attributes._skin||void 0:this.options.container.controls._skin?this.options.container.panel.getControlView("_skin").getControlValue():void 0}async switchDocument(e){await $e.run("editor/documents/switch",{id:parseInt(e),mode:"save"});const t=elementor.documents.getCurrent();t.config.container_attributes&&t.config.container_attributes.class&&t.$element.addClass(t.config.container_attributes.class)}async onEditButtonClicked(){this.afterAction("edit",this.getControlValue())}getSelect2Placeholder(){return{id:"",text:o("Start typing its name","elementor-pro")}}async afterAction(e,t){"switch_document"===("new"===e?this.ui.newButton[0].getAttribute("data-after-action"):this.ui.editButton[0].getAttribute("data-after-action"))?await this.switchDocument(t):window.open(this.getThemeBuilderURL(t),"_blank")}getThemeBuilderURL(e){return`${elementor.config.admin_url}post.php?post=${e}&action=elementor`}}t.default=TemplateQueryControl}}]);