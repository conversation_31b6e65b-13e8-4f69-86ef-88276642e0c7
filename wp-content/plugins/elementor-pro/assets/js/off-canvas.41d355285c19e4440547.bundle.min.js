/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[579],{9547:(e,t,n)=>{var s=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=s(n(7754)),a=s(n(5012));class OffCanvas extends elementorModules.frontend.handlers.Base{keyboardHandler=null;isOffCanvasOpenedOnce=!1;getDefaultSettings(){return{selectors:{wrapper:".e-off-canvas",overlay:".e-off-canvas__overlay",main:".e-off-canvas__main",content:".e-off-canvas__content",body:"body"}}}getDefaultElements(){const e=this.getSettings();return{$wrapper:this.$element.find(e.selectors.wrapper),$overlay:this.$element.find(e.selectors.overlay),$main:this.$element.find(e.selectors.main),$content:this.$element.find(e.selectors.content),$body:jQuery(e.selectors.body)}}onInit(){super.onInit(),this.initAriaAttributesToTriggerElements(),this.isEditingMode()?this.maybeDisableScroll():this.addClassToPreviousSiblingInsideASection()}onDestroy(){super.onDestroy(),this.enableScroll()}bindEvents(){this.elements.$overlay.on("click",(e=>{e.preventDefault(),this.onClickOverlay(e)})),elementorFrontend.elements.$window.on("keydown",this.onCanvasKeyDown.bind(this)),this.elements.$main.on("animationend animationcancel",this.removeAnimationClasses.bind(this)),elementorFrontend.elements.$window.on("elementor-pro/off-canvas/toggle-display-mode",this.handleDisplayToggle.bind(this))}unbindEvents(){this.elements.$overlay.off(),this.elements.$main.off(),elementorFrontend.elements.$window.off("keydown",this.onCanvasKeyDown),elementorFrontend.elements.$window.off("elementor-pro/off-canvas/toggle-display-mode")}handleDisplayToggle(e){if(e.originalEvent.detail.id!==this.getWidgetId())return;const t=e.originalEvent.detail.displayMode,n=this.isVisible()?"open":"close",s=""===e?.originalEvent?.detail?.previousEvent?.pointerType;"open"===t?this.openOffCanvas(s):"close"===t?this.closeOffCanvas():"toggle"===t&&this["open"===n?"closeOffCanvas":"openOffCanvas"](s)}openOffCanvas(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.isVisible()||this.isInsideCarousel()||(this.elements.$wrapper.attr("aria-hidden","false"),this.elements.$wrapper.removeAttr("inert"),this.elements.$wrapper.removeAttr("data-delay-child-handlers"),this.updateAriaExpandedOfTriggerElements("true"),this.toggleDraggable(!1),this.maybeOnOpenAnimation(),this.maybeDisableScroll(),this.handleElementHandlers(),e&&this.handleKeyboardA11y())}handleKeyboardA11y(){this.initKeyboardHandler(),this.keyboardHandler.onOpenModal()}closeOffCanvas(){this.isVisible()&&(this.maybeOnCloseAnimation(),this.elements.$wrapper.attr("aria-hidden","true"),this.elements.$wrapper.attr("inert",""),this.updateAriaExpandedOfTriggerElements("false"),this.toggleDraggable(!0),this.enableScroll())}onCanvasKeyDown(e){"Escape"===e.key&&"yes"!==this.getElementSettings("is_not_close_on_esc_overlay")&&this.closeOffCanvas()}onClickOverlay(){"yes"===this.getElementSettings().is_not_close_on_overlay||this.isEditingMode()||this.closeOffCanvas()}maybeOnOpenAnimation(){const e=this.getResponsiveSetting("entrance_animation")||"none";"none"===e?this.elements.$wrapper.addClass("no-animation"):this.elements.$wrapper.removeClass("no-animation"),this.elements.$main.addClass(`animated ${e}`),this.elements.$wrapper.removeClass("animated-reverse-wrapper")}maybeOnCloseAnimation(){const e=this.getResponsiveSetting("exit_animation")||"none";"none"===e?this.elements.$wrapper.addClass("no-animation"):this.elements.$wrapper.removeClass("no-animation"),this.elements.$main.addClass(`animated reverse ${e}`),this.elements.$wrapper.addClass("animated-reverse-wrapper"),this.elements.$body.addClass("e-off-canvas__no-scroll-animation")}removeAnimationClasses(){const e=this.elements.$main.hasClass("reverse"),t=this.getResponsiveSetting("entrance_animation")||"none",n=this.getResponsiveSetting("exit_animation")||"none";e?(this.elements.$main.removeClass(`animated reverse ${n}`),this.elements.$wrapper.removeClass("animated-reverse-wrapper"),this.elements.$body.removeClass("e-off-canvas__no-scroll-animation"),this.keyboardHandler?.onCloseModal()):this.elements.$main.removeClass(`animated ${t}`)}getResponsiveSetting(e){const t=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),e,"",t)}isEditingMode(){return"yes"===this.getElementSettings("editing_mode")&&elementorFrontend.isEditMode()}maybeDisableScroll(){"yes"===this.getElementSettings("prevent_scroll")&&this.elements.$body.addClass("e-off-canvas__no-scroll")}enableScroll(){this.elements.$body.removeClass("e-off-canvas__no-scroll")}toggleDraggable(e){elementorFrontend.isEditMode()&&"0"!==this.elements.$overlay.css("opacity")&&this.$element.attr("draggable",e)}handleEditingModeToggle(){"yes"===this.getElementSettings("editing_mode")?this.openOffCanvas():this.closeOffCanvas()}getKeyboardHandlingConfig(){return{$modalElements:this.elements.$wrapper,$elementWrapper:this.elements.$content,modalType:"off-canvas",modalId:this.getID()}}initAriaAttributesToTriggerElements(){this.getTriggerElements().forEach((e=>{e.setAttribute("aria-controls",`off-canvas-${this.getID()}`),e.setAttribute("aria-expanded","false")}))}getTriggerElements(){const e=elementorFrontend.elements.window.document.body.querySelectorAll("a"),t=Array.from(e).filter((e=>e.href?.includes("elementor-action"))),n=[];return t.forEach((e=>{if(!this.isActionUrlIdEqualToWidgetId(e.href))return!1;n.push(e)})),n}updateAriaExpandedOfTriggerElements(e){elementorFrontend.elements.window.document.body.querySelectorAll(`[aria-controls="off-canvas-${this.getID()}"]`).forEach((t=>{t.setAttribute("aria-expanded",e)}))}isActionUrlIdEqualToWidgetId(e){let t={};const n=decodeURIComponent(e).match(/settings=(.+)/);return n&&(t=JSON.parse(atob(n[1]))),this.getID()===t?.id}isVisible(){return"false"===this.elements.$wrapper.attr("aria-hidden")}maybeDragWidgetsBeneathOverlay(){this.elements.$overlay.toggleClass("no-pointer-events")}onElementChange(e){"editing_mode"===e&&this.handleEditingModeToggle(),"has_overlay"===e&&this.maybeDragWidgetsBeneathOverlay("has_overlay")}handleElementHandlers(){this.isOffCanvasOpenedOnce||((0,a.default)(this.elements.$main[0].querySelectorAll(".e-off-canvas__content")),this.isOffCanvasOpenedOnce=!0)}initKeyboardHandler(){this.keyboardHandler||(this.keyboardHandler=new i.default(this.getKeyboardHandlingConfig()))}addClassToPreviousSiblingInsideASection(){if(!this.$element[0].closest(".elementor-section"))return;const e=this.$element[0].previousElementSibling;e?.classList.add("e-element-before-off-canvas")}getWidgetId(){if(!(this.$element.closest(".e-loop-item").length>0))return this.getID().toString();const e=this.elements.$wrapper.attr("id");return e?e.replace("off-canvas-",""):this.getID().toString()}isInsideCarousel(){return this.$element.closest(".swiper-wrapper").length>0}}t.default=OffCanvas}}]);