/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[404],{3827:(e,t,s)=>{var i=s(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(s(1234));class TOCHandler extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{widgetContainer:".elementor-widget-container",postContentContainer:'.elementor:not([data-elementor-type="header"]):not([data-elementor-type="footer"]):not([data-elementor-type="popup"])',expandButton:".elementor-toc__toggle-button--expand",collapseButton:".elementor-toc__toggle-button--collapse",body:".elementor-toc__body",headerTitle:".elementor-toc__header-title"},classes:{anchor:"elementor-menu-anchor",listWrapper:"elementor-toc__list-wrapper",listItem:"elementor-toc__list-item",listTextWrapper:"elementor-toc__list-item-text-wrapper",firstLevelListItem:"elementor-toc__top-level",listItemText:"elementor-toc__list-item-text",activeItem:"elementor-item-active",headingAnchor:"elementor-toc__heading-anchor",collapsed:"elementor-toc--collapsed"},listWrapperTag:"numbers"===this.getElementSettings().marker_view?"ol":"ul"}}getDefaultElements(){const e=this.getSettings();let t=this.$element.find(e.selectors.widgetContainer);return 0===t.length&&(t=this.$element),{$pageContainer:this.getContainer(),$widgetContainer:t,$expandButton:this.$element.find(e.selectors.expandButton),$collapseButton:this.$element.find(e.selectors.collapseButton),$tocBody:this.$element.find(e.selectors.body),$listItems:this.$element.find("."+e.classes.listItem)}}getContainer(){const e=this.getElementSettings();if(e.container)return jQuery(n.default.sanitize(e.container));const t=this.$element.parents(".elementor");if("popup"===t.attr("data-elementor-type"))return t;const s=this.getSettings();return jQuery(s.selectors.postContentContainer)}bindEvents(){const e=this.getElementSettings();e.minimize_box&&(this.elements.$expandButton.on("click",(()=>this.expandBox())).on("keyup",(e=>this.triggerClickOnEnterSpace(e))),this.elements.$collapseButton.on("click",(()=>this.collapseBox())).on("keyup",(e=>this.triggerClickOnEnterSpace(e)))),e.collapse_subitems&&this.elements.$listItems.on("hover",(e=>jQuery(e.target).slideToggle()))}getHeadings(){const e=this.getElementSettings(),t=e.headings_by_tags.join(","),s=this.getSettings("selectors"),i=e.exclude_headings_by_selector;return this.elements.$pageContainer.find(t).not(s.headerTitle).filter(((e,t)=>!jQuery(t).closest(i).length))}addAnchorsBeforeHeadings(){const e=this.getSettings("classes");this.elements.$headings.before((t=>{if(!jQuery(this.elements.$headings[t]).data("hasOwnID"))return`<span id="${e.headingAnchor}-${t}" class="${e.anchor} "></span>`}))}activateItem(e){const t=this.getSettings("classes");if(this.deactivateActiveItem(e),e.addClass(t.activeItem),this.$activeItem=e,!this.getElementSettings("collapse_subitems"))return;let s;s=e.hasClass(t.firstLevelListItem)?e.parent().next():e.parents("."+t.listWrapper).eq(-2),s.length?(this.$activeList=s,this.$activeList.stop().slideDown()):delete this.$activeList}deactivateActiveItem(e){if(!this.$activeItem||this.$activeItem.is(e))return;const{classes:t}=this.getSettings();this.$activeItem.removeClass(t.activeItem),!this.$activeList||e&&this.$activeList[0].contains(e[0])||this.$activeList.slideUp()}followAnchor(e,t){const s=e[0].hash;let i;try{i=jQuery(decodeURIComponent(s))}catch(e){return}this.createObserver(s,i,{rootMargin:"0px",threshold:0},e,t).observe(i[0])}createObserver(e,t,s,i,n){let l=0;return new IntersectionObserver((e=>{e.forEach((e=>{const s=document.documentElement.scrollTop,a=s>l,o=t.attr("id");e.isIntersecting&&!this.itemClicked?(this.viewportItems[o]=!0,this.activateItem(i)):e.isIntersecting&&a?(delete this.viewportItems[o],Object.keys(this.viewportItems).length&&this.activateItem(this.$listItemTexts.eq(n+1))):a||(delete this.viewportItems[o],this.activateItem(this.$listItemTexts.eq(n-1))),l=s<=0?0:s}))}),s)}followAnchors(){this.$listItemTexts.each(((e,t)=>this.followAnchor(jQuery(t),e)))}populateTOC(){this.listItemPointer=0;this.getElementSettings().hierarchical_view?this.createNestedList():this.createFlatList(),this.$listItemTexts=this.$element.find(".elementor-toc__list-item-text"),this.$listItemTexts.on("click",this.onListItemClick.bind(this)),elementorFrontend.isEditMode()||this.followAnchors()}createNestedList(){this.headingsData.forEach(((e,t)=>{e.level=0;for(let s=t-1;s>=0;s--){const t=this.headingsData[s];if(t.tag<=e.tag){e.level=t.level,t.tag<e.tag&&e.level++;break}}})),this.elements.$tocBody.html(this.getNestedLevel(0))}createFlatList(){this.elements.$tocBody.html(this.getNestedLevel())}getNestedLevel(e){const t=this.getSettings(),s=this.getElementSettings(),i=this.getElementSettings("icon");let l;i&&(l=elementorFrontend.config.experimentalFeatures.e_font_icon_svg&&!elementorFrontend.isEditMode()?void 0!==i.rendered_tag?i.rendered_tag:"":i.value?`<i class="${i.value}"></i>`:"");let a=`<${t.listWrapperTag} class="${t.classes.listWrapper}">`;for(;this.listItemPointer<this.headingsData.length;){const o=this.headingsData[this.listItemPointer];let r=t.classes.listItemText;if(0===o.level&&(r+=" "+t.classes.firstLevelListItem),e>o.level)break;if(e===o.level){a+=`<li class="${t.classes.listItem}">`,a+=`<div class="${t.classes.listTextWrapper}">`;let h=`<a href="#${o.anchorLink}" class="${r}">${o.text}</a>`;"bullets"===s.marker_view&&i&&(h=`${l}${h}`),h=n.default.sanitize(h),a+=h,a+="</div>",this.listItemPointer++;const c=this.headingsData[this.listItemPointer];c&&e<c.level&&(a+=this.getNestedLevel(c.level)),a+="</li>"}}return a+=`</${t.listWrapperTag}>`,a}handleNoHeadingsFound(){const e=this.getElementSettings("no_headings_message");return this.elements.$tocBody.html(e)}collapseBodyListener(){const e=elementorFrontend.breakpoints.getActiveBreakpointsList({withDesktop:!0}),t=this.getElementSettings("minimized_on"),s=elementorFrontend.getCurrentDeviceMode(),i=this.$element.hasClass(this.getSettings("classes.collapsed"));"desktop"===t||e.indexOf(t)>=e.indexOf(s)?i||this.collapseBox(!1):i&&this.expandBox(!1)}onElementChange(e){"minimized_on"===e&&this.collapseBodyListener()}getHeadingAnchorLink(e,t){const s=this.elements.$headings[e].id,i=this.elements.$headings[e].closest(".elementor-widget").id;let n="";return s?n=s:i&&(n=i),s||i?jQuery(this.elements.$headings[e]).data("hasOwnID",!0):n=`${t.headingAnchor}-${e}`,n}setHeadingsData(){this.headingsData=[];const e=this.getSettings("classes");this.elements.$headings.each(((t,s)=>{const i=this.getHeadingAnchorLink(t,e);this.headingsData.push({tag:+s.nodeName.slice(1),text:s.textContent,anchorLink:i})}))}run(){if(this.elements.$headings=this.getHeadings(),!this.elements.$headings.length)return this.handleNoHeadingsFound();this.setHeadingsData(),elementorFrontend.isEditMode()||this.addAnchorsBeforeHeadings(),this.populateTOC(),this.getElementSettings("minimize_box")&&this.collapseBodyListener()}expandBox(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.getCurrentDeviceSetting("min_height");this.$element.removeClass(this.getSettings("classes.collapsed")),this.elements.$tocBody.slideDown(),this.elements.$expandButton.attr("aria-expanded","true"),this.elements.$collapseButton.attr("aria-expanded","true"),this.elements.$widgetContainer.css("min-height",t.size+t.unit),e&&this.elements.$collapseButton.trigger("focus")}collapseBox(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.$element.addClass(this.getSettings("classes.collapsed")),this.elements.$tocBody.slideUp(),this.elements.$expandButton.attr("aria-expanded","false"),this.elements.$collapseButton.attr("aria-expanded","false"),this.elements.$widgetContainer.css("min-height","0px"),e&&this.elements.$expandButton.trigger("focus")}triggerClickOnEnterSpace(e){13!==e.keyCode&&32!==e.keyCode||(e.currentTarget.click(),e.stopPropagation())}onInit(){super.onInit(...arguments),this.viewportItems=[],jQuery((()=>this.run()))}onListItemClick(e){this.itemClicked=!0,setTimeout((()=>this.itemClicked=!1),2e3);const t=jQuery(e.target),s=t.parent().next(),i=this.getElementSettings("collapse_subitems");let n;i&&t.hasClass(this.getSettings("classes.firstLevelListItem"))&&s.is(":visible")&&(n=!0),this.activateItem(t),i&&n&&s.slideUp()}}t.default=TOCHandler}}]);