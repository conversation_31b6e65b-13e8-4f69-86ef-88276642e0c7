/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[993],{2098:(e,t,n)=>{var o=n(2470).__;Object.defineProperty(t,"__esModule",{value:!0}),t.SAVE_CONTEXT=t.EDIT_CONTEXT=void 0,t.createElement=createElement,t.default=function addDocumentHandle(e){let{element:t,id:n,title:a=o("Template","elementor-pro")}=e,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l,m=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(l===d){if(!n||!t)throw Error("`id` and `element` are required.");if(function isCurrentlyEditing(e){return e.classList.contains(i)}(t)||function hasHandle(e){return!!e.querySelector(`:scope > .${s}`)}(t))return}const u=function createHandleElement(e,t){let{title:n,onClick:i}=e,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const d=["header","footer"].includes(a?.dataset.elementorType)?"%s":o("Edit %s","elementor-pro"),m=createElement({tag:"div",classNames:[`${s}__inner`],children:[createElement({tag:"i",classNames:[getHandleIcon(t)]}),createElement({tag:"div",classNames:[`${l===t?s:r}__title`],children:[document.createTextNode(l===t?d.replace("%s",n):o("Save %s","elementor-pro").replace("%s",n))]})]}),c=[s];l!==t&&c.push(r);const u=createElement({tag:"div",classNames:c,children:[m]});return u.addEventListener("click",i),u}({title:a,onClick:()=>async function onDocumentClick(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;l===t?(window.top.$e.internal("panel/state-loading"),await window.top.$e.run("editor/documents/switch",{id:parseInt(e),onClose:n,selector:o}),window.top.$e.internal("panel/state-ready")):(elementorCommon.api.internal("panel/state-loading"),elementorCommon.api.run("editor/documents/switch",{id:elementor.config.initial_document.id,mode:"save",shouldScroll:!1,selector:o}).finally((()=>elementorCommon.api.internal("panel/state-ready"))))}(n,d,m,c)},d,t);t.prepend(u),l===d&&(t.dataset.editableElementorDocument=n)};const s="elementor-document-handle",i="elementor-edit-mode",l=t.EDIT_CONTEXT="edit",r="elementor-document-save-back-handle",a=t.SAVE_CONTEXT="save";function getHandleIcon(e){let t="eicon-edit";return a===e&&(t=elementorFrontend.config.is_rtl?"eicon-arrow-right":"eicon-arrow-left"),t}function createElement(e){let{tag:t,classNames:n=[],children:o=[]}=e;const s=document.createElement(t);return s.classList.add(...n),o.forEach((e=>s.appendChild(e))),s}},2813:(e,t,n)=>{var o=n(2470).__,s=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=s(n(2195)),l=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var l=s?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}return o.default=e,n&&n.set(e,o),o}(n(2098));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}class Loop extends i.default{getSkinPrefix(){return""}getDefaultSettings(){const e=super.getDefaultSettings();return e.selectors.post=".elementor-loop-container .elementor",e.selectors.postsContainer=".elementor-loop-container",e.classes.inPlaceTemplateEditable="elementor-in-place-template-editable",e}fitImages(){}getVerticalSpaceBetween(){return elementorProFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"row_gap","size")}onInPlaceEditTemplate(){this.$element.addClass(this.getDefaultSettings().classes.inPlaceTemplateEditable),this.elementsToRemove=[],this.handleSwiper();const e=this.getElementSettings("template_id");this.elementsToRemove=[...this.elementsToRemove,"style#loop-"+e,"link#font-loop-"+e,"style#loop-dynamic-"+e],this.elementsToRemove.forEach((e=>{this.$element.find(e).remove()}))}handleSwiper(){const e=this.elements.$postsContainer.data("swiper");e&&(e.slideTo(0),e.autoplay.pause(),e.allowTouchMove=!1,e.params.autoplay.delay=1e6,e.update(),this.elementsToRemove=[...this.elementsToRemove,".swiper-pagination",".elementor-swiper-button",".elementor-document-handle"])}attachEditDocumentHandle(){const e=this.getElementSettings("template_id");if(!e)return;const t=this.getElementSettings(),n=`.elementor-element-${this.getID()}`,s=t?.edit_handle_selector+('[data-elementor-type="loop-item"]'===t?.edit_handle_selector?`.elementor-${e}`:""),i=this.$element.find(s).first()[0];i&&(this.isFirstEdit()?this.$element.find(".elementor-swiper-button").remove():(0,l.default)({element:i,title:o("Template","elementor-pro"),id:e},l.EDIT_CONTEXT,(()=>this.onInPlaceEditTemplate()),`${n} .elementor-${e}`))}isFirstEdit(){return this.$element.has(".e-loop-first-edit").length}handleCTA(){const e=document.querySelector(`[data-id="${this.getID()}"] .e-loop-empty-view__wrapper`);if(!e)return;const t=e.attachShadow({mode:"open"});t.appendChild(elementorPro.modules.loopBuilder.getCtaStyles()),t.appendChild(elementorPro.modules.loopBuilder.getCtaContent(this.getWidgetType()));t.querySelector(".e-loop-empty-view__box-cta").addEventListener("click",(()=>{elementorPro.modules.loopBuilder.createTemplate()}))}doEditorInitAction(){elementor.hooks.doAction("editor/widgets/loop-grid/on-init",this)}onElementChange(e){"_skin"===e&&elementorPro.modules.loopBuilder.onApplySkinChange(),i.default.prototype.onElementChange.apply(this)}bindEvents(){super.bindEvents(),elementorFrontend.elements.$window.on("elementor-pro/loop-builder/after-insert-posts",this.reInitMasonry.bind(this))}reInitMasonry(){const e=this.getSettings("selectors");this.elements.$posts=jQuery(`.elementor-element-${this.getID()} ${e.post}`),super.runMasonry()}unbindEvents(){super.unbindEvents(),elementorFrontend.elements.$window.off("elementor-pro/loop-builder/after-insert-posts",this.reInitMasonry.bind(this))}onInit(){super.onInit(...arguments),elementorFrontend.isEditMode()&&(this.doEditorInitAction(),this.attachEditDocumentHandle(),this.handleCTA())}}t.default=Loop},2195:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=elementorModules.frontend.handlers.Base.extend({getSkinPrefix:()=>"classic_",bindEvents(){elementorFrontend.addListenerOnce(this.getModelCID(),"resize",this.onWindowResize)},unbindEvents(){elementorFrontend.removeListeners(this.getModelCID(),"resize",this.onWindowResize)},getClosureMethodsNames(){return elementorModules.frontend.handlers.Base.prototype.getClosureMethodsNames.apply(this,arguments).concat(["fitImages","onWindowResize","runMasonry"])},getDefaultSettings:()=>({classes:{fitHeight:"elementor-fit-height",hasItemRatio:"elementor-has-item-ratio"},selectors:{postsContainer:".elementor-posts-container",post:".elementor-post",postThumbnail:".elementor-post__thumbnail",postThumbnailImage:".elementor-post__thumbnail img"}}),getDefaultElements(){var e=this.getSettings("selectors");return{$postsContainer:this.$element.find(e.postsContainer),$posts:this.$element.find(e.post)}},fitImage(e){var t=this.getSettings(),n=e.find(t.selectors.postThumbnail),o=n.find("img")[0];if(o){var s=n.outerHeight()/n.outerWidth(),i=o.naturalHeight/o.naturalWidth;n.toggleClass(t.classes.fitHeight,i<s)}},fitImages(){var e=jQuery,t=this,n=getComputedStyle(this.$element[0],":after").content,o=this.getSettings();t.isMasonryEnabled()?this.elements.$postsContainer.removeClass(o.classes.hasItemRatio):(this.elements.$postsContainer.toggleClass(o.classes.hasItemRatio,!!n.match(/\d/)),this.elements.$posts.each((function(){var n=e(this),s=n.find(o.selectors.postThumbnailImage);t.fitImage(n),s.on("load",(function(){t.fitImage(n)}))})))},setColsCountSettings(){const e=this.getElementSettings(),t=this.getSkinPrefix(),n=elementorProFrontend.utils.controls.getResponsiveControlValue(e,`${t}columns`);this.setSettings("colsCount",n)},isMasonryEnabled(){return!!this.getElementSettings(this.getSkinPrefix()+"masonry")},initMasonry(){imagesLoaded(this.elements.$posts,this.runMasonry)},getVerticalSpaceBetween(){let e=elementorProFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),`${this.getSkinPrefix()}row_gap`,"size");return""===this.getSkinPrefix()&&""===e&&(e=this.getElementSettings("item_gap.size")),e},runMasonry(){var e=this.elements;e.$posts.css({marginTop:"",transitionDuration:""}),this.setColsCountSettings();var t=this.getSettings("colsCount"),n=this.isMasonryEnabled()&&t>=2;if(e.$postsContainer.toggleClass("elementor-posts-masonry",n),!n)return void e.$postsContainer.height("");const o=this.getVerticalSpaceBetween();new elementorModules.utils.Masonry({container:e.$postsContainer,items:e.$posts.filter(":visible"),columnsCount:this.getSettings("colsCount"),verticalSpaceBetween:o||0}).run()},run(){setTimeout(this.fitImages,0),this.initMasonry()},onInit(){elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),this.bindEvents(),this.run()},onWindowResize(){this.fitImages(),this.runMasonry()},onElementChange(){this.fitImages(),setTimeout(this.runMasonry)}})}}]);