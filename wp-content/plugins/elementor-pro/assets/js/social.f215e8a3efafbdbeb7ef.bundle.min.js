/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[158],{5070:(e,o)=>{Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;class FacebookHandler extends elementorModules.frontend.handlers.Base{getConfig(){return elementorProFrontend.config.facebook_sdk}setConfig(e,o){elementorProFrontend.config.facebook_sdk[e]=o}parse(){FB.XFBML.parse(this.$element[0])}loadSDK(){const e=this.getConfig();e.isLoading||e.isLoaded||(this.setConfig("isLoading",!0),jQuery.ajax({url:"https://connect.facebook.net/"+e.lang+"/sdk.js",dataType:"script",cache:!0,success:()=>{FB.init({appId:e.app_id,version:"v2.10",xfbml:!1}),this.setConfig("isLoaded",!0),this.setConfig("isLoading",!1),elementorFrontend.elements.$document.trigger("fb:sdk:loaded")}}))}onInit(){super.onInit(...arguments),this.loadSDK();this.getConfig().isLoaded?this.parse():elementorFrontend.elements.$document.on("fb:sdk:loaded",(()=>this.parse()))}}o.default=FacebookHandler}}]);