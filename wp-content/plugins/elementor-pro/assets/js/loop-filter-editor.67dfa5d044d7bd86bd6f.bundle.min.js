/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[994],{1865:(e,t,o)=>{var n=o(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o(225));class LoopFilter extends elementorModules.editor.utils.Module{onElementorInit(){this.taxonomyFilter=new r.default("taxonomy-filter")}}t.default=LoopFilter},225:(e,t,o)=>{var n=o(2470).__;const r=o(7842);e.exports=r.extend({__construct(){this.cache={},r.prototype.__construct.apply(this,arguments)},onInit(){elementor.channels.editor.on("editor:widget:taxonomy-filter:section_taxonomy_filter:activated",this.onTaxonomyFilterSectionActive)},onTaxonomyFilterSectionActive(){this.updateSelectedElementOptions();const e=this.getEditorControlView("selected_element").getControlValue();e&&this.updateTaxonomyOptions(e)},updateSelectedElementOptions(){const e=this.getEditorControlView("selected_element"),t=e.getControlValue();(t?elementor.$previewContents[0].querySelector(`[data-elementor-id="${elementor.config.document.id}"] .elementor-element-${t}`):"")||e.setValue("");const o=elementor.$previewContents[0].querySelectorAll(`[data-elementor-id="${elementor.config.document.id}"] .elementor-widget-loop-grid`),r={"":n("Select a widget","elementor-pro")};o.length||(this.updateOptions("selected_element",r),e.setValue(""));let s=1;for(const e of o)r[e.dataset.id]=`${n("Loop Grid","elementor-pro")} ${s++}`;this.updateOptions("selected_element",r)},onElementChange(e,t){if("selected_element"!==e)return;const o=t.getControlValue();o?this.updateTaxonomyOptions(o):this.updateOptions("taxonomy",{"":n("Select a taxonomy","elementor-pro")})},getPostSourceQueryPostType(e){const t=e.settings.attributes.post_query_post_type;let o="";switch(t){case"current_query":o=elementorPro.config.loopFilter.mainQueryPostType;break;case"by_id":case"related":o="post";break;default:o=t}return o},getLoopQueryPostType(e){const t=elementor.getContainer(e);return"post"===t.settings.attributes._skin?this.getPostSourceQueryPostType(t):"product"},updateTaxonomyOptions(e){const t=this.getLoopQueryPostType(e);return this.getPostTypeTaxonomies(t).then((e=>e instanceof Response?!e.ok||400<=e.status?(this.displayErrorDialog(),{}):e.json():e)).catch((()=>(this.displayErrorDialog(),{}))).then((e=>{let o=e?.data||e;Object.keys(o).length?(o={"":n("Select a taxonomy","elementor-pro"),...o},this.cache[t]=o,this.updateOptions("taxonomy",o)):this.updateOptions("taxonomy",{"":n("No taxonomies found","elementor-pro")})}))},getPostTypeTaxonomies(e){return this.cache[e]&&Object.keys(this.cache[e]).length?Promise.resolve(this.cache[e]):this.fetchPostTypeTaxonomies(e)},fetchPostTypeTaxonomies:e=>fetch(`${elementorCommon.config.urls.rest}elementor-pro/v1/get-post-type-taxonomies`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":elementorWebCliConfig.nonce},body:JSON.stringify({post_type:e})}),displayErrorDialog(){elementorCommon.dialogsManager.createWidget("alert",{id:"e-filter-error-message",className:"e-filter__error-message",headerMessage:n("Something went wrong","elementor-pro"),message:n("We are experiencing technical difficulties on our end. Please try again to reconnect.","elementor-pro"),position:{my:"center center",at:"center center"},strings:{confirm:n("OK","elementor-pro")}}).show()}})}}]);