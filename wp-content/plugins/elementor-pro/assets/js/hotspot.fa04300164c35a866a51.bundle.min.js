/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[292],{507:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;class Hotspot extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{hotspot:".e-hotspot",tooltip:".e-hotspot__tooltip"}}}getDefaultElements(){const t=this.getSettings("selectors");return{$hotspot:this.$element.find(t.hotspot),$hotspotsExcludesLinks:this.$element.find(t.hotspot).filter(":not(.e-hotspot--no-tooltip)"),$tooltip:this.$element.find(t.tooltip)}}bindEvents(){const t=this.getCurrentDeviceSetting("tooltip_trigger"),e="mouseenter"===t?"mouseleave mouseenter":t;"none"!==e&&this.elements.$hotspotsExcludesLinks.on(e,(t=>this.onHotspotTriggerEvent(t)))}onDeviceModeChange(){this.elements.$hotspotsExcludesLinks.off(),this.bindEvents()}onHotspotTriggerEvent(t){const e=jQuery(t.target),o=e.closest(".e-hotspot__button").length,s="mouseleave"===t.type&&(e.is(".e-hotspot--tooltip-position")||e.parents(".e-hotspot--tooltip-position").length),n="mobile"===elementorFrontend.getCurrentDeviceMode();if(!(e.closest(".e-hotspot--link").length&&n&&("mouseleave"===t.type||"mouseenter"===t.type))&&(o||s)){const e=jQuery(t.currentTarget);this.elements.$hotspot.not(e).removeClass("e-hotspot--active"),e.toggleClass("e-hotspot--active")}}editorAddSequencedAnimation(){this.elements.$hotspot.toggleClass("e-hotspot--sequenced","yes"===this.getElementSettings("hotspot_sequenced_animation"))}hotspotSequencedAnimation(){const t=this.getElementSettings();if("no"===t.hotspot_sequenced_animation)return;const e=elementorModules.utils.Scroll.scrollObserver({callback:o=>{o.isInViewport&&(e.unobserve(this.$element[0]),this.elements.$hotspot.each(((e,o)=>{if(0===e)return;const s=t.hotspot_sequenced_animation_duration,n=e*((s?s.size:1e3)/this.elements.$hotspot.length);o.style.animationDelay=n+"ms"})))}});e.observe(this.$element[0])}setTooltipPositionControl(){const t=this.getElementSettings();void 0!==t.tooltip_animation&&t.tooltip_animation.match(/^e-hotspot--(slide|fade)-direction/)&&(this.elements.$tooltip.removeClass("e-hotspot--tooltip-animation-from-left e-hotspot--tooltip-animation-from-top e-hotspot--tooltip-animation-from-right e-hotspot--tooltip-animation-from-bottom"),this.elements.$tooltip.addClass("e-hotspot--tooltip-animation-from-"+t.tooltip_position))}onInit(){super.onInit(...arguments),this.hotspotSequencedAnimation(),this.setTooltipPositionControl(),window.elementor&&elementor.listenTo(elementor.channels.deviceMode,"change",(()=>this.onDeviceModeChange()))}onElementChange(t){t.startsWith("tooltip_position")&&this.setTooltipPositionControl(),t.startsWith("hotspot_sequenced_animation")&&this.editorAddSequencedAnimation()}}e.default=Hotspot}}]);