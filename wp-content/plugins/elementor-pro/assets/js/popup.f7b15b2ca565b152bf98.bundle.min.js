/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[887],{5985:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=elementorModules.frontend.handlers.Base.extend({getDefaultSettings:()=>({selectors:{form:".elementor-form"}}),getDefaultElements(){var e=this.getSettings("selectors"),t={};return t.$form=this.$element.find(e.form),t},bindEvents(){this.elements.$form.on("submit_success",this.handleFormAction)},handleFormAction(e,t){if(void 0===t.data.popup)return;const o=t.data.popup;if("open"===o.action)return elementorProFrontend.modules.popup.showPopup(o);setTimeout((()=>elementorProFrontend.modules.popup.closePopup(o,e)),1e3)}})}}]);