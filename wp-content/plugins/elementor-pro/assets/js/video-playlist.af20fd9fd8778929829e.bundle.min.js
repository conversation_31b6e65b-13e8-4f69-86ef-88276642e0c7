/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[519],{281:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class baseTabs extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{tablist:'[role="tablist"]',tabTitle:".e-tab-title",tabContent:".e-tab-content"},classes:{active:"e-active"},showTabFn:"show",hideTabFn:"hide",toggleSelf:!0,hidePrevious:!0,autoExpand:!0,keyDirection:{ArrowLeft:elementorFrontendConfig.is_rtl?1:-1,ArrowUp:-1,ArrowRight:elementorFrontendConfig.is_rtl?-1:1,ArrowDown:1}}}getDefaultElements(){const e=this.getSettings("selectors");return{$tabTitles:this.findElement(e.tabTitle),$tabContents:this.findElement(e.tabContent)}}activateDefaultTab(e){const t=this.getSettings();if(!t.autoExpand||"editor"===t.autoExpand&&!this.isEdit)return;const i=this.getEditSettings("activeItemIndex")||e||1,s={showTabFn:t.showTabFn,hideTabFn:t.hideTabFn};this.setSettings({showTabFn:"show",hideTabFn:"hide"}),this.changeActiveTab(i),this.setSettings(s)}handleKeyboardNavigation(e){const t=e.currentTarget,i=jQuery(t.closest(this.getSettings("selectors").tablist)),s=i.find(this.getSettings("selectors").tabTitle),a="vertical"===i.attr("aria-orientation");switch(e.key){case"ArrowLeft":case"ArrowRight":if(a)return;break;case"ArrowUp":case"ArrowDown":if(!a)return;e.preventDefault();break;case"Home":return e.preventDefault(),void s.first().trigger("focus");case"End":return e.preventDefault(),void s.last().trigger("focus");default:return}const n=t.getAttribute("data-tab")-1,r=this.getSettings("keyDirection")[e.key],l=s[n+r];l?l.focus():-1===n+r?s.last().trigger("focus"):s.first().trigger("focus")}deactivateActiveTab(e){const t=this.getSettings(),i=t.classes.active,s=e?'[data-tab="'+e+'"]':"."+i,a=this.elements.$tabTitles.filter(s),n=this.elements.$tabContents.filter(s);a.add(n).removeClass(i),a.attr({tabindex:"-1","aria-selected":"false"}),n[t.hideTabFn](),n.attr("hidden","hidden")}activateTab(e){const t=this.getSettings(),i=t.classes.active,s=this.elements.$tabTitles.filter('[data-tab="'+e+'"]'),a=this.elements.$tabContents.filter('[data-tab="'+e+'"]'),n="show"===t.showTabFn?0:400;s.add(a).addClass(i),s.attr({tabindex:"0","aria-selected":"true"}),a[t.showTabFn](n,(()=>elementorFrontend.elements.$window.trigger("resize"))),a.removeAttr("hidden")}isActiveTab(e){return this.elements.$tabTitles.filter('[data-tab="'+e+'"]').hasClass(this.getSettings("classes.active"))}bindEvents(){this.elements.$tabTitles.on({keydown:e=>{jQuery(e.target).is("a")&&"Enter"===e.key&&e.preventDefault(),["End","Home","ArrowUp","ArrowDown"].includes(e.key)&&this.handleKeyboardNavigation(e)},keyup:e=>{switch(e.key){case"ArrowLeft":case"ArrowRight":this.handleKeyboardNavigation(e);break;case"Enter":case"Space":e.preventDefault(),this.changeActiveTab(e.currentTarget.getAttribute("data-tab"))}},click:e=>{e.preventDefault(),this.changeActiveTab(e.currentTarget.getAttribute("data-tab"))}})}onInit(){super.onInit(...arguments)}changeActiveTab(e){const t=this.isActiveTab(e),i=this.getSettings();!i.toggleSelf&&t||!i.hidePrevious||this.deactivateActiveTab(),!i.hidePrevious&&t&&this.deactivateActiveTab(e),t||this.activateTab(e)}}t.default=baseTabs},5120:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=async function triggerEvent(e,t,i,s){const n=new a.default({event:await getEventEventObject(e,t,i,s),tab:getEventTabsObject(e),playlist:getEventPlaylistObject(e,s),video:getEventVideoObject(e,s)});jQuery("body").trigger("elementor-video-playList",n)};var a=s(i(2374));function getEventTabsObject(e){const t=e.elements.$innerTabs.filter(".e-active").find(".e-inner-tabs-wrapper .e-inner-tab-title");if(t.length){const e=t.filter(".e-inner-tab-active");return{name:e.text().trim(),index:e.index()+1}}return{name:"none",index:"none"}}function getEventPlaylistObject(e,t){const i=t||e.currentPlaylistItemIndex;return{name:e.getElementSettings("playlist_title"),currentItem:i,amount:e.playlistItemsArray.filter((e=>"section"!==e.videoType)).length}}function getEventVideoObject(e,t){const i=t||e.currentPlaylistItemIndex,s=e.playlistItemsArray[i-1];return{provider:s.videoType,url:s.videoUrl,title:s.videoTitle,duration:s.videoDuration}}async function getEventEventObject(e,t,i,s){const a=s||e.currentPlaylistItemIndex,n=e.playlistItemsArray[a-1];return{type:t,time:await n.playerInstance.getCurrentTime(),element:e.$element,trigger:i,watchCount:n.playerInstance.watchCount}}},4161:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=s(i(281)),n=s(i(8200)),r=s(i(9369)),l=s(i(3650)),o=i(7712),d=i(9642),h=i(7571),c=s(i(5120));class VideoPlaylistHandler extends a.default{getDefaultSettings(){const e=super.getDefaultSettings();return{...e,selectors:{...e.selectors,tabsWrapper:".e-tabs-items-wrapper",tabsItems:".e-tabs-items",toggleVideosDisplayButton:".e-tabs-toggle-videos-display-button",videos:".e-tabs-content-wrapper .e-tab-content",innerTabs:".e-tabs-inner-tabs .e-tab-content",imageOverlay:".elementor-custom-embed-image-overlay"}}}getDefaultElements(){const e=super.getDefaultElements(),t=this.getSettings("selectors");return{...e,$tabsWrapper:this.findElement(t.tabsWrapper),$tabsItems:this.findElement(t.tabsItems),$toggleVideosDisplayButton:this.findElement(t.toggleVideosDisplayButton),$videos:this.findElement(t.videos),$innerTabs:this.findElement(t.innerTabs),$imageOverlay:this.findElement(t.imageOverlay)}}initEditorListeners(){super.initEditorListeners(),this.editorListeners.push({event:"elementorPlaylistWidget:fetchVideoData",to:elementor.channels.editor,callback:e=>{this.getCurrentPlayerSelected().setVideoProviderData().then((()=>{e.currentItem=this.getCurrentItemSelected(),elementor.channels.editor.trigger("elementorPlaylistWidget:setVideoData",e)}))}})}bindEvents(){super.bindEvents(),this.elements.$imageOverlay.on({click:e=>{e.currentTarget.remove(),this.getCurrentPlayerSelected().play()}}),this.elements.$innerTabs.on({click:e=>{(0,d.handleInnerTabs)(e,this)}}),this.elements.$tabsItems.on({scroll:e=>{(0,o.handleVideosPanelScroll)(this.elements,e)}}),this.elements.$toggleVideosDisplayButton.on({click:e=>{jQuery(e.target).toggleClass("rotate-up"),jQuery(e.target).toggleClass("rotate-down"),this.elements.$tabsWrapper.slideToggle("slow")}})}onInit(){super.onInit(...arguments),this.playlistId=this.getID(),this.storageKey="watched_videos_"+this.getID();const e=elementorFrontend.storage.get(this.storageKey);this.watchedVideosArray=e?JSON.parse(e):[],this.watchedIndication=this.getElementSettings("show_watched_indication"),(0,o.handleVideosPanelScroll)(this.elements),this.isAutoplayOnLoad="yes"===this.getElementSettings("autoplay_on_load"),this.isAutoplayNextUp="yes"===this.getElementSettings("autoplay_next"),this.isFirstVideoActivated=!0,this.createPlaylistItems(),this.isCollapsible=this.getElementSettings("inner_tab_is_content_collapsible"),this.innerTabsHeightLimit=this.getElementSettings("inner_tab_collapsible_height"),this.currentPlayingPlaylistItemIndex=1,this.activateInitialVideo(),this.activateInnerTabInEditMode()}onEditSettingsChange(e){"panel"===e&&(this.preventTabActivation=!0),"activeItemIndex"===e&&(this.preventTabActivation?this.preventTabActivation=!1:this.activateDefaultTab())}activateInitialVideo(){this.isPageOnLoad=!0;const e=!!this.getElementSettings("lazy_load"),t=(0,h.handleURLParams)(this.playlistId,this.playlistItemsArray);let i=!1;t?(this.currentPlaylistItemIndex=t,this.currentPlayingPlaylistItemIndex=t,i=!0):(this.currentPlaylistItemIndex=1,this.currentPlayingPlaylistItemIndex=1),this.isAutoplayOnLoad&&!i&&(0,h.setVideoParams)(this.playlistId,this.playlistItemsArray,this.currentPlaylistItemIndex),i&&this.$element[0]?.scrollIntoView({behavior:"smooth"}),this.handleFirstVideoActivation(e)}handleFirstVideoActivation(e){if(!e)return void this.activateDefaultTab(this.currentPlaylistItemIndex);const t=document.querySelector(".elementor-element-"+this.playlistId+" .e-tabs-main-area"),i=elementorModules.utils.Scroll.scrollObserver({callback:e=>{e.isInViewport&&(this.activateDefaultTab(this.currentPlaylistItemIndex),i.unobserve(t))}});i.observe(t)}getCurrentItemSelected(){return this.playlistItemsArray[this.currentPlaylistItemIndex-1]}getCurrentPlayerSelected(){return this.getCurrentItemSelected().playerInstance}getCurrentPlayerPlaying(){return this.playlistItemsArray[this.currentPlayingPlaylistItemIndex-1].playerInstance}isVideoShouldBePlayed(){if(this.currentPlayingPlaylistItemIndex!==this.currentPlaylistItemIndex)this.getCurrentPlayerPlaying()&&this.getCurrentPlayerPlaying().pause(),this.currentPlayingPlaylistItemIndex=this.currentPlaylistItemIndex;else if(this.getCurrentPlayerPlaying().isVideoPlaying)return this.getCurrentPlayerPlaying().pause(),!1;return!0}activateInnerTabInEditMode(){if(this.isEdit&&this.getEditSettings("innerActiveIndex")){const e=this.getEditSettings("innerActiveIndex");jQuery(this.elements.$innerTabs.eq(this.currentPlaylistItemIndex-1).find(".e-inner-tab-title a"))[e].click()}}handleVideo(e){if(e.playerInstance)this.isVideoShouldBePlayed()&&(1===this.currentPlaylistItemIndex&&this.elements.$imageOverlay&&this.elements.$imageOverlay.remove(),this.playVideoAfterCreation(e));else{const t={youtube:n.default,vimeo:r.default,hosted:l.default};e.playerInstance=new t[e.videoType](e,this.currentPlaylistItemIndex),e.playerInstance.create().then((()=>{this.isVideoShouldBePlayed()&&this.playVideoOnCreation(e),e.playerInstance.handleFullScreenChange((e=>{(0,c.default)(this,e?"videoFullScreen":"videoExitFullScreen","click")})),e.playerInstance.handlePlayed((()=>{const t=this.getCurrentItemSelected();let i="click";t.isAutoplayOnLoad?(i="onLoad",e.isAutoplayOnLoad=!1):t.isAutoPlayNextUp&&(i="nextVideo"),(0,c.default)(this,t.playerInstance.isVideoPausedLocal?"videoResume":"videoStart",i)})),e.playerInstance.handleEnded((()=>{(0,c.default)(this,"videoEnded","click"),this.watchedIndication&&this.elements.$tabTitles.filter(".e-active").addClass("watched-video");const e=this.getCurrentItemSelected().dataItemId;if(!this.watchedVideosArray.includes(e)&&this.watchedIndication&&(this.watchedVideosArray.push(this.getCurrentItemSelected().dataItemId),elementorFrontend.storage.set(this.storageKey,JSON.stringify(this.watchedVideosArray))),this.isAutoplayNextUp&&this.playlistItemsArray.length>=++this.currentPlaylistItemIndex){for(;"section"===this.getCurrentItemSelected().videoType;)if(this.currentPlaylistItemIndex++,this.playlistItemsArray.length<this.currentPlaylistItemIndex)return void(this.currentPlaylistItemIndex=this.playlistItemsArray.length);this.changeActiveTab(this.currentPlaylistItemIndex,!0)}})),e.playerInstance.handlePaused((e=>{(0,c.default)(this,"videoPaused","click",e)}))}))}}playVideoAfterCreation(e){e.playerInstance.play()}playVideoOnCreation(e){this.isAutoplayOnLoad?(e.isAutoplayOnLoad=!0,e.playerInstance.mute(),e.playerInstance.play(),this.isAutoplayOnLoad=!1):this.isFirstVideoActivated||(e.isAutoPlayNextUp=!0,e.playerInstance.play()),this.isFirstVideoActivated=!1}createPlaylistItems(){this.playlistItemsArray=[],this.elements.$videos.each(((e,t)=>{const i={},s=jQuery(t);i.videoUrl=s.attr("data-video-url"),i.videoType=s.attr("data-video-type"),i.videoTitle=s.attr("data-video-title"),i.videoDuration=s.attr("data-video-duration"),i.tabContent=t,i.dataTab=e+1,i.dataItemId=this.getElementSettings().tabs[e]._id,this.playlistItemsArray.push(i)})),this.watchedVideosArray.length>0&&this.watchedIndication&&this.watchedVideosArray.forEach((e=>{const t=this.playlistItemsArray.find((t=>t.dataItemId===e));this.elements.$tabTitles.filter('[data-tab="'+t.dataTab+'"]').addClass("watched-video")}))}changeActiveTab(e,t){if(super.changeActiveTab(e),this.playlistItemsArray[e-1]&&"section"!==this.playlistItemsArray[e-1].videoType&&(this.currentPlaylistItemIndex=parseInt(e),t&&(this.currentPlayingPlaylistItemIndex=this.currentPlaylistItemIndex),this.handleVideo(this.getCurrentItemSelected(),t),this.isPageOnLoad||(0,h.setVideoParams)(this.playlistId,this.playlistItemsArray,this.currentPlaylistItemIndex),this.isPageOnLoad=!1,jQuery(this.elements.$innerTabs.eq(e-1)).find(".e-inner-tab-content").length>0)){const e=this.elements.$innerTabs.filter(".e-active").find(".e-inner-tab-content");(0,d.handleInnerTabsButtonsDisplay)(e.toArray(),this.isCollapsible,this.innerTabsHeightLimit)}}}t.default=VideoPlaylistHandler},9642:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.handleInnerTabs=function handleInnerTabs(e,t){const i=e.target,s=i.tagName;if(i.classList.contains("e-inner-tab-title-text")){e.preventDefault();toggleInnerTabs(e,jQuery(i).parent(".e-inner-tab-title"),t)}if(i.classList.contains("e-tab-mobile-title")){toggleInnerTabs(e,jQuery(i),t)}"button"===s.toLowerCase()&&onTabContentButtonsClick(e,t)},t.handleInnerTabsButtonsDisplay=handleInnerTabsButtonsDisplay,t.onTabContentButtonsClick=onTabContentButtonsClick;var a=s(i(5120));function toggleInnerTabs(e,t,i){const s=e.currentTarget,n=s.querySelectorAll(".e-inner-tab-title");if(t.hasClass("e-inner-tab-active")||n.length<2)return;const r=s.querySelectorAll(".e-inner-tab-content");n.forEach((e=>{e.classList.toggle("e-inner-tab-active")})),r.forEach((e=>{e.toggleAttribute("hidden"),e.classList.toggle("e-inner-tab-active")})),handleInnerTabsButtonsDisplay(Array.from(r),i.isCollapsible,i.innerTabsHeightLimit),(0,a.default)(i,"tabOpened","click")}function handleInnerTabsButtonsDisplay(e,t,i){if(!t)return;const s=e.filter((e=>e.classList.contains("e-inner-tab-active"))),a=s[0].querySelector(".e-inner-tab-text > div").offsetHeight,n=parseInt(i.size);n&&a>n&&s[0].classList.add("show-inner-tab-buttons")}function onTabContentButtonsClick(e,t){const i=jQuery(e.currentTarget).find(".e-inner-tab-content").filter(".e-inner-tab-active");i.find("button").toggleClass("show-button"),i.toggleClass("show-full-height");const s=i.hasClass("show-full-height")?"tabExpanded":"tabCollapsed";(0,a.default)(t,s,"click")}},3552:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class PlayerBase{constructor(e,t){this.playlistItem=e,this.positionInVideoList=t}formatDuration(e){const t=new Date(1e3*e),i=t.getUTCHours(),s=t.getUTCMinutes(),a=t.getSeconds();return 0!==i?`${i.toString()}:${s.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`:`${s.toString()}:${a.toString().padStart(2,"0")}`}}},3650:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=s(i(3552));class playerHosted extends a.default{constructor(e,t){super(e,t),this.playerObject=null,this.watchCount=0,this.isVideoPlaying=!1,this.isVideoPausedLocal=!1,this.isVideoSeeking=!1,this.isVideoEnded=!1,this.isReady=!1}create(){return new Promise((e=>{const t=document.createElement("video");t.setAttribute("controls","");const i=document.createTextNode("Sorry, your browser doesn't support embedded videos."),s=document.createElement("source");s.setAttribute("src",this.playlistItem.videoUrl),s.setAttribute("type","video/"+this.playlistItem.videoUrl.split(".").pop()),t.appendChild(s),t.appendChild(i),this.playerObject=t,this.playlistItem.tabContent.querySelector("div").replaceWith(this.playerObject),this.playerObject.addEventListener("canplay",(()=>{this.isReady=!0,e()})),this.playerObject.addEventListener("seeked",(()=>{this.isVideoSeeking=!1})),this.playerObject.addEventListener("seeking",(()=>{clearTimeout(this.seekTimeOut),this.isVideoSeeking=!0}))}))}handleEnded(e){this.playerObject.addEventListener("ended",(()=>{this.watchCount++,this.isVideoEnded=!0,this.isVideoPlaying=!1,e(this.playlistItem)}))}handlePaused(e){this.playerObject.addEventListener("pause",(()=>{this.seekTimeOut=setTimeout((()=>{this.isVideoSeeking||this.isVideoEnded?this.isVideoEnded=!1:(e(this.positionInVideoList),this.isVideoPausedLocal=!0)}),30)}))}handlePlayed(e){this.playerObject.addEventListener("play",(()=>{this.isVideoSeeking||e(this.playlistItem)}))}handleFullScreenChange(e){jQuery(this.playerObject).on("webkitfullscreenchange mozfullscreenchange fullscreenchange",(()=>{e(document.fullscreenElement)}))}getCurrentTime(){return this.playerObject.currentTime}play(){this.isReady&&(this.isVideoPlaying=!0,this.playerObject.play())}pause(){this.isReady&&(this.isVideoPlaying=!1,this.playerObject.pause())}mute(){this.playerObject.muted=!0}}t.default=playerHosted},9369:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=s(i(3552));class playerVimeo extends a.default{constructor(e,t){super(e,t),this.apiProvider=elementorFrontend.utils.vimeo,this.playerObject=null,this.watchCount=0,this.isVideoInFullScreenChange=!1,this.isReady=!1}create(){return this.currentVideoID=this.apiProvider.getVideoIDFromURL(this.playlistItem.videoUrl),new Promise((e=>{this.apiProvider.onApiReady((t=>{const i={id:this.currentVideoID,autoplay:!1};this.playerObject=new t.Player(this.playlistItem.tabContent.querySelector("div"),i),this.playerObject.ready().then((()=>{this.isReady=!0,e()}))}))}))}handleEnded(e){this.playerObject.on("ended",(()=>{this.watchCount++,e(this.playlistItem)}))}handlePaused(e){this.playerObject.on("pause",(t=>{0===t.percent||t.percent>=1||this.isVideoInFullScreenChange||e(this.positionInVideoList)}))}handlePlayed(e){this.playerObject.on("play",(()=>{this.isVideoInFullScreenChange?this.isVideoInFullScreenChange=!1:e(this.playlistItem)}))}handleFullScreenChange(e){this.playerObject.element.addEventListener("fullscreenchange",(()=>{e(document.fullscreenElement),this.isVideoInFullScreenChange=!0}))}getCurrentTime(){return this.playerObject.getCurrentTime().then((e=>e))}play(){this.isReady&&this.playerObject.play()}pause(){this.isReady&&this.playerObject.pause()}mute(){this.playerObject.setMuted(!0)}async setVideoProviderData(){if(!this.currentVideoID&&9===!this.currentVideoID.length)return;const e=await this.playerObject.getVideoId(),t=await fetch("https://vimeo.com/api/v2/video/"+e+".json"),i=await t.json();return this.playlistItem.duration=this.formatDuration(i[0].duration),this.playlistItem.video_title=i[0].title,this.playlistItem.thumbnail={url:i[0].thumbnail_medium},this.playlistItem}}t.default=playerVimeo},8200:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=s(i(3552));class playerYoutube extends a.default{constructor(e,t){super(e,t),this.apiProvider=elementorFrontend.utils.youtube,this.playerObject=null,this.watchCount=0,this.isVideoPlaying=!1,this.isVideoPausedLocal=!1,this.isVideoEnded=!1,this.seekSequenceArray=[],this.pauseCurrentTime=null,this.isReady=!1}create(){this.currentVideoID=this.apiProvider.getVideoIDFromURL(this.playlistItem.videoUrl);return new Promise((e=>{this.apiProvider.onApiReady((t=>{const i={width:"773",videoId:this.currentVideoID,playerVars:{rel:0,showinfo:0,ecver:2},events:{onReady:()=>{this.isReady=!0,e()}}};this.playerObject=new t.Player(this.playlistItem.tabContent.querySelector("div"),i),this.playerObject.addEventListener("onStateChange",(e=>{3===e.data&&(2===this.seekSequenceArray[this.seekSequenceArray.length-1]?this.seekSequenceArray.push(3):(this.seekSequenceArray=[],clearTimeout(this.seekTimeOut)))}))}))}))}handleEnded(e){this.playerObject.addEventListener("onStateChange",(t=>{0===t.data&&(this.watchCount++,this.isVideoEnded=!0,t.target.seekTo(0),t.target.stopVideo(),this.isVideoPlaying=!1,e())}))}handlePaused(e){this.playerObject.addEventListener("onStateChange",(t=>{2===t.data&&(this.seekSequenceArray=[],this.seekSequenceArray.push(2),this.pauseCurrentTime=this.playerObject.playerInfo.currentTime,this.seekTimeOut=setTimeout((()=>{2===this.seekSequenceArray.length&&2===this.seekSequenceArray[0]&&3===this.seekSequenceArray[1]?(this.seekSequenceArray=[],clearTimeout(this.seekTimeOut)):(e(this.positionInVideoList),this.isVideoPausedLocal=!0)}),1e3))}))}handlePlayed(e){this.playerObject.addEventListener("onStateChange",(t=>{1!==t.data||this.isVideoEnded?this.isVideoEnded=!1:2===this.seekSequenceArray.length&&2===this.seekSequenceArray[0]&&3===this.seekSequenceArray[1]||e()}))}handleError(e){this.playerObject.addEventListener("onError",(()=>{e()}))}handleFullScreenChange(e){this.playerObject.h.addEventListener("fullscreenchange",(()=>{e(document.fullscreenElement)}))}getCurrentTime(){const e=this.pauseCurrentTime?this.pauseCurrentTime:this.playerObject.playerInfo.currentTime;return this.pauseCurrentTime=null,e}play(){this.isReady&&(this.isVideoPlaying=!0,this.playerObject.playVideo())}pause(){this.isReady&&(this.isVideoPlaying=!1,this.playerObject.pauseVideo())}mute(){this.playerObject.mute()}async setVideoProviderData(){this.isReady&&(this.currentVideoID&&11===this.currentVideoID.length?(this.playlistItem.thumbnail={url:"https://img.youtube.com/vi/"+this.playerObject.getVideoData().video_id+"/maxresdefault.jpg"},this.playlistItem.video_title=this.playerObject.getVideoData().title,this.playlistItem.duration=this.formatDuration(this.playerObject.getDuration())):(this.playlistItem.thumbnail={url:""},this.playlistItem.video_title="",this.playlistItem.duration=""))}}t.default=playerYoutube},2374:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class PlaylistEvent{constructor(e){let{event:t,tab:i,playlist:s,video:a}=e;this.event={type:t.type||"",time:t.time||0,element:t.element,trigger:t.trigger||"",watchCount:t.watchCount||0},this.tab={name:i.name,index:i.index},this.playlist={name:s.name,currentItem:s.currentItem,amount:s.amount},this.video={provider:a.provider,url:a.url,title:a.title,duration:a.duration}}}},7712:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.handleVideosPanelScroll=function handleVideosPanelScroll(e,t){if(!t)return void(e.$tabsItems[0].offsetHeight<e.$tabsItems[0].scrollHeight&&e.$tabsWrapper.addClass("bottom-shadow"));t.target.scrollTop>0?e.$tabsWrapper.addClass("top-shadow"):e.$tabsWrapper.removeClass("top-shadow");t.target.offsetHeight+t.target.scrollTop>=t.target.scrollHeight?e.$tabsWrapper.removeClass("bottom-shadow"):e.$tabsWrapper.addClass("bottom-shadow")}},7571:(e,t)=>{function setVideoParams(e,t,i){const s=new URLSearchParams(location.search);s.set("playlist",e),s.set("video",t[i-1].dataItemId),history.replaceState({},"",location.pathname+"?"+s)}Object.defineProperty(t,"__esModule",{value:!0}),t.handleURLParams=function handleURLParams(e,t){const i=new URLSearchParams(location.search),s=i.get("playlist");if(!s)return!1;if(s===e){const s=i.get("video"),a=t.find((e=>s===e.dataItemId)),n=a?a.dataTab:1;return n||setVideoParams(e,t,1),n||!1}},t.setVideoParams=setVideoParams}}]);