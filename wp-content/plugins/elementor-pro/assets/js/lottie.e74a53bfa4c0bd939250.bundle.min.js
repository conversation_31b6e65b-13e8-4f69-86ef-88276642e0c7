/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[970],{5200:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;class lottieHandler extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{container:".e-lottie__container",containerLink:".e-lottie__container__link",animation:".e-lottie__animation",caption:".e-lottie__caption"},classes:{caption:"e-lottie__caption"}}}getDefaultElements(){const{selectors:t}=this.getSettings();return{$widgetWrapper:this.$element,$container:this.$element.find(t.container),$containerLink:this.$element.find(t.containerLink),$animation:this.$element.find(t.animation),$caption:this.$element.find(t.caption),$sectionParent:this.$element.closest(".elementor-section"),$columnParent:this.$element.closest(".elementor-column"),$containerParent:this.$element.closest(".e-con")}}onInit(){super.onInit(...arguments),this.lottie=null,this.state={isAnimationScrollUpdateNeededOnFirstLoad:!0,isNewLoopCycle:!1,isInViewport:!1,loop:!1,animationDirection:"forward",currentAnimationTrigger:"",effectsRelativeTo:"",hoverOutMode:"",hoverArea:"",caption:"",playAnimationCount:0,animationSpeed:0,linkTimeout:0,viewportOffset:{start:0,end:100}},this.intersectionObservers={animation:{observer:null,element:null},lazyload:{observer:null,element:null}},this.animationFrameRequest={timer:null,lastScrollY:0},this.listeners={collection:[],elements:{$widgetArea:{triggerAnimationHoverIn:null,triggerAnimationHoverOut:null},$container:{triggerAnimationClick:null}}},this.initLottie()}initLottie(){this.getLottieSettings().lazyload?this.lazyloadLottie():this.generateLottie()}lazyloadLottie(){this.intersectionObservers.lazyload.observer=elementorModules.utils.Scroll.scrollObserver({offset:"0px 0px 200px",callback:t=>{t.isInViewport&&(this.generateLottie(),this.intersectionObservers.lazyload.observer.unobserve(this.intersectionObservers.lazyload.element))}}),this.intersectionObservers.lazyload.element=this.elements.$container[0],this.intersectionObservers.lazyload.observer.observe(this.intersectionObservers.lazyload.element)}generateLottie(){this.createLottieInstance(),this.setLottieEvents()}createLottieInstance(){const t=this.getLottieSettings();this.lottie=bodymovin.loadAnimation({container:this.elements.$animation[0],path:this.getAnimationPath(),renderer:t.renderer,autoplay:!1,name:"lottie-widget"}),this.elements.$animation.data("lottie",this.lottie)}getAnimationPath(){const t=this.getLottieSettings();return t.source_json?.url&&"json"===t.source_json.url.toLowerCase().substr(-4)?t.source_json.url:t.source_external_url?.url?t.source_external_url.url:elementorProFrontend.config.lottie.defaultAnimationUrl}setCaption(){const t=this.getLottieSettings();if("external_url"===t.source||"media_file"===t.source&&"custom"===t.caption_source){this.getCaptionElement().text(t.caption)}}getCaptionElement(){if(!this.elements.$caption.length){const{classes:t}=this.getSettings();return this.elements.$caption=jQuery("<p>",{class:t.caption}),this.elements.$container.append(this.elements.$caption),this.elements.$caption}return this.elements.$caption}setLottieEvents(){this.lottie.addEventListener("DOMLoaded",(()=>this.onLottieDomLoaded())),this.lottie.addEventListener("complete",(()=>this.onComplete()))}saveInitialValues(){const t=this.getLottieSettings();this.lottie.__initialTotalFrames=this.lottie.totalFrames,this.lottie.__initialFirstFrame=this.lottie.firstFrame,this.state.currentAnimationTrigger=t.trigger,this.state.effectsRelativeTo=t.effects_relative_to,this.state.viewportOffset.start=t.viewport?t.viewport.sizes.start:0,this.state.viewportOffset.end=t.viewport?t.viewport.sizes.end:100,this.state.animationSpeed=t.play_speed?.size,this.state.linkTimeout=t.link_timeout,this.state.caption=t.caption,this.state.loop=t.loop}setAnimationFirstFrame(){const t=this.getAnimationFrames();t.first=t.first-this.lottie.__initialFirstFrame,this.lottie.goToAndStop(t.first,!0)}initAnimationTrigger(){switch(this.getLottieSettings().trigger){case"none":this.playLottie();break;case"arriving_to_viewport":this.playAnimationWhenArrivingToViewport();break;case"bind_to_scroll":this.playAnimationWhenBindToScroll();break;case"on_click":this.bindAnimationClickEvents();break;case"on_hover":this.bindAnimationHoverEvents()}}playAnimationWhenArrivingToViewport(){const t=this.getOffset();this.intersectionObservers.animation.observer=elementorModules.utils.Scroll.scrollObserver({offset:`${t.end}% 0% ${t.start}%`,callback:t=>{t.isInViewport?(this.state.isInViewport=!0,this.playLottie()):(this.state.isInViewport=!1,this.lottie.pause())}}),this.intersectionObservers.animation.element=this.elements.$widgetWrapper[0],this.intersectionObservers.animation.observer.observe(this.intersectionObservers.animation.element)}getOffset(){const t=this.getLottieSettings();return{start:-t.viewport.sizes.start||0,end:-(100-t.viewport.sizes.end)||0}}playAnimationWhenBindToScroll(){const t=this.getLottieSettings(),e=this.getOffset();this.intersectionObservers.animation.observer=elementorModules.utils.Scroll.scrollObserver({offset:`${e.end}% 0% ${e.start}%`,callback:t=>this.onLottieIntersection(t)}),this.intersectionObservers.animation.element="viewport"===t.effects_relative_to?this.elements.$widgetWrapper[0]:document.documentElement,this.intersectionObservers.animation.observer.observe(this.intersectionObservers.animation.element)}updateAnimationByScrollPosition(){let t;t="page"===this.getLottieSettings().effects_relative_to?this.getLottiePagePercentage():"fixed"===this.getCurrentDeviceSetting("_position")?this.getLottieViewportHeightPercentage():this.getLottieViewportPercentage();let e=this.getFrameNumberByPercent(t);e-=this.lottie.__initialFirstFrame,this.lottie.goToAndStop(e,!0)}getLottieViewportPercentage(){return elementorModules.utils.Scroll.getElementViewportPercentage(this.elements.$widgetWrapper,this.getOffset())}getLottiePagePercentage(){return elementorModules.utils.Scroll.getPageScrollPercentage(this.getOffset())}getLottieViewportHeightPercentage(){return elementorModules.utils.Scroll.getPageScrollPercentage(this.getOffset(),window.innerHeight)}getFrameNumberByPercent(t){const e=this.getAnimationFrames();return t=Math.min(100,Math.max(0,t)),e.first+(e.last-e.first)*t/100}getAnimationFrames(){const t=this.getLottieSettings(),e=this.getAnimationCurrentFrame(),i=this.getAnimationRange().start,s=this.getAnimationRange().end;let n=this.lottie.__initialFirstFrame,o=0===this.lottie.__initialFirstFrame?this.lottie.__initialTotalFrames:this.lottie.__initialFirstFrame+this.lottie.__initialTotalFrames;return i&&i>n&&(n=i),s&&s<o&&(o=s),this.state.isNewLoopCycle||"bind_to_scroll"===t.trigger||(n=i&&i>e?i:e),"backward"===this.state.animationDirection&&this.isReverseMode()&&(n=e,o=i&&i>this.lottie.__initialFirstFrame?i:this.lottie.__initialFirstFrame),{first:n,last:o,current:e,total:this.lottie.__initialTotalFrames}}getAnimationRange(){const t=this.getLottieSettings();return{start:this.getInitialFrameNumberByPercent(t.start_point.size),end:this.getInitialFrameNumberByPercent(t.end_point.size)}}getInitialFrameNumberByPercent(t){return t=Math.min(100,Math.max(0,t)),this.lottie.__initialFirstFrame+(this.lottie.__initialTotalFrames-this.lottie.__initialFirstFrame)*t/100}getAnimationCurrentFrame(){return 0===this.lottie.firstFrame?this.lottie.currentFrame:this.lottie.firstFrame+this.lottie.currentFrame}setLinkTimeout(){const t=this.getLottieSettings();"on_click"===t.trigger&&t.custom_link?.url&&t.link_timeout&&this.elements.$containerLink.on("click",(e=>{e.preventDefault(),this.isEdit||setTimeout((()=>{const e="on"===t.custom_link.is_external?"_blank":"_self";window.open(t.custom_link.url,e)}),t.link_timeout)}))}bindAnimationClickEvents(){this.listeners.elements.$container.triggerAnimationClick=()=>{this.playLottie()},this.addSessionEventListener(this.elements.$container,"click",this.listeners.elements.$container.triggerAnimationClick)}getLottieSettings(){const t=this.getElementSettings();return{...t,lazyload:"yes"===t.lazyload,loop:"yes"===t.loop}}playLottie(){const t=this.getAnimationFrames();this.lottie.stop(),this.lottie.playSegments([t.first,t.last],!0),this.state.isNewLoopCycle=!1}bindAnimationHoverEvents(){this.createAnimationHoverInEvents(),this.createAnimationHoverOutEvents()}createAnimationHoverInEvents(){const t=this.getLottieSettings(),e=this.getHoverAreaElement();this.state.hoverArea=t.hover_area,this.listeners.elements.$widgetArea.triggerAnimationHoverIn=()=>{this.state.animationDirection="forward",this.playLottie()},this.addSessionEventListener(e,"mouseenter",this.listeners.elements.$widgetArea.triggerAnimationHoverIn)}addSessionEventListener(t,e,i){t.on(e,i),this.listeners.collection.push({$el:t,event:e,callback:i})}createAnimationHoverOutEvents(){const t=this.getLottieSettings(),e=this.getHoverAreaElement();"pause"!==t.on_hover_out&&"reverse"!==t.on_hover_out||(this.state.hoverOutMode=t.on_hover_out,this.listeners.elements.$widgetArea.triggerAnimationHoverOut=()=>{"pause"===t.on_hover_out?this.lottie.pause():(this.state.animationDirection="backward",this.playLottie())},this.addSessionEventListener(e,"mouseleave",this.listeners.elements.$widgetArea.triggerAnimationHoverOut))}getHoverAreaElement(){switch(this.getLottieSettings().hover_area){case"section":return this.elements.$sectionParent;case"column":return this.elements.$columnParent;case"container":return this.elements.$containerParent}return this.elements.$container}setLoopOnAnimationComplete(){const t=this.getLottieSettings();this.state.isNewLoopCycle=!0,t.loop&&!this.isReverseMode()?this.setLoopWhenNotReverse():t.loop&&this.isReverseMode()?this.setReverseAnimationOnLoop():!t.loop&&this.isReverseMode()&&this.setReverseAnimationOnSingleTrigger()}isReverseMode(){const t=this.getLottieSettings();return"yes"===t.reverse_animation||"reverse"===t.on_hover_out&&"backward"===this.state.animationDirection}setLoopWhenNotReverse(){const t=this.getLottieSettings();t.number_of_times>0?(this.state.playAnimationCount++,this.state.playAnimationCount<t.number_of_times?this.playLottie():this.state.playAnimationCount=0):this.playLottie()}setReverseAnimationOnLoop(){const t=this.getLottieSettings();!t.number_of_times||this.state.playAnimationCount<t.number_of_times?(this.state.animationDirection="forward"===this.state.animationDirection?"backward":"forward",this.playLottie(),"backward"===this.state.animationDirection&&this.state.playAnimationCount++):(this.state.playAnimationCount=0,this.state.animationDirection="forward")}setReverseAnimationOnSingleTrigger(){this.state.playAnimationCount<1?(this.state.playAnimationCount++,this.state.animationDirection="backward",this.playLottie()):this.state.playAnimationCount>=1&&"forward"===this.state.animationDirection?(this.state.animationDirection="backward",this.playLottie()):(this.state.playAnimationCount=0,this.state.animationDirection="forward")}setAnimationSpeed(){const t=this.getLottieSettings();t.play_speed&&this.lottie.setSpeed(t.play_speed.size)}onElementChange(){this.updateLottieValues(),this.resetAnimationTrigger()}updateLottieValues(){const t=this.getLottieSettings();[{sourceVal:t.play_speed?.size,stateProp:"animationSpeed",callback:()=>this.setAnimationSpeed()},{sourceVal:t.link_timeout,stateProp:"linkTimeout",callback:()=>this.setLinkTimeout()},{sourceVal:t.caption,stateProp:"caption",callback:()=>this.setCaption()},{sourceVal:t.effects_relative_to,stateProp:"effectsRelativeTo",callback:()=>this.updateAnimationByScrollPosition()},{sourceVal:t.loop,stateProp:"loop",callback:()=>this.onLoopStateChange()}].forEach((t=>{void 0!==t.sourceVal&&t.sourceVal!==this.state[t.stateProp]&&(this.state[t.stateProp]=t.sourceVal,t.callback())}))}onLoopStateChange(){const t="arriving_to_viewport"===this.state.currentAnimationTrigger&&this.state.isInViewport;this.state.loop&&(t||"none"===this.state.currentAnimationTrigger)&&this.playLottie()}resetAnimationTrigger(){const t=this.getLottieSettings(),e=t.trigger!==this.state.currentAnimationTrigger,i=!!t.viewport&&this.isViewportOffsetChange(),s=!!t.on_hover_out&&this.isHoverOutModeChange(),n=!!t.hover_area&&this.isHoverAreaChange();(e||i||s||n)&&(this.removeAnimationFrameRequests(),this.removeObservers(),this.removeEventListeners(),this.initAnimationTrigger())}isViewportOffsetChange(){const t=this.getLottieSettings(),e=t.viewport.sizes.start!==this.state.viewportOffset.start,i=t.viewport.sizes.end!==this.state.viewportOffset.end;return e||i}isHoverOutModeChange(){return this.getLottieSettings().on_hover_out!==this.state.hoverOutMode}isHoverAreaChange(){return this.getLottieSettings().hover_area!==this.state.hoverArea}removeEventListeners(){this.listeners.collection.forEach((t=>{t.$el.off(t.event,null,t.callback)}))}removeObservers(){for(const t in this.intersectionObservers)this.intersectionObservers[t].observer&&this.intersectionObservers[t].element&&this.intersectionObservers[t].observer.unobserve(this.intersectionObservers[t].element)}removeAnimationFrameRequests(){cancelAnimationFrame(this.animationFrameRequest.timer)}onDestroy(){super.onDestroy(),this.destroyLottie()}destroyLottie(){this.removeAnimationFrameRequests(),this.removeObservers(),this.removeEventListeners(),this.elements.$animation.removeData("lottie"),this.lottie&&this.lottie.destroy()}onLottieDomLoaded(){this.saveInitialValues(),this.setAnimationSpeed(),this.setLinkTimeout(),this.setCaption(),this.setAnimationFirstFrame(),this.initAnimationTrigger()}onComplete(){this.setLoopOnAnimationComplete()}onLottieIntersection(t){if(t.isInViewport)this.state.isAnimationScrollUpdateNeededOnFirstLoad&&(this.state.isAnimationScrollUpdateNeededOnFirstLoad=!1,this.updateAnimationByScrollPosition()),this.animationFrameRequest.timer=requestAnimationFrame((()=>this.onAnimationFrameRequest()));else{const e=this.getAnimationFrames(),i="up"===t.intersectionScrollDirection?e.first:e.last;this.state.isAnimationScrollUpdateNeededOnFirstLoad=!1,cancelAnimationFrame(this.animationFrameRequest.timer),this.lottie.goToAndStop(i,!0)}}onAnimationFrameRequest(){window.scrollY!==this.animationFrameRequest.lastScrollY&&(this.updateAnimationByScrollPosition(),this.animationFrameRequest.lastScrollY=window.scrollY),this.animationFrameRequest.timer=requestAnimationFrame((()=>this.onAnimationFrameRequest()))}}e.default=lottieHandler}}]);