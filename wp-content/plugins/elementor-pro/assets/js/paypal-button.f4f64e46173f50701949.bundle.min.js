/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[375],{466:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class PayPalHandler extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{button:".elementor-button.elementor-paypal-legacy",errors:".elementor-message-danger"}}}getDefaultElements(){const e=this.getSettings();return{wrapper:this.$element[0],button:this.$element[0].querySelector(e.selectors.button),errors:this.$element[0].querySelectorAll(e.selectors.errors)}}handleClick(e){0<this.elements.errors.length&&(e.preventDefault(),this.elements.errors.forEach((e=>{e.classList.remove("elementor-hidden")})))}bindEvents(){this.elements.button.addEventListener("click",this.handleClick.bind(this))}}t.default=PayPalHandler}}]);