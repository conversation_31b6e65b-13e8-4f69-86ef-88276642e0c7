/*! elementor-pro - v3.27.0 - 06-02-2025 */
(()=>{"use strict";var e={2098:(e,t,r)=>{var n=r(2470).__;Object.defineProperty(t,"__esModule",{value:!0}),t.SAVE_CONTEXT=t.EDIT_CONTEXT=void 0,t.createElement=createElement,t.default=function addDocumentHandle(e){let{element:t,id:r,title:u=n("Template","elementor-pro")}=e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,f=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(a===s){if(!r||!t)throw Error("`id` and `element` are required.");if(function isCurrentlyEditing(e){return e.classList.contains(i)}(t)||function hasHandle(e){return!!e.querySelector(`:scope > .${o}`)}(t))return}const p=function createHandleElement(e,t){let{title:r,onClick:i}=e,u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const s=["header","footer"].includes(u?.dataset.elementorType)?"%s":n("Edit %s","elementor-pro"),l=createElement({tag:"div",classNames:[`${o}__inner`],children:[createElement({tag:"i",classNames:[getHandleIcon(t)]}),createElement({tag:"div",classNames:[`${a===t?o:c}__title`],children:[document.createTextNode(a===t?s.replace("%s",r):n("Save %s","elementor-pro").replace("%s",r))]})]}),f=[o];a!==t&&f.push(c);const p=createElement({tag:"div",classNames:f,children:[l]});return p.addEventListener("click",i),p}({title:u,onClick:()=>async function onDocumentClick(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a===t?(window.top.$e.internal("panel/state-loading"),await window.top.$e.run("editor/documents/switch",{id:parseInt(e),onClose:r,selector:n}),window.top.$e.internal("panel/state-ready")):(elementorCommon.api.internal("panel/state-loading"),elementorCommon.api.run("editor/documents/switch",{id:elementor.config.initial_document.id,mode:"save",shouldScroll:!1,selector:n}).finally((()=>elementorCommon.api.internal("panel/state-ready"))))}(r,s,l,f)},s,t);t.prepend(p),a===s&&(t.dataset.editableElementorDocument=r)},r(6281),r(5724);const o="elementor-document-handle",i="elementor-edit-mode",a=t.EDIT_CONTEXT="edit",c="elementor-document-save-back-handle",u=t.SAVE_CONTEXT="save";function getHandleIcon(e){let t="eicon-edit";return u===e&&(t=elementorFrontend.config.is_rtl?"eicon-arrow-right":"eicon-arrow-left"),t}function createElement(e){let{tag:t,classNames:r=[],children:n=[]}=e;const o=document.createElement(t);return o.classList.add(...r),n.forEach((e=>o.appendChild(e))),o}},2470:e=>{e.exports=wp.i18n},8120:(e,t,r)=>{var n=r(1483),o=r(8761),i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(o(e)+" is not a function")}},7095:(e,t,r)=>{var n=r(1),o=r(5290),i=r(5835).f,a=n("unscopables"),c=Array.prototype;void 0===c[a]&&i(c,a,{configurable:!0,value:o(null)}),e.exports=function(e){c[a][e]=!0}},2293:(e,t,r)=>{var n=r(1704),o=String,i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(o(e)+" is not an object")}},6651:(e,t,r)=>{var n=r(5599),o=r(3392),i=r(6960),createMethod=function(e){return function(t,r,a){var c=n(t),u=i(c);if(0===u)return!e&&-1;var s,l=o(a,u);if(e&&r!=r){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},9273:(e,t,r)=>{var n=r(382),o=r(4914),i=TypeError,a=Object.getOwnPropertyDescriptor,c=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=c?function(e,t){if(o(e)&&!a(e,"length").writable)throw new i("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},1278:(e,t,r)=>{var n=r(4762),o=n({}.toString),i=n("".slice);e.exports=function(e){return i(o(e),8,-1)}},6726:(e,t,r)=>{var n=r(5755),o=r(9497),i=r(4961),a=r(5835);e.exports=function(e,t,r){for(var c=o(t),u=a.f,s=i.f,l=0;l<c.length;l++){var f=c[l];n(e,f)||r&&n(r,f)||u(e,f,s(t,f))}}},9037:(e,t,r)=>{var n=r(382),o=r(5835),i=r(7738);e.exports=n?function(e,t,r){return o.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},7738:e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7914:(e,t,r)=>{var n=r(1483),o=r(5835),i=r(169),a=r(2095);e.exports=function(e,t,r,c){c||(c={});var u=c.enumerable,s=void 0!==c.name?c.name:t;if(n(r)&&i(r,s,c),c.global)u?e[t]=r:a(t,r);else{try{c.unsafe?e[t]&&(u=!0):delete e[t]}catch(e){}u?e[t]=r:o.f(e,t,{value:r,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return e}},2095:(e,t,r)=>{var n=r(5578),o=Object.defineProperty;e.exports=function(e,t){try{o(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},382:(e,t,r)=>{var n=r(8473);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3145:(e,t,r)=>{var n=r(5578),o=r(1704),i=n.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},1091:e=>{var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},4741:e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},9461:(e,t,r)=>{var n=r(5578).navigator,o=n&&n.userAgent;e.exports=o?String(o):""},6477:(e,t,r)=>{var n,o,i=r(5578),a=r(9461),c=i.process,u=i.Deno,s=c&&c.versions||u&&u.version,l=s&&s.v8;l&&(o=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),e.exports=o},8612:(e,t,r)=>{var n=r(5578),o=r(4961).f,i=r(9037),a=r(7914),c=r(2095),u=r(6726),s=r(8730);e.exports=function(e,t){var r,l,f,p,d,v=e.target,m=e.global,y=e.stat;if(r=m?n:y?n[v]||c(v,{}):n[v]&&n[v].prototype)for(l in t){if(p=t[l],f=e.dontCallGetSet?(d=o(r,l))&&d.value:r[l],!s(m?l:v+(y?".":"#")+l,e.forced)&&void 0!==f){if(typeof p==typeof f)continue;u(p,f)}(e.sham||f&&f.sham)&&i(p,"sham",!0),a(r,l,p,e)}}},8473:e=>{e.exports=function(e){try{return!!e()}catch(e){return!0}}},274:(e,t,r)=>{var n=r(8473);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},1807:(e,t,r)=>{var n=r(274),o=Function.prototype.call;e.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},2048:(e,t,r)=>{var n=r(382),o=r(5755),i=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,c=o(i,"name"),u=c&&"something"===function something(){}.name,s=c&&(!n||n&&a(i,"name").configurable);e.exports={EXISTS:c,PROPER:u,CONFIGURABLE:s}},4762:(e,t,r)=>{var n=r(274),o=Function.prototype,i=o.call,a=n&&o.bind.bind(i,i);e.exports=n?a:function(e){return function(){return i.apply(e,arguments)}}},1409:(e,t,r)=>{var n=r(5578),o=r(1483);e.exports=function(e,t){return arguments.length<2?(r=n[e],o(r)?r:void 0):n[e]&&n[e][t];var r}},2564:(e,t,r)=>{var n=r(8120),o=r(5983);e.exports=function(e,t){var r=e[t];return o(r)?void 0:n(r)}},5578:function(e,t,r){var check=function(e){return e&&e.Math===Math&&e};e.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof r.g&&r.g)||check("object"==typeof this&&this)||function(){return this}()||Function("return this")()},5755:(e,t,r)=>{var n=r(4762),o=r(2347),i=n({}.hasOwnProperty);e.exports=Object.hasOwn||function hasOwn(e,t){return i(o(e),t)}},1507:e=>{e.exports={}},2811:(e,t,r)=>{var n=r(1409);e.exports=n("document","documentElement")},1799:(e,t,r)=>{var n=r(382),o=r(8473),i=r(3145);e.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},2121:(e,t,r)=>{var n=r(4762),o=r(8473),i=r(1278),a=Object,c=n("".split);e.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?c(e,""):a(e)}:a},7268:(e,t,r)=>{var n=r(4762),o=r(1483),i=r(1831),a=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},4483:(e,t,r)=>{var n,o,i,a=r(4644),c=r(5578),u=r(1704),s=r(9037),l=r(5755),f=r(1831),p=r(5409),d=r(1507),v="Object already initialized",m=c.TypeError,y=c.WeakMap;if(a||f.state){var h=f.state||(f.state=new y);h.get=h.get,h.has=h.has,h.set=h.set,n=function(e,t){if(h.has(e))throw new m(v);return t.facade=e,h.set(e,t),t},o=function(e){return h.get(e)||{}},i=function(e){return h.has(e)}}else{var g=p("state");d[g]=!0,n=function(e,t){if(l(e,g))throw new m(v);return t.facade=e,s(e,g,t),t},o=function(e){return l(e,g)?e[g]:{}},i=function(e){return l(e,g)}}e.exports={set:n,get:o,has:i,enforce:function(e){return i(e)?o(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!u(t)||(r=o(t)).type!==e)throw new m("Incompatible receiver, "+e+" required");return r}}}},4914:(e,t,r)=>{var n=r(1278);e.exports=Array.isArray||function isArray(e){return"Array"===n(e)}},1483:e=>{var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},8730:(e,t,r)=>{var n=r(8473),o=r(1483),i=/#|\.prototype\./,isForced=function(e,t){var r=c[a(e)];return r===s||r!==u&&(o(t)?n(t):!!t)},a=isForced.normalize=function(e){return String(e).replace(i,".").toLowerCase()},c=isForced.data={},u=isForced.NATIVE="N",s=isForced.POLYFILL="P";e.exports=isForced},5983:e=>{e.exports=function(e){return null==e}},1704:(e,t,r)=>{var n=r(1483);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},9557:e=>{e.exports=!1},1423:(e,t,r)=>{var n=r(1409),o=r(1483),i=r(4815),a=r(5022),c=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return o(t)&&i(t.prototype,c(e))}},6960:(e,t,r)=>{var n=r(8324);e.exports=function(e){return n(e.length)}},169:(e,t,r)=>{var n=r(4762),o=r(8473),i=r(1483),a=r(5755),c=r(382),u=r(2048).CONFIGURABLE,s=r(7268),l=r(4483),f=l.enforce,p=l.get,d=String,v=Object.defineProperty,m=n("".slice),y=n("".replace),h=n([].join),g=c&&!o((function(){return 8!==v((function(){}),"length",{value:8}).length})),b=String(String).split("String"),w=e.exports=function(e,t,r){"Symbol("===m(d(t),0,7)&&(t="["+y(d(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!a(e,"name")||u&&e.name!==t)&&(c?v(e,"name",{value:t,configurable:!0}):e.name=t),g&&r&&a(r,"arity")&&e.length!==r.arity&&v(e,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?c&&v(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=f(e);return a(n,"source")||(n.source=h(b,"string"==typeof t?t:"")),e};Function.prototype.toString=w((function toString(){return i(this)&&p(this).source||s(this)}),"toString")},1703:e=>{var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function trunc(e){var n=+e;return(n>0?r:t)(n)}},5290:(e,t,r)=>{var n,o=r(2293),i=r(5799),a=r(4741),c=r(1507),u=r(2811),s=r(3145),l=r(5409),f="prototype",p="script",d=l("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(e){return"<"+p+">"+e+"</"+p+">"},NullProtoObjectViaActiveX=function(e){e.write(scriptTag("")),e.close();var t=e.parentWindow.Object;return e=null,t},NullProtoObject=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;NullProtoObject="undefined"!=typeof document?document.domain&&n?NullProtoObjectViaActiveX(n):(t=s("iframe"),r="java"+p+":",t.style.display="none",u.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(scriptTag("document.F=Object")),e.close(),e.F):NullProtoObjectViaActiveX(n);for(var o=a.length;o--;)delete NullProtoObject[f][a[o]];return NullProtoObject()};c[d]=!0,e.exports=Object.create||function create(e,t){var r;return null!==e?(EmptyConstructor[f]=o(e),r=new EmptyConstructor,EmptyConstructor[f]=null,r[d]=e):r=NullProtoObject(),void 0===t?r:i.f(r,t)}},5799:(e,t,r)=>{var n=r(382),o=r(3896),i=r(5835),a=r(2293),c=r(5599),u=r(3658);t.f=n&&!o?Object.defineProperties:function defineProperties(e,t){a(e);for(var r,n=c(t),o=u(t),s=o.length,l=0;s>l;)i.f(e,r=o[l++],n[r]);return e}},5835:(e,t,r)=>{var n=r(382),o=r(1799),i=r(3896),a=r(2293),c=r(3815),u=TypeError,s=Object.defineProperty,l=Object.getOwnPropertyDescriptor,f="enumerable",p="configurable",d="writable";t.f=n?i?function defineProperty(e,t,r){if(a(e),t=c(t),a(r),"function"==typeof e&&"prototype"===t&&"value"in r&&d in r&&!r[d]){var n=l(e,t);n&&n[d]&&(e[t]=r.value,r={configurable:p in r?r[p]:n[p],enumerable:f in r?r[f]:n[f],writable:!1})}return s(e,t,r)}:s:function defineProperty(e,t,r){if(a(e),t=c(t),a(r),o)try{return s(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},4961:(e,t,r)=>{var n=r(382),o=r(1807),i=r(7611),a=r(7738),c=r(5599),u=r(3815),s=r(5755),l=r(1799),f=Object.getOwnPropertyDescriptor;t.f=n?f:function getOwnPropertyDescriptor(e,t){if(e=c(e),t=u(t),l)try{return f(e,t)}catch(e){}if(s(e,t))return a(!o(i.f,e,t),e[t])}},2278:(e,t,r)=>{var n=r(6742),o=r(4741).concat("length","prototype");t.f=Object.getOwnPropertyNames||function getOwnPropertyNames(e){return n(e,o)}},4347:(e,t)=>{t.f=Object.getOwnPropertySymbols},4815:(e,t,r)=>{var n=r(4762);e.exports=n({}.isPrototypeOf)},6742:(e,t,r)=>{var n=r(4762),o=r(5755),i=r(5599),a=r(6651).indexOf,c=r(1507),u=n([].push);e.exports=function(e,t){var r,n=i(e),s=0,l=[];for(r in n)!o(c,r)&&o(n,r)&&u(l,r);for(;t.length>s;)o(n,r=t[s++])&&(~a(l,r)||u(l,r));return l}},3658:(e,t,r)=>{var n=r(6742),o=r(4741);e.exports=Object.keys||function keys(e){return n(e,o)}},7611:(e,t)=>{var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);t.f=o?function propertyIsEnumerable(e){var t=n(this,e);return!!t&&t.enumerable}:r},348:(e,t,r)=>{var n=r(1807),o=r(1483),i=r(1704),a=TypeError;e.exports=function(e,t){var r,c;if("string"===t&&o(r=e.toString)&&!i(c=n(r,e)))return c;if(o(r=e.valueOf)&&!i(c=n(r,e)))return c;if("string"!==t&&o(r=e.toString)&&!i(c=n(r,e)))return c;throw new a("Can't convert object to primitive value")}},9497:(e,t,r)=>{var n=r(1409),o=r(4762),i=r(2278),a=r(4347),c=r(2293),u=o([].concat);e.exports=n("Reflect","ownKeys")||function ownKeys(e){var t=i.f(c(e)),r=a.f;return r?u(t,r(e)):t}},3312:(e,t,r)=>{var n=r(5983),o=TypeError;e.exports=function(e){if(n(e))throw new o("Can't call method on "+e);return e}},5409:(e,t,r)=>{var n=r(7255),o=r(1866),i=n("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},1831:(e,t,r)=>{var n=r(9557),o=r(5578),i=r(2095),a="__core-js_shared__",c=e.exports=o[a]||i(a,{});(c.versions||(c.versions=[])).push({version:"3.38.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},7255:(e,t,r)=>{var n=r(1831);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},6029:(e,t,r)=>{var n=r(6477),o=r(8473),i=r(5578).String;e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},3392:(e,t,r)=>{var n=r(3005),o=Math.max,i=Math.min;e.exports=function(e,t){var r=n(e);return r<0?o(r+t,0):i(r,t)}},5599:(e,t,r)=>{var n=r(2121),o=r(3312);e.exports=function(e){return n(o(e))}},3005:(e,t,r)=>{var n=r(1703);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},8324:(e,t,r)=>{var n=r(3005),o=Math.min;e.exports=function(e){var t=n(e);return t>0?o(t,9007199254740991):0}},2347:(e,t,r)=>{var n=r(3312),o=Object;e.exports=function(e){return o(n(e))}},2355:(e,t,r)=>{var n=r(1807),o=r(1704),i=r(1423),a=r(2564),c=r(348),u=r(1),s=TypeError,l=u("toPrimitive");e.exports=function(e,t){if(!o(e)||i(e))return e;var r,u=a(e,l);if(u){if(void 0===t&&(t="default"),r=n(u,e,t),!o(r)||i(r))return r;throw new s("Can't convert object to primitive value")}return void 0===t&&(t="number"),c(e,t)}},3815:(e,t,r)=>{var n=r(2355),o=r(1423);e.exports=function(e){var t=n(e,"string");return o(t)?t:t+""}},8761:e=>{var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},1866:(e,t,r)=>{var n=r(4762),o=0,i=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},5022:(e,t,r)=>{var n=r(6029);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3896:(e,t,r)=>{var n=r(382),o=r(8473);e.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},4644:(e,t,r)=>{var n=r(5578),o=r(1483),i=n.WeakMap;e.exports=o(i)&&/native code/.test(String(i))},1:(e,t,r)=>{var n=r(5578),o=r(7255),i=r(5755),a=r(1866),c=r(6029),u=r(5022),s=n.Symbol,l=o("wks"),f=u?s.for||s:s&&s.withoutSetter||a;e.exports=function(e){return i(l,e)||(l[e]=c&&i(s,e)?s[e]:f("Symbol."+e)),l[e]}},6281:(e,t,r)=>{var n=r(8612),o=r(6651).includes,i=r(8473),a=r(7095);n({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function includes(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),a("includes")},5724:(e,t,r)=>{var n=r(8612),o=r(2347),i=r(6960),a=r(9273),c=r(1091);n({target:"Array",proto:!0,arity:1,forced:r(8473)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function push(e){var t=o(this),r=i(t),n=arguments.length;c(r+n);for(var u=0;u<n;u++)t[r]=arguments[u],r++;return a(t,r),r}})}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,__webpack_require__),o.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();(()=>{var e=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(__webpack_require__(2098));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function(e){return e?r:t})(e)}class Preview extends elementorModules.ViewModule{constructor(){super(),elementorFrontend.on("components:init",(()=>this.onFrontendComponentsInit()))}addDocumentClass(){const e=elementor.documents.getCurrent();e&&e.$element&&e.$element.parents("[data-elementor-id]").addClass("e-embedded-document-active")}removeDocumentClass(){Object.values(elementorFrontend.documentsManager.documents).forEach((e=>{e.$element.get(0).classList.remove("e-embedded-document-active")}))}createDocumentsHandles(){Object.values(elementorFrontend.documentsManager.documents).forEach((t=>{const r=t.$element.get(0),{elementorTitle:n,customEditHandle:o}=r.dataset;if(o)return;const i=t.getSettings("id");(0,e.default)({element:r,title:n,id:i},e.EDIT_CONTEXT,null,".elementor-"+i)}))}onFrontendComponentsInit(){this.addDocumentClass(),this.createDocumentsHandles(),elementor.on("document:loaded",(()=>{this.addDocumentClass(),this.createDocumentsHandles()})),elementor.on("document:unloaded",(()=>{this.removeDocumentClass()}))}}window.elementorProPreview=new Preview})()})();