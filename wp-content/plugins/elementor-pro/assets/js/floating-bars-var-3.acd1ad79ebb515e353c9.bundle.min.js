/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[440],{9744:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=class FloatingBarDomHelper{constructor(t){this.$element=t}maybeMoveToTop(){const t=this.$element[0],e=t.querySelector(".e-floating-bars");if(elementorFrontend.isEditMode())e.classList.add("is-sticky");else if(t.dataset.widget_type.startsWith("floating-bars")&&e.classList.contains("has-vertical-position-top")&&!e.classList.contains("is-sticky")){const e=document.getElementById("wpadminbar"),n=t.closest(".elementor");e?e.after(n):document.body.prepend(n)}}}},7704:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=s(n(8562)),o=n(6367),a=s(n(9744)),r=s(n(8243));class FloatingBarsHandler extends i.default{getDefaultSettings(){return{selectors:{main:".e-floating-bars",mainV3:".e-floating-bars-var-3",closeButton:".e-floating-bars__close-button",couponButton:".e-floating-bars__coupon-button",couponCode:".e-floating-bars__coupon-code",codeTextGroup:".e-floating-bars__coupon-code",successTextGroup:".e-floating-bars__coupon-success"},constants:{couponEntranceAnimation:"style_coupon_animation",couponEntranceAnimationDelay:"style_coupon_animation_delay",hasEntranceAnimation:"has-entrance-animation",visible:"visible",isSticky:"is-sticky",hasVerticalPositionTop:"has-vertical-position-top",hasVerticalPositionBottom:"has-vertical-position-bottom",isHidden:"is-hidden",successMessageDurationControl:"style_coupon_success_message_duration",animated:"animated"}}}getDefaultElements(){const t=this.getSettings("selectors");return{main:this.$element[0].querySelector(t.main),mainV3:this.$element[0].querySelector(t.mainV3),mainAll:this.$element[0].querySelectorAll(t.main),closeButton:this.$element[0].querySelector(t.closeButton),couponButton:this.$element[0].querySelector(t.couponButton),couponCode:this.$element[0].querySelector(t.couponCode),codeTextGroup:this.$element[0].querySelector(t.codeTextGroup),successTextGroup:this.$element[0].querySelector(t.successTextGroup)}}getResponsiveSetting(t){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),t,"",e)}bindEvents(){this.elements.closeButton&&this.elements.closeButton.addEventListener("click",this.closeFloatingBar.bind(this)),this.elements.couponButton&&this.elements.couponButton.addEventListener("animationend",this.handleAnimationEnd.bind(this)),this.elements.main&&window.addEventListener("keyup",this.onDocumentKeyup.bind(this)),this.elements.couponButton&&this.elements.couponButton.addEventListener("click",this.handleCouponButtonClick.bind(this)),this.hasStickyElements()&&window.addEventListener("resize",this.handleStickyElements.bind(this))}isStickyTop(){const{isSticky:t,hasVerticalPositionTop:e}=this.getSettings("constants");return this.elements.main.classList.contains(t)&&this.elements.main.classList.contains(e)}isStickyBottom(){const{isSticky:t,hasVerticalPositionBottom:e}=this.getSettings("constants");return this.elements.main.classList.contains(t)&&this.elements.main.classList.contains(e)}hasStickyElements(){return document.querySelectorAll(".elementor-sticky").length>0}focusOnLoad(){this.elements.main.setAttribute("tabindex","0"),this.elements.main.focus({focusVisible:!0})}applyBodyPadding(){const t=this.elements.main.offsetHeight;document.body.style.paddingTop=`${t}px`}removeBodyPadding(){document.body.style.paddingTop="0"}handleWPAdminBar(){const t=elementorFrontend.elements.$wpAdminBar;t.length&&(this.elements.main.style.top=`${t.height()}px`)}handleStickyElements(){const t=this.elements.main.offsetHeight,e=elementorFrontend.elements.$wpAdminBar,n=document.querySelectorAll(".elementor-sticky:not(.elementor-sticky__spacer)");0!==n.length&&(n.forEach((n=>{const s=n.getAttribute("data-settings"),i=JSON.parse(s)?.sticky,o="0px"===n.style.top||"top"===i,a="0px"===n.style.bottom||"bottom"===i;this.isStickyTop()&&o?e.length?n.style.top=`${t+e.height()}px`:n.style.top=`${t}px`:this.isStickyBottom()&&a&&(n.style.bottom=`${t}px`),elementorFrontend.isEditMode()&&(o?n.style.top=this.isStickyTop()?`${t}px`:"0px":a&&(n.style.bottom=this.isStickyBottom()?`${t}px`:"0px"))})),document.querySelectorAll(".elementor-sticky__spacer").forEach((e=>{const n=e.getAttribute("data-settings"),s=JSON.parse(n)?.sticky,i="0px"===e.style.top||"top"===s;this.isStickyTop()&&i&&(e.style.marginBottom=`${t}px`)})))}closeFloatingBar(){const{isHidden:t}=this.getSettings("constants");elementorFrontend.isEditMode()||(this.elements.main.classList.add(t),this.hasStickyElements()?this.handleStickyElements():this.isStickyTop()&&this.removeBodyPadding())}initEntranceAnimation(){const{animated:t,couponEntranceAnimation:e,couponEntranceAnimationDelay:n,hasEntranceAnimation:s}=this.getSettings("constants"),i=this.getResponsiveSetting(e),o=(this.getResponsiveSetting(n)||0)+500;this.elements.couponButton.classList.add(t),this.elements.couponButton.classList.add(i),setTimeout((()=>{this.elements.couponButton.classList.remove(s)}),o)}handleAnimationEnd(){this.removeEntranceAnimationClasses(),this.focusOnLoad()}removeEntranceAnimationClasses(){if(!this.elements.couponButton)return;const{animated:t,couponEntranceAnimation:e,visible:n}=this.getSettings("constants"),s=this.getResponsiveSetting(e);this.elements.couponButton.classList.remove(t),this.elements.couponButton.classList.remove(s),this.elements.couponButton.classList.add(n)}onDocumentKeyup(t){27===t.keyCode&&this.elements.main&&this.elements.main.contains(document.activeElement)&&this.closeFloatingBar()}getDuration(t){const e="s"===t.unit,n=e?"1.5":"1500",s=""!==t.size?t.size:n;return e?1e3*s:s}handleCouponButtonClick(t){const{successMessageDurationControl:e,isHidden:n}=this.getSettings("constants"),s=this.elements.couponCode.innerText,i=this.getResponsiveSetting(e),a=this.getDuration(i),r=t.currentTarget.getBoundingClientRect().width,l=t.currentTarget.getBoundingClientRect().height;this.elements.mainV3.style.setProperty("--e-floating-bars-coupon-width",`${r}px`),this.elements.mainV3.style.setProperty("--e-floating-bars-coupon-height",`${l}px`),(0,o.copyToClipboard)(s),this.elements.codeTextGroup.classList.add(n),this.elements.successTextGroup.classList.remove(n),setTimeout((()=>{this.elements.codeTextGroup.classList.remove(n),this.elements.successTextGroup.classList.add(n),this.elements.mainV3.style.setProperty("--e-floating-bars-coupon-width","initial"),this.elements.mainV3.style.setProperty("--e-floating-bars-coupon-height","initial")}),a)}initDefaultState(){const{hasEntranceAnimation:t}=this.getSettings("constants");this.isStickyTop()&&this.handleWPAdminBar(),this.hasStickyElements()?this.handleStickyElements():this.isStickyTop()&&this.applyBodyPadding(),!this.elements.main||this.elements.couponButton.classList.contains(t)||elementorFrontend.isEditMode()||this.focusOnLoad()}setupInnerContainer(){this.elements.main.closest(".e-con-inner").classList.add("e-con-inner--floating-bars"),this.elements.main.closest(".e-con").classList.add("e-con--floating-bars")}onInit(){const{hasEntranceAnimation:t}=this.getSettings("constants");super.onInit(...arguments),this.clickTrackingHandler=new r.default({$element:this.$element});new a.default(this.$element).maybeMoveToTop(),this.elements.couponButton&&this.elements.couponButton.classList.contains(t)&&this.initEntranceAnimation(),this.initDefaultState(),this.setupInnerContainer()}}e.default=FloatingBarsHandler},8243:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=s(n(8562));class ClickTrackingHandler extends i.default{clicks=[];getDefaultSettings(){return{selectors:{contentWrapper:".e-contact-buttons__content-wrapper",contentWrapperFloatingBars:".e-floating-bars",floatingBarCouponButton:".e-floating-bars__coupon-button",floatingBarsHeadline:".e-floating-bars__headline",contactButtonsVar4:".e-contact-buttons__contact-icon-link",contactButtonsVar5:".e-contact-buttons__chat-button",contactButtonsVar6:".e-contact-buttons-var-6",contactButtonsVar8:".e-contact-buttons-var-8",elementorWrapper:'[data-elementor-type="floating-buttons"]',contactButtonCore:".e-contact-buttons__send-button"}}}getDefaultElements(){const t=this.getSettings("selectors");return{contentWrapper:this.$element[0].querySelector(t.contentWrapper),contentWrapperFloatingBars:this.$element[0].querySelector(t.contentWrapperFloatingBars),contactButtonsVar5:this.$element[0].querySelector(t.contactButtonsVar5),contactButtonsVar6:this.$element[0].querySelector(t.contactButtonsVar6)}}bindEvents(){this.elements.contentWrapper&&this.elements.contentWrapper.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contactButtonsVar5&&this.elements.contactButtonsVar5.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contactButtonsVar6&&this.elements.contactButtonsVar6.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contentWrapperFloatingBars&&this.elements.contentWrapperFloatingBars.addEventListener("click",this.onChatButtonTrackClick.bind(this)),window.addEventListener("beforeunload",(()=>{this.clicks.length>0&&this.sendClicks()}))}onChatButtonTrackClick(t){const e=t.target||t.srcElement,n=this.getSettings("selectors"),s=[n.contactButtonsVar4,n.contactButtonsVar6,n.floatingBarCouponButton,n.floatingBarsHeadline,n.contactButtonCore];for(const t of s)(e.matches(t)||e.closest(t))&&this.getDocumentIdAndTrack(e,n);(e.matches(n.contactButtonsVar5)||e.closest(n.contactButtonsVar5))&&e.closest(".e-contact-buttons-var-5")&&this.getDocumentIdAndTrack(e,n)}getDocumentIdAndTrack(t,e){const n=t.closest(e.elementorWrapper).dataset.elementorId;this.trackClick(n)}trackClick(t){t&&(this.clicks.push(t),this.clicks.length>=10&&this.sendClicks())}sendClicks(){const t=new FormData;t.append("action","elementor_send_clicks"),t.append("_nonce",elementorFrontendConfig?.nonces?.floatingButtonsClickTracking),this.clicks.forEach((e=>t.append("clicks[]",e))),fetch(elementorFrontendConfig?.urls?.ajaxurl,{method:"POST",body:t}).then((()=>{this.clicks=[]}))}}e.default=ClickTrackingHandler},6367:(t,e)=>{function canCopyToClipboard(){return!!navigator?.clipboard}Object.defineProperty(e,"__esModule",{value:!0}),e.canCopyToClipboard=canCopyToClipboard,e.copyToClipboard=function copyToClipboard(t){if(!canCopyToClipboard())throw new Error("Cannot copy to clipboard, please make sure you are using SSL in your website.");navigator.clipboard.writeText(t)}},8562:t=>{t.exports=elementorModules.ViewModule.extend({$element:null,editorListeners:null,onElementChange:null,onEditSettingsChange:null,onPageSettingsChange:null,isEdit:null,__construct(t){this.isActive(t)&&(this.$element=t.$element,this.isEdit=this.$element.hasClass("elementor-element-edit-mode"),this.isEdit&&this.addEditorListeners())},isActive:()=>!0,isElementInTheCurrentDocument(){return!!elementorFrontend.isEditMode()&&elementor.documents.currentDocument.id.toString()===this.$element[0].closest(".elementor").dataset.elementorId},findElement(t){var e=this.$element;return e.find(t).filter((function(){return jQuery(this).parent().closest(".elementor-element").is(e)}))},getUniqueHandlerID(t,e){return t||(t=this.getModelCID()),e||(e=this.$element),t+e.attr("data-element_type")+this.getConstructorID()},initEditorListeners(){var t=this;if(t.editorListeners=[{event:"element:destroy",to:elementor.channels.data,callback(e){e.cid===t.getModelCID()&&t.onDestroy()}}],t.onElementChange){const e=t.getWidgetType()||t.getElementType();let n="change";"global"!==e&&(n+=":"+e),t.editorListeners.push({event:n,to:elementor.channels.editor,callback(e,n){t.getUniqueHandlerID(n.model.cid,n.$el)===t.getUniqueHandlerID()&&t.onElementChange(e.model.get("name"),e,n)}})}t.onEditSettingsChange&&t.editorListeners.push({event:"change:editSettings",to:elementor.channels.editor,callback(e,n){if(n.model.cid!==t.getModelCID())return;const s=Object.keys(e.changed)[0];t.onEditSettingsChange(s,e.changed[s])}}),["page"].forEach((function(e){var n="on"+e[0].toUpperCase()+e.slice(1)+"SettingsChange";t[n]&&t.editorListeners.push({event:"change",to:elementor.settings[e].model,callback(e){t[n](e.changed)}})}))},getEditorListeners(){return this.editorListeners||this.initEditorListeners(),this.editorListeners},addEditorListeners(){var t=this.getUniqueHandlerID();this.getEditorListeners().forEach((function(e){elementorFrontend.addListenerOnce(t,e.event,e.callback,e.to)}))},removeEditorListeners(){var t=this.getUniqueHandlerID();this.getEditorListeners().forEach((function(e){elementorFrontend.removeListeners(t,e.event,null,e.to)}))},getElementType(){return this.$element.data("element_type")},getWidgetType(){const t=this.$element.data("widget_type");if(t)return t.split(".")[0]},getID(){return this.$element.data("id")},getModelCID(){return this.$element.data("model-cid")},getElementSettings(t){let e={};const n=this.getModelCID();if(this.isEdit&&n){const t=elementorFrontend.config.elements.data[n],s=t.attributes;let i=s.widgetType||s.elType;s.isInner&&(i="inner-"+i);let o=elementorFrontend.config.elements.keys[i];o||(o=elementorFrontend.config.elements.keys[i]=[],jQuery.each(t.controls,((t,e)=>{(e.frontend_available||e.editor_available)&&o.push(t)}))),jQuery.each(t.getActiveControls(),(function(t){if(-1!==o.indexOf(t)){let n=s[t];n.toJSON&&(n=n.toJSON()),e[t]=n}}))}else e=this.$element.data("settings")||{};return this.getItems(e,t)},getEditSettings(t){var e={};return this.isEdit&&(e=elementorFrontend.config.elements.editSettings[this.getModelCID()].attributes),this.getItems(e,t)},getCurrentDeviceSetting(t){return elementorFrontend.getCurrentDeviceSetting(this.getElementSettings(),t)},onInit(){this.isActive(this.getSettings())&&elementorModules.ViewModule.prototype.onInit.apply(this,arguments)},onDestroy(){this.isEdit&&this.removeEditorListeners(),this.unbindEvents&&this.unbindEvents()}})}}]);