/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[225],{6974:(t,e)=>{function getChildrenWidth(t){let e=0;const n=t[0].parentNode,i=getComputedStyle(n),l=parseFloat(i.gap)||0;for(let n=0;n<t.length;n++)e+=t[n].offsetWidth+l;return e}Object.defineProperty(e,"__esModule",{value:!0}),e.changeScrollStatus=function changeScrollStatus(t,e){"mousedown"===e.type?(t.classList.add("e-scroll"),t.dataset.pageX=e.pageX):(t.classList.remove("e-scroll","e-scroll-active"),t.dataset.pageX="")},e.setHorizontalScrollAlignment=function setHorizontalScrollAlignment(t){let{element:e,direction:n,justifyCSSVariable:i,horizontalScrollStatus:l}=t;if(!e)return;!function isHorizontalScroll(t,e){return t.clientWidth<getChildrenWidth(t.children)&&"enable"===e}(e,l)?e.style.setProperty(i,""):function initialScrollPosition(t,e,n){const i=elementorFrontend.config.is_rtl;if("end"===e)t.style.setProperty(n,"start"),t.scrollLeft=i?-1*getChildrenWidth(t.children):getChildrenWidth(t.children);else t.style.setProperty(n,"start"),t.scrollLeft=0}(e,n,i)},e.setHorizontalTitleScrollValues=function setHorizontalTitleScrollValues(t,e,n){const i=t.classList.contains("e-scroll"),l="enable"===e,s=t.scrollWidth>t.clientWidth;if(!i||!l||!s)return;n.preventDefault();const r=parseFloat(t.dataset.pageX),o=n.pageX-r;let a=0;a=20<o?5:-20>o?-5:o;t.scrollLeft=t.scrollLeft-a,t.classList.add("e-scroll-active")}},2236:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(6974),l=n(275);class TaxonomyFilter extends elementorModules.frontend.handlers.Base{constructor(){super(...arguments),this.resizeListenerNestedTabs=null}getDefaultSettings(){return{selectors:{item:".e-filter-item",container:".e-filter"},filterValues:{default:"__all"}}}getDefaultElements(){return{$filterButtons:this.$element.find(this.getSettings("selectors.item")),$container:this.$element.find(this.getSettings("selectors.container"))}}getHeadingEvents(){const t=this.elements.$container[0];return{mousedown:i.changeScrollStatus.bind(this,t),mouseup:i.changeScrollStatus.bind(this,t),mouseleave:i.changeScrollStatus.bind(this,t),mousemove:i.setHorizontalTitleScrollValues.bind(this,t,this.getHorizontalScrollSetting())}}bindEvents(){this.elements.$filterButtons.on("click",this.onFilterButtonClick.bind(this)),this.elements.$container.on(this.getHeadingEvents());const t={element:this.elements.$container[0],direction:this.getItemsAlignment(),justifyCSSVariable:"--e-filter-justify-content",horizontalScrollStatus:this.getHorizontalScrollSetting()};this.resizeListenerNestedTabs=i.setHorizontalScrollAlignment.bind(this,t),elementorFrontend.elements.$window.on("resize",this.resizeListenerNestedTabs)}onElementChange(t){if(this.checkSliderPropsToWatch(t)){const t={element:this.elements.$container[0],direction:this.getItemsAlignment(),justifyCSSVariable:"--e-filter-justify-content",horizontalScrollStatus:this.getHorizontalScrollSetting()};(0,i.setHorizontalScrollAlignment)(t)}}checkSliderPropsToWatch(t){return 0===t.indexOf("horizontal_scroll")||0===t.indexOf("item_alignment_horizontal")}getFilterButtonElements(){return this.elements?.$filterButtons.length||(this.elements=this.getDefaultElements(),this.bindEvents()),this.elements.$filterButtons}getActiveFilterButtonElements(){return this.getFilterButtonElements().filter('[aria-pressed="true"]')}activateFilterButton(t){const e=this.getFilterButtonElements(),n="yes"===this.getElementSettings("multiple_selection");if(!e.length)return;const i=this.getSettings("filterValues.default");n&&i!==t||e.attr("aria-pressed",!1);e.filter('[data-filter="'+t+'"]').attr("aria-pressed",!0);const l=this.getCurrentlyActiveFilter();l&&l.includes(i)&&i!==t&&this.deactivateDefaultFilterButton(e)}deactivateFilterButton(t){const e=this.getFilterButtonElements(),n="yes"===this.getElementSettings("multiple_selection");if(!e.length)return;const i=e.filter('[data-filter="'+t+'"]'),l=this.getSettings("filterValues.default"),s=this.getCurrentlyActiveFilter(),r=!n||!s.includes(l)&&1===s.length;i.attr("aria-pressed",!1),r&&this.activateDefaultFilterButton(),elementorProFrontend.modules.taxonomyFilter.removeFilterFromLoopWidget(this.getElementSettings("selected_element"),this.getID(),t,l)}activateDefaultFilterButton(){const t=this.getFilterButtonElements(),e=t.filter('[data-filter="'+this.getSettings("filterValues.default")+'"]');t.attr("aria-pressed",!1),e.attr("aria-pressed",!0)}deactivateDefaultFilterButton(){this.getFilterButtonElements().filter('[data-filter="'+this.getSettings("filterValues.default")+'"]').attr("aria-pressed",!1)}getCurrentlyActiveFilter(){const t=this.getActiveFilterButtonElements(),e=[];for(let n=0;n<t.length;n++)e.push(t[n].dataset.filter);return e}getFilterOperator(){const t=this.getElementSettings();return t.multiple_selection&&["AND","OR"].includes(t.logical_combination)?t.logical_combination:"DISABLED"}filterItems(t){const e=this.getElementSettings(),n=this.getSettings("filterValues.default");if(n===t)return void elementorProFrontend.modules.taxonomyFilter.removeFilterFromLoopWidget(e.selected_element,this.getID(),t,n);const i=this.getFilterOperator();elementorProFrontend.modules.taxonomyFilter.setFilterDataForLoopWidget(e.selected_element,this.getID(),{filterType:"taxonomy",filterData:{selectedTaxonomy:e.taxonomy,terms:[t]}},!0,i)}setFilter(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSettings("filterValues.default");this.filterItems(t),this.activateFilterButton(t)}onFilterButtonClick(t){this.removePaginationHiddenClassOnLoopWidgetContainer();const e=this.getCurrentlyActiveFilter(),n=t.currentTarget?.dataset?.filter;this.userClickedOnAllWhileItWasActive(n,e)||(e.includes(n)?this.deactivateFilterButton(n):this.setFilter(n))}removePaginationHiddenClassOnLoopWidgetContainer(){const t=this.getElementSettings(),e=document.querySelector(".elementor-element-"+t.selected_element);e&&e.classList.remove("e-load-more-pagination-end")}userClickedOnAllWhileItWasActive(t,e){return e.includes(t)&&t===this.getSettings("filterValues.default")}onDestroy(){const t=this.getElementSettings("selected_element"),e=this.getElementSettings("taxonomy"),n=this.getID();t&&e&&elementorProFrontend.modules.taxonomyFilter.removeFilterFromLoopWidget(t,n,""),super.onDestroy()}populateLoopWidgetsStoreOnInitialPageLoad(){const t=this.getElementSettings();let e=new URLSearchParams(window.location.search).get("e-filter-"+t.selected_element+"-"+t.taxonomy);if(e){e=this.getTermsFromParams(e);const n=this.getFilterOperator();elementorProFrontend.modules.taxonomyFilter.setFilterDataForLoopWidget(t.selected_element,this.getID(),{filterType:"taxonomy",filterData:{selectedTaxonomy:t.taxonomy,terms:e}},!1,n)}}getTermsFromParams(t){let e=l.queryConstants.AND.separator.fromBrowser;return t.includes(l.queryConstants.OR.separator.fromBrowser)&&(e=l.queryConstants.OR.separator.fromBrowser),t.split(e)}onInit(){super.onInit(),this.populateLoopWidgetsStoreOnInitialPageLoad();const t={element:this.elements.$container[0],direction:this.getItemsAlignment(),justifyCSSVariable:"--e-filter-justify-content",horizontalScrollStatus:this.getHorizontalScrollSetting()};(0,i.setHorizontalScrollAlignment)(t)}getHorizontalScrollSetting(){const t=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"horizontal_scroll","",t)}getItemsAlignment(){const t=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"item_alignment_horizontal","",t)}}e.default=TaxonomyFilter}}]);