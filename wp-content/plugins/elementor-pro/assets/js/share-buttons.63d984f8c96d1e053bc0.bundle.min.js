/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[316],{6613:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function handleParameterPollution(e){const t=new URL(e),n=t.hostname,r=new URLSearchParams(t.search);return["u"].forEach((e=>{const t=r.get(e);if(t)try{new URL(t).hostname!==n&&r.delete(e)}catch(t){r.delete(e)}})),t.search=r.toString(),t.toString()}},3607:(e,t,n)=>{var r=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=r(n(6613));t.default=elementorModules.frontend.handlers.Base.extend({async onInit(){if(!this.isActive())return;elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments);const e=this.getElementSettings(),t=this.getSettings("classes"),n=e.share_url&&e.share_url.url,r={classPrefix:t.shareLinkPrefix};n?r.url=e.share_url.url:(r.url=(0,s.default)(location.href),r.title=elementorFrontend.config.post.title,r.text=elementorFrontend.config.post.excerpt,r.image=elementorFrontend.config.post.featuredImage),!window.ShareLink&&elementorFrontend.utils.assetsLoader&&await elementorFrontend.utils.assetsLoader.load("script","share-link"),this.elements.$shareButton.shareLink&&this.elements.$shareButton.shareLink(r)},getDefaultSettings:()=>({selectors:{shareButton:".elementor-share-btn"},classes:{shareLinkPrefix:"elementor-share-btn_"}}),getDefaultElements(){var e=this.getSettings("selectors");return{$shareButton:this.$element.find(e.shareButton)}},isActive:()=>!elementorFrontend.isEditMode()})}}]);