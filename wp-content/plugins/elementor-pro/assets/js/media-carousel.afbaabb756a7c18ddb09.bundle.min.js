/*! elementor-pro - v3.27.0 - 06-02-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[692],{5456:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class CarouselBase extends elementorModules.frontend.handlers.SwiperBase{getDefaultSettings(){return{selectors:{swiperContainer:".elementor-main-swiper",swiperSlide:".swiper-slide"},slidesPerView:{widescreen:3,desktop:3,laptop:3,tablet_extra:3,tablet:2,mobile_extra:2,mobile:1}}}getDefaultElements(){const e=this.getSettings("selectors"),t={$swiperContainer:this.$element.find(e.swiperContainer)};return t.$slides=t.$swiperContainer.find(e.swiperSlide),t}getEffect(){return this.getElementSettings("effect")}getDeviceSlidesPerView(e){const t="slides_per_view"+("desktop"===e?"":"_"+e);return Math.min(this.getSlidesCount(),+this.getElementSettings(t)||this.getSettings("slidesPerView")[e])}getSlidesPerView(e){return"slide"===this.getEffect()?this.getDeviceSlidesPerView(e):1}getDeviceSlidesToScroll(e){const t="slides_to_scroll"+("desktop"===e?"":"_"+e);return Math.min(this.getSlidesCount(),+this.getElementSettings(t)||1)}getSlidesToScroll(e){return"slide"===this.getEffect()?this.getDeviceSlidesToScroll(e):1}getSpaceBetween(e){let t="space_between";return e&&"desktop"!==e&&(t+="_"+e),this.getElementSettings(t).size||0}getSwiperOptions(){const e=this.getElementSettings(),t={grabCursor:!0,initialSlide:this.getInitialSlide(),slidesPerView:this.getSlidesPerView("desktop"),slidesPerGroup:this.getSlidesToScroll("desktop"),spaceBetween:this.getSpaceBetween(),loop:"yes"===e.loop,speed:e.speed,effect:this.getEffect(),preventClicksPropagation:!1,slideToClickedSlide:!0,handleElementorBreakpoints:!0};if("yes"===e.lazyload&&(t.lazy={loadPrevNext:!0,loadPrevNextAmount:1}),e.show_arrows&&(t.navigation={prevEl:".elementor-swiper-button-prev",nextEl:".elementor-swiper-button-next"}),e.pagination&&(t.pagination={el:".swiper-pagination",type:e.pagination,clickable:!0}),"cube"!==this.getEffect()){const e={},i=elementorFrontend.config.responsive.activeBreakpoints;Object.keys(i).forEach((t=>{e[i[t].value]={slidesPerView:this.getSlidesPerView(t),slidesPerGroup:this.getSlidesToScroll(t),spaceBetween:this.getSpaceBetween(t)}})),t.breakpoints=e}return!this.isEdit&&e.autoplay&&(t.autoplay={delay:e.autoplay_speed,disableOnInteraction:!!e.pause_on_interaction}),t}getDeviceBreakpointValue(e){if(!this.breakpointsDictionary){const e=elementorFrontend.config.responsive.activeBreakpoints;this.breakpointsDictionary={},Object.keys(e).forEach((t=>{this.breakpointsDictionary[t]=e[t].value}))}return this.breakpointsDictionary[e]}updateSpaceBetween(e){const t=e.match("space_between_(.*)"),i=t?t[1]:"desktop",s=this.getSpaceBetween(i);"desktop"!==i?this.swiper.params.breakpoints[this.getDeviceBreakpointValue(i)].spaceBetween=s:this.swiper.params.spaceBetween=s,this.swiper.params.spaceBetween=s,this.swiper.update()}async onInit(){if(elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments),1>=this.getSlidesCount())return;const e=elementorFrontend.utils.swiper;this.swiper=await new e(this.elements.$swiperContainer,this.getSwiperOptions());"yes"===this.getElementSettings().pause_on_hover&&this.togglePauseOnHover(!0),this.elements.$swiperContainer.data("swiper",this.swiper)}getChangeableProperties(){return{autoplay:"autoplay",pause_on_hover:"pauseOnHover",pause_on_interaction:"disableOnInteraction",autoplay_speed:"delay",speed:"speed",width:"width"}}updateSwiperOption(e){if(0===e.indexOf("width"))return void this.swiper.update();const t=this.getElementSettings(),i=t[e];let s=this.getChangeableProperties()[e],n=i;switch(e){case"autoplay":n=!!i&&{delay:t.autoplay_speed,disableOnInteraction:"yes"===t.pause_on_interaction};break;case"autoplay_speed":s="autoplay",n={delay:i,disableOnInteraction:"yes"===t.pause_on_interaction};break;case"pause_on_hover":this.togglePauseOnHover("yes"===i);break;case"pause_on_interaction":n="yes"===i}"pause_on_hover"!==e&&(this.swiper.params[s]=n),this.swiper.update()}onElementChange(e){if(1>=this.getSlidesCount())return;if(0===e.indexOf("width"))return this.swiper.update(),void(this.thumbsSwiper&&this.thumbsSwiper.update());if(0===e.indexOf("space_between"))return void this.updateSpaceBetween(e);const t=this.getChangeableProperties();Object.prototype.hasOwnProperty.call(t,e)&&this.updateSwiperOption(e)}onEditSettingsChange(e){1>=this.getSlidesCount()||"activeItemIndex"===e&&this.swiper.slideToLoop(this.getEditSettings("activeItemIndex")-1)}}t.default=CarouselBase},8948:(e,t,i)=>{var s=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(i(5456));class MediaCarousel extends n.default{isSlideshow(){return"slideshow"===this.getElementSettings("skin")}getDefaultSettings(){const e=super.getDefaultSettings(...arguments);return this.isSlideshow()&&(e.selectors.thumbsSwiper=".elementor-thumbnails-swiper",e.slidesPerView={widescreen:5,desktop:5,laptop:5,tablet_extra:5,tablet:4,mobile_extra:4,mobile:3}),e}getSlidesPerViewSettingNames(){if(!this.slideshowElementSettings){this.slideshowElementSettings=["slides_per_view"];const e=elementorFrontend.config.responsive.activeBreakpoints;Object.keys(e).forEach((e=>{this.slideshowElementSettings.push("slides_per_view_"+e)}))}return this.slideshowElementSettings}getElementSettings(e){return-1!==this.getSlidesPerViewSettingNames().indexOf(e)&&this.isSlideshow()&&(e="slideshow_"+e),super.getElementSettings(e)}getDefaultElements(){const e=this.getSettings("selectors"),t=super.getDefaultElements(...arguments);return this.isSlideshow()&&(t.$thumbsSwiper=this.$element.find(e.thumbsSwiper)),t}getEffect(){return"coverflow"===this.getElementSettings("skin")?"coverflow":super.getEffect()}getSlidesPerView(e){return this.isSlideshow()?1:"coverflow"===this.getElementSettings("skin")?this.getDeviceSlidesPerView(e):super.getSlidesPerView(e)}getSwiperOptions(){const e=super.getSwiperOptions();return this.isSlideshow()&&(e.loopedSlides=this.getSlidesCount(),delete e.pagination,delete e.breakpoints),e}async onInit(){await super.onInit();const e=this.getSlidesCount();if(!this.isSlideshow()||1>=e)return;const t=this.getElementSettings(),i="yes"===t.loop,s={},n=elementorFrontend.config.responsive.activeBreakpoints,r=this.getDeviceSlidesPerView("desktop");Object.keys(n).forEach((e=>{s[n[e].value]={slidesPerView:this.getDeviceSlidesPerView(e),spaceBetween:this.getSpaceBetween(e)}}));const o={slidesPerView:r,initialSlide:this.getInitialSlide(),centeredSlides:t.centered_slides,slideToClickedSlide:!0,spaceBetween:this.getSpaceBetween(),loopedSlides:e,loop:i,breakpoints:s,handleElementorBreakpoints:!0};"yes"===t.lazyload&&(o.lazy={loadPrevNext:!0,loadPrevNextAmount:1});const l=elementorFrontend.utils.swiper;this.swiper.controller.control=this.thumbsSwiper=await new l(this.elements.$thumbsSwiper,o),this.elements.$thumbsSwiper.data("swiper",this.thumbsSwiper),this.thumbsSwiper.controller.control=this.swiper}}t.default=MediaCarousel}}]);