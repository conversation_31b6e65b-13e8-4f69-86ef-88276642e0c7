# Copyright (C) 2025 Better Payment
# This file is distributed under the same license as the Better Payment package.
msgid ""
msgstr ""
"Project-Id-Version: Better Payment\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../includes/Admin.php:80, ../includes/Admin/Settings.php:90, ../includes/Admin/Settings.php:91, ../includes/Admin/views/better-payment-settings.php:42
msgid "Settings"
msgstr ""

#: ../includes/Admin.php:83
msgid "Go Pro"
msgstr ""

#: ../includes/Assets.php:156, ../includes/Assets.php:173, ../includes/Admin/Setup_Wizard.php:73
msgid "Are you sure?"
msgstr ""

#: ../includes/Assets.php:157, ../includes/Assets.php:166
msgid "Something went wrong"
msgstr ""

#: ../includes/Assets.php:159
msgid "Redirecting"
msgstr ""

#: ../includes/Assets.php:162
msgid "field is required"
msgstr ""

#: ../includes/Assets.php:163
msgid "Business Email is required"
msgstr ""

#: ../includes/Assets.php:164
msgid "Payment Amount field is required"
msgstr ""

#: ../includes/Assets.php:165
msgid "Minimum amount is 1"
msgstr ""

#: ../includes/Assets.php:174, ../includes/Admin/Setup_Wizard.php:74
msgid "You won't be able to revert this!"
msgstr ""

#: ../includes/Assets.php:175, ../includes/Admin/Setup_Wizard.php:75
msgid "Yes, delete it!"
msgstr ""

#: ../includes/Assets.php:176, ../includes/Admin/Setup_Wizard.php:76
msgid "No, cancel!"
msgstr ""

#: ../includes/Assets.php:179, ../includes/Admin/Setup_Wizard.php:79
msgid "Changes saved successfully!"
msgstr ""

#: ../includes/Assets.php:180, ../includes/Admin/Setup_Wizard.php:80
msgid "Opps! something went wrong!"
msgstr ""

#: ../includes/Assets.php:181, ../includes/Admin/Setup_Wizard.php:81
msgid "No action taken!"
msgstr ""

#: ../includes/Admin/Settings.php:79, ../includes/Admin/Settings.php:80, ../includes/Classes/Actions.php:104, ../includes/Classes/Actions.php:247, ../includes/Classes/Actions.php:253, ../includes/Admin/Elementor/Better_Payment_Widget.php:59, ../includes/Admin/Elementor/Better_Payment_Widget.php:874, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:34, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:46, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:107
msgid "Better Payment"
msgstr ""

#: ../includes/Admin/Settings.php:102, ../includes/Admin/Elementor/User_Dashboard.php:169, ../includes/Admin/Elementor/User_Dashboard.php:239, ../includes/Admin/Elementor/User_Dashboard.php:242
msgid "Transactions"
msgstr ""

#: ../includes/Admin/Settings.php:110
msgid "Analytics"
msgstr ""

#: ../includes/Admin/Settings.php:206, ../includes/Admin/Settings.php:343
msgid "Record not found!"
msgstr ""

#: ../includes/Admin/Settings.php:329, ../includes/Admin/Settings.php:334, ../includes/Admin/Settings.php:362, ../includes/Admin/Settings.php:367, ../includes/Admin/Settings.php:403, ../includes/Admin/Settings.php:408, ../includes/Admin/Settings.php:671, ../includes/Admin/Settings.php:676, ../includes/Classes/Export.php:38, ../includes/Classes/Export.php:43
msgid "Access Denied!"
msgstr ""

#: ../includes/Admin/Settings.php:388
msgid "Something went wrong!"
msgstr ""

#: ../includes/Admin/Settings.php:374
msgid "Deleted Successfully!"
msgstr ""

#: ../includes/Admin/Settings.php:689
msgid "Failed to mark transaction as completed!"
msgstr ""

#: ../includes/Admin/Settings.php:686
msgid "Transaction marked as completed!"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:102, ../includes/Admin/Setup_Wizard.php:103
msgid "Better Payment "
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:135
msgid "Getting Started"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:147
msgid "PayPal Configuration"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:159
msgid "Stripe Configuration"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:169
msgid "Finalize"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:227, ../includes/Admin/views/better-payment-settings.php:76, ../includes/Admin/views/better-payment-settings.php:87, ../includes/Admin/views/better-payment-transaction-list.php:276, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:32, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:44, ../includes/Admin/views/elementor/layouts/layout-1.php:70, ../includes/Admin/views/elementor/layouts/layout-2.php:38
msgid "PayPal"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:228, ../includes/Admin/views/better-payment-settings.php:89
msgid "Enable PayPal if you want to make transaction using PayPal."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:240, ../includes/Admin/views/better-payment-settings.php:77, ../includes/Admin/views/better-payment-settings.php:103, ../includes/Admin/views/better-payment-transaction-list.php:277, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:29, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:45, ../includes/Admin/views/elementor/layouts/layout-1.php:84, ../includes/Admin/views/elementor/layouts/layout-2.php:52
msgid "Stripe"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:241, ../includes/Admin/views/better-payment-settings.php:105
msgid "Enable Stripe if you want to accept payment via Stripe."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:253, ../includes/Admin/views/better-payment-settings.php:152
msgid "Email Notification"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:254, ../includes/Admin/views/better-payment-settings.php:153
msgid "Enable email notification for each transaction. It sends notification to the website admin and customer (who makes the payment). You can modify email settings as per your need."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:266, ../includes/Classes/Handler.php:590, ../includes/Admin/Elementor/Better_Payment_Widget.php:624, ../includes/Admin/Elementor/Better_Payment_Widget.php:2262, ../includes/Admin/Elementor/Better_Payment_Widget.php:2264, ../includes/Admin/Elementor/Better_Payment_Widget.php:2265, ../includes/Admin/views/better-payment-settings.php:165, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:57, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:58
msgid "Currency"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:267, ../includes/Admin/views/better-payment-settings.php:166
msgid "Select default currency for each transaction. You can also overwrite this setting from each widget control on elementor page builder."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:272, ../includes/Admin/views/better-payment-settings.php:171
msgid "Select Currency"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:288, ../includes/Admin/Setup_Wizard.php:315, ../includes/Admin/Elementor/Better_Payment_Widget.php:1613, ../includes/Admin/Elementor/Better_Payment_Widget.php:1717, ../includes/Admin/Elementor/Better_Payment_Widget.php:1783, ../includes/Admin/views/better-payment-settings.php:339, ../includes/Admin/views/better-payment-settings.php:418, ../includes/Admin/views/better-payment-settings.php:487, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:115, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:107, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:121
msgid "Live Mode"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:289, ../includes/Admin/views/better-payment-settings.php:340
msgid "Live mode allows you to process real transactions. It just requires PayPal business email to accept real payments."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:301, ../includes/Admin/Elementor/Better_Payment_Widget.php:1583, ../includes/Admin/views/better-payment-settings.php:352, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:85
msgid "Business Email"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:302, ../includes/Admin/views/better-payment-settings.php:353
msgid "Your PayPal account email address to accept payment via PayPal."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:316, ../includes/Admin/views/better-payment-settings.php:419
msgid "Live mode allows you to process real transactions. It just requires live Stripe keys (public and secret keys) to accept real payments."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:331, ../includes/Admin/Elementor/Better_Payment_Widget.php:1678, ../includes/Admin/views/better-payment-settings.php:434, ../includes/Admin/views/better-payment-settings.php:503
msgid "Live Public Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:332, ../includes/Admin/views/better-payment-settings.php:436
msgid "Stripe live public key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:340, ../includes/Admin/Elementor/Better_Payment_Widget.php:1697, ../includes/Admin/views/better-payment-settings.php:446, ../includes/Admin/views/better-payment-settings.php:515
msgid "Live Secret Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:341, ../includes/Admin/views/better-payment-settings.php:448
msgid "Stripe live secret key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:349, ../includes/Admin/Elementor/Better_Payment_Widget.php:1639, ../includes/Admin/views/better-payment-settings.php:458, ../includes/Admin/views/better-payment-settings.php:527
msgid "Test Public Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:350, ../includes/Admin/views/better-payment-settings.php:460
msgid "Stripe test public key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:358, ../includes/Admin/Elementor/Better_Payment_Widget.php:1658, ../includes/Admin/views/better-payment-settings.php:470, ../includes/Admin/views/better-payment-settings.php:539
msgid "Test Secret Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:359, ../includes/Admin/views/better-payment-settings.php:472
msgid "Stripe test secret key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:380
msgid "Great Job! Your Configuration is Complete "
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:382
msgid "Share non-sensitive diagnostic data and plugin usage information."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:384
msgid "What do we collect? We collect non-sensitive diagnostic data and plugin usage information. Your site URL, WordPress & PHP version, plugins & themes and email address to send you the discount coupon. This data lets us make sure this plugin always stays compatible with the most popular plugins and themes. No spam, we promise."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:400
msgid "< Previous"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:401
msgid "Next >"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:402
msgid "Finish"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:518
msgid "Quick Setup Wizard - Better Payment"
msgstr ""

#: ../includes/Classes/Actions.php:166, ../includes/Classes/Actions.php:388
msgid "Page ID is missing"
msgstr ""

#: ../includes/Classes/Actions.php:173, ../includes/Classes/Actions.php:392
msgid "Widget ID is missing"
msgstr ""

#: ../includes/Classes/Actions.php:186, ../includes/Classes/Actions.php:405
msgid "Setting Data is missing"
msgstr ""

#: ../includes/Classes/Actions.php:206
msgid "Stripe Key missing"
msgstr ""

#: ../includes/Classes/Actions.php:409
msgid "Paystack Key missing"
msgstr ""

#: ../includes/Classes/Handler.php:228, ../includes/Classes/Handler.php:380, ../includes/Classes/Handler.php:469
msgid "USD"
msgstr ""

#: ../includes/Classes/Handler.php:264
msgid "Payment under processing!"
msgstr ""

#: ../includes/Classes/Handler.php:319, ../includes/Classes/Handler.php:340
msgid "There was a problem connecting to the Stripe API endpoint."
msgstr ""

#: ../includes/Classes/Handler.php:548, ../includes/Admin/Elementor/Better_Payment_Widget.php:2193, ../includes/Admin/Elementor/Better_Payment_Widget.php:2194, ../includes/Admin/Elementor/Better_Payment_Widget.php:2195
msgid "You paid"
msgstr ""

#: ../includes/Classes/Handler.php:548
msgid " to "
msgstr ""

#: ../includes/Classes/Handler.php:558
msgid "Payment Confirmation email will be sent to "
msgstr ""

#: ../includes/Classes/Handler.php:581, ../includes/Admin/views/template-email-notification.php:173
msgid "Thank You!"
msgstr ""

#: ../includes/Classes/Handler.php:584, ../includes/Admin/Elementor/Better_Payment_Widget.php:2213, ../includes/Admin/Elementor/Better_Payment_Widget.php:2214, ../includes/Admin/Elementor/User_Dashboard.php:1907, ../includes/Admin/Elementor/User_Dashboard.php:2030, ../includes/Admin/Elementor/User_Dashboard.php:2033, ../includes/Admin/views/template-transaction-list.php:44
msgid "Transaction ID"
msgstr ""

#: ../includes/Classes/Handler.php:587, ../includes/Admin/Elementor/Better_Payment_Widget.php:1213, ../includes/Admin/Elementor/Better_Payment_Widget.php:2245, ../includes/Admin/Elementor/Better_Payment_Widget.php:2247, ../includes/Admin/Elementor/Better_Payment_Widget.php:2248, ../includes/Admin/Elementor/User_Dashboard.php:1883, ../includes/Admin/Elementor/User_Dashboard.php:2004, ../includes/Admin/Elementor/User_Dashboard.php:2007, ../includes/Admin/views/better-payment-transaction-list.php:229, ../includes/Admin/views/template-email-notification.php:328, ../includes/Admin/views/template-transaction-list.php:38
msgid "Amount"
msgstr ""

#: ../includes/Classes/Handler.php:591, ../includes/Admin/Elementor/Better_Payment_Widget.php:2279, ../includes/Admin/Elementor/Better_Payment_Widget.php:2281, ../includes/Admin/Elementor/Better_Payment_Widget.php:2282, ../includes/Admin/Elementor/Better_Payment_Widget.php:3279
msgid "Payment Method"
msgstr ""

#: ../includes/Classes/Handler.php:594, ../includes/Admin/Elementor/Better_Payment_Widget.php:116, ../includes/Admin/Elementor/Better_Payment_Widget.php:2296, ../includes/Admin/Elementor/Better_Payment_Widget.php:2298, ../includes/Admin/Elementor/Better_Payment_Widget.php:2299, ../includes/Admin/Elementor/User_Dashboard.php:1895, ../includes/Admin/Elementor/User_Dashboard.php:2017, ../includes/Admin/Elementor/User_Dashboard.php:2020, ../includes/Admin/views/template-transaction-list.php:41
msgid "Payment Type"
msgstr ""

#: ../includes/Classes/Handler.php:604
msgid "Split Payment"
msgstr ""

#: ../includes/Classes/Handler.php:602
msgid "Recurring Payment"
msgstr ""

#: ../includes/Classes/Handler.php:600
msgid "One Time Payment"
msgstr ""

#: ../includes/Classes/Handler.php:609, ../includes/Admin/Elementor/Better_Payment_Widget.php:2313, ../includes/Admin/Elementor/Better_Payment_Widget.php:2315, ../includes/Admin/Elementor/Better_Payment_Widget.php:2316
msgid "Merchant Details"
msgstr ""

#: ../includes/Classes/Handler.php:615, ../includes/Admin/Elementor/Better_Payment_Widget.php:2330, ../includes/Admin/Elementor/Better_Payment_Widget.php:2332, ../includes/Admin/Elementor/Better_Payment_Widget.php:2333
msgid "Paid Amount"
msgstr ""

#: ../includes/Classes/Handler.php:618, ../includes/Admin/Elementor/Better_Payment_Widget.php:2347, ../includes/Admin/Elementor/Better_Payment_Widget.php:2349, ../includes/Admin/Elementor/Better_Payment_Widget.php:2350
msgid "Purchase Details"
msgstr ""

#: ../includes/Classes/Handler.php:621, ../includes/Admin/Elementor/Better_Payment_Widget.php:2364, ../includes/Admin/Elementor/Better_Payment_Widget.php:2366, ../includes/Admin/Elementor/Better_Payment_Widget.php:2367
msgid "Print"
msgstr ""

#: ../includes/Classes/Handler.php:624, ../includes/Admin/Elementor/Better_Payment_Widget.php:2381, ../includes/Admin/Elementor/Better_Payment_Widget.php:2383, ../includes/Admin/Elementor/Better_Payment_Widget.php:2384
msgid "View Details"
msgstr ""

#: ../includes/Classes/Handler.php:806, ../includes/Admin/Elementor/Better_Payment_Widget.php:2453
msgid "Payment Failed"
msgstr ""

#: ../includes/Classes/Handler.php:850, ../includes/Admin/Elementor/Better_Payment_Widget.php:1838, ../includes/Admin/Elementor/Better_Payment_Widget.php:2005
msgid "Better Payment transaction on %s"
msgstr ""

#: ../includes/Classes/Handler.php:990, ../includes/Classes/Handler.php:994
msgid "New better payment transaction! "
msgstr ""

#: ../includes/Classes/Handler.php:1123
msgid "Field"
msgstr ""

#: ../includes/Classes/Handler.php:1124
msgid "Entry"
msgstr ""

#: ../includes/Classes/Handler.php:1177, ../includes/Admin/views/template-email-notification.php:252
msgid "Paid"
msgstr ""

#: ../includes/Classes/Handler.php:1180
msgid "Product Price: "
msgstr ""

#: ../includes/Classes/Helper.php:51
msgid "%1$sBetter Payment%2$s requires %1$sElementor%2$s plugin to be installed and activated. Please install Elementor to continue."
msgstr ""

#: ../includes/Classes/Helper.php:52
msgid "Install Elementor"
msgstr ""

#: ../includes/Classes/Helper.php:45
msgid "%1$sBetter Payment%2$s requires %1$sElementor%2$s plugin to be active. Please activate Elementor to continue."
msgstr ""

#: ../includes/Classes/Helper.php:47
msgid "Activate Elementor"
msgstr ""

#: ../includes/Classes/Import.php:38, ../includes/Classes/Import.php:137
msgid "Invalid File!"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:416
msgid "We can't detect any plugin information. This is most probably because you have not included the code in the plugin main file."
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:778
msgid "Sorry to see you go"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:779
msgid "Before you deactivate the plugin, would you quickly give us your reason for doing so?"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:782
msgid "I no longer need the plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:784
msgid "I found a better plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:785
msgid "Please share which plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:787
msgid "I couldn't get the plugin to work"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:788
msgid "It's a temporary deactivation"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:790
msgid "Other"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:791
msgid "Please share the reason"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:834
msgid "Submitting form"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:892
msgid "Submit and Deactivate"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:892
msgid "Just Deactivate"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:97
msgid "Payment Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:106
msgid "Form Layout"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:117
msgid "Recurring and Split Payment is available for Stripe only at the moment!"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:134
msgid "Default Price ID"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:137
msgid "<p>Create a product from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the (default) price id.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:158
msgid "Installment Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:171, ../includes/Admin/Elementor/Better_Payment_Widget.php:265
msgid "Price ID"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:184
msgid "Iterations"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:196
msgid "<p>Now add more prices to the product from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the price id for each installment.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:213
msgid "Installments"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:252
msgid "Interval Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:278
msgid "Intervals"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:303
msgid "Webhook Secret"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:306
msgid "<p>Create a webhook endpoint from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the webhook secret.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:326
msgid "<p><a href=\"%1$s\" target=\"_blank\">Your webhook endpoint url »</a><br>%2$s</p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:344
msgid "Payment Source"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:359
msgid "<a href=\"%1$s\" target=\"_blank\"><strong>WooCommerce</strong></a> is not installed/activated on your site. Please install and activate <a href=\"%1$s\" target=\"_blank\"><strong>WooCommerce</strong></a> first."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:394
msgid "Product"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:403
msgid "Choose a Product"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:404
msgid "Enter Product IDs separated by a comma"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:410
msgid "Search By"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:419
msgid "Select Products"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:433
msgid "Enable PayPal"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:435, ../includes/Admin/Elementor/Better_Payment_Widget.php:461, ../includes/Admin/Elementor/Better_Payment_Widget.php:538, ../includes/Admin/Elementor/Better_Payment_Widget.php:581, ../includes/Admin/Elementor/Better_Payment_Widget.php:593, ../includes/Admin/Elementor/Better_Payment_Widget.php:613, ../includes/Admin/Elementor/Better_Payment_Widget.php:969, ../includes/Admin/Elementor/Better_Payment_Widget.php:996, ../includes/Admin/Elementor/Better_Payment_Widget.php:1045, ../includes/Admin/Elementor/Better_Payment_Widget.php:1078, ../includes/Admin/Elementor/Better_Payment_Widget.php:1092, ../includes/Admin/Elementor/Better_Payment_Widget.php:1330, ../includes/Admin/Elementor/Better_Payment_Widget.php:1346, ../includes/Admin/Elementor/Better_Payment_Widget.php:1615, ../includes/Admin/Elementor/Better_Payment_Widget.php:1719, ../includes/Admin/Elementor/Better_Payment_Widget.php:1785, ../includes/Admin/Elementor/Better_Payment_Widget.php:1865, ../includes/Admin/Elementor/Better_Payment_Widget.php:1877, ../includes/Admin/Elementor/Better_Payment_Widget.php:1889, ../includes/Admin/Elementor/Better_Payment_Widget.php:1901, ../includes/Admin/Elementor/Better_Payment_Widget.php:1913, ../includes/Admin/Elementor/Better_Payment_Widget.php:1925, ../includes/Admin/Elementor/Better_Payment_Widget.php:2033, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:80, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:119, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:176, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:283, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:117, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:109, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:123
msgid "Yes"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:436, ../includes/Admin/Elementor/Better_Payment_Widget.php:462, ../includes/Admin/Elementor/Better_Payment_Widget.php:539, ../includes/Admin/Elementor/Better_Payment_Widget.php:582, ../includes/Admin/Elementor/Better_Payment_Widget.php:594, ../includes/Admin/Elementor/Better_Payment_Widget.php:614, ../includes/Admin/Elementor/Better_Payment_Widget.php:970, ../includes/Admin/Elementor/Better_Payment_Widget.php:997, ../includes/Admin/Elementor/Better_Payment_Widget.php:1046, ../includes/Admin/Elementor/Better_Payment_Widget.php:1079, ../includes/Admin/Elementor/Better_Payment_Widget.php:1093, ../includes/Admin/Elementor/Better_Payment_Widget.php:1331, ../includes/Admin/Elementor/Better_Payment_Widget.php:1347, ../includes/Admin/Elementor/Better_Payment_Widget.php:1616, ../includes/Admin/Elementor/Better_Payment_Widget.php:1720, ../includes/Admin/Elementor/Better_Payment_Widget.php:1786, ../includes/Admin/Elementor/Better_Payment_Widget.php:1866, ../includes/Admin/Elementor/Better_Payment_Widget.php:1878, ../includes/Admin/Elementor/Better_Payment_Widget.php:1890, ../includes/Admin/Elementor/Better_Payment_Widget.php:1902, ../includes/Admin/Elementor/Better_Payment_Widget.php:1914, ../includes/Admin/Elementor/Better_Payment_Widget.php:1926, ../includes/Admin/Elementor/Better_Payment_Widget.php:2034, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:81, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:120, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:177, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:284, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:118, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:110, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:124
msgid "No"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:448
msgid "Whoops! It seems like you haven't configured <b>PayPal (Business Email) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:459
msgid "Enable Stripe"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:470, ../includes/Admin/Elementor/Better_Payment_Widget.php:503
msgid "Whoops! It seems like you haven't configured <b>Stripe (Public and Secret Key) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:536
msgid "Enable Paystack"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:550
msgid "Whoops! It seems like you haven't configured <b>Paystack (Public and Secret Key) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:579
msgid "Enable Email Notification"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:591
msgid "Show Sidebar"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:611
msgid "Use WooCommerce Currency?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:723
msgid "WooCommerce Currency"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:741
msgid "Supported by %1$sStripe%2$s (and/or %1$sPaystack%2$s) only"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:783, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:67, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:68
msgid "Currency Alignment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:787, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:71, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:72
msgid "Left"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:791, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:75, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:76
msgid "Right"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:845
msgid "Form Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:869, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:102
msgid "Form Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:888, ../includes/Admin/Elementor/Better_Payment_Widget.php:890, ../includes/Admin/Elementor/Better_Payment_Widget.php:903
msgid "Field Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:901
msgid "Placeholder Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:914, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:243
msgid "Field Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:918
msgid "Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:919, ../includes/Admin/Elementor/Better_Payment_Widget.php:946, ../includes/Admin/Elementor/Better_Payment_Widget.php:1204, ../includes/Admin/views/better-payment-settings.php:67
msgid "Email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:920
msgid "Number"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:939
msgid "Primary Field Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:940
msgid "If this is a primary field (first name, last name, email etc), then please select one."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:944, ../includes/Admin/Elementor/Better_Payment_Widget.php:1140, ../includes/Admin/Elementor/Better_Payment_Widget.php:1141
msgid "First Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:945, ../includes/Admin/Elementor/Better_Payment_Widget.php:1149, ../includes/Admin/Elementor/Better_Payment_Widget.php:1150
msgid "Last Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:947, ../includes/Admin/Elementor/Better_Payment_Widget.php:1167, ../includes/Admin/Elementor/Better_Payment_Widget.php:1168, ../includes/Admin/Elementor/Better_Payment_Widget.php:1319, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:29, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:93
msgid "Payment Amount"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:948
msgid "None"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:957, ../includes/Admin/Elementor/Better_Payment_Widget.php:2175, ../includes/Admin/Elementor/Better_Payment_Widget.php:2442, ../includes/Admin/Elementor/Better_Payment_Widget.php:2883, ../includes/Admin/Elementor/User_Dashboard.php:643
msgid "Icon"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:958
msgid "Select an icon for this field (not applicable for primary field - Payment Amount and layout 4, 5, 6)."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:967, ../includes/Admin/Elementor/User_Dashboard.php:117, ../includes/Admin/Elementor/User_Dashboard.php:129, ../includes/Admin/Elementor/User_Dashboard.php:144, ../includes/Admin/Elementor/User_Dashboard.php:159, ../includes/Admin/Elementor/User_Dashboard.php:171, ../includes/Admin/Elementor/User_Dashboard.php:183, ../includes/Admin/Elementor/User_Dashboard.php:195, ../includes/Admin/Elementor/User_Dashboard.php:1662, ../includes/Admin/Elementor/User_Dashboard.php:1674, ../includes/Admin/Elementor/User_Dashboard.php:1686, ../includes/Admin/Elementor/User_Dashboard.php:1698, ../includes/Admin/Elementor/User_Dashboard.php:1710, ../includes/Admin/Elementor/User_Dashboard.php:1861, ../includes/Admin/Elementor/User_Dashboard.php:1873, ../includes/Admin/Elementor/User_Dashboard.php:1885, ../includes/Admin/Elementor/User_Dashboard.php:1897, ../includes/Admin/Elementor/User_Dashboard.php:1909, ../includes/Admin/Elementor/User_Dashboard.php:1921, ../includes/Admin/Elementor/User_Dashboard.php:1933, ../includes/Admin/Elementor/User_Dashboard.php:1945
msgid "Show"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:982
msgid "Field is hidden if payment source is WooCommerce or payment type is recurring/split payment or field dynamic value is enabled."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:994
msgid "Required"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1009, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:284
msgid "Min. Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1020, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:295
msgid "Max. Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1031, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:306
msgid "Default Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1042
msgid "Dynamic Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1044
msgid "It will override default value!"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1059
msgid "<p class=\"better-payment-dynamic-value-info\" style=\"word-break: break-word;\"><a href=\"%1$s\" target=\"_blank\">Sample url »</a><br>%1$s</p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1076, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:317
msgid "Readonly"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1090
msgid "Display Inline?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1110
msgid "Column Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1135
msgid "Set the width of the column. Use less than 50% to make fields inline"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1158, ../includes/Admin/Elementor/Better_Payment_Widget.php:1159, ../includes/Admin/Elementor/User_Dashboard.php:1871, ../includes/Admin/Elementor/User_Dashboard.php:1991, ../includes/Admin/Elementor/User_Dashboard.php:1994, ../includes/Admin/views/better-payment-transaction-list.php:228, ../includes/Admin/views/template-transaction-list.php:35
msgid "Email Address"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1176, ../includes/Admin/Elementor/Better_Payment_Widget.php:1177
msgid "First name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1190, ../includes/Admin/Elementor/Better_Payment_Widget.php:1191
msgid "Last name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1205
msgid "Enter your email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1214, ../includes/Admin/Elementor/Better_Payment_Widget.php:1223
msgid "Other amount"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1222
msgid "Amount to pay"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1260, ../includes/Admin/Elementor/Better_Payment_Widget.php:1274, ../includes/Admin/Elementor/Better_Payment_Widget.php:1288, ../includes/Admin/Elementor/Better_Payment_Widget.php:1302
msgid "Form Fields"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1328, ../includes/Admin/Elementor/Better_Payment_Widget.php:1344, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:117
msgid "Show Amount List"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1360, ../includes/Admin/Elementor/Better_Payment_Widget.php:1390, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:248, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:146
msgid "Amount List"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1424, ../includes/Admin/Elementor/Better_Payment_Widget.php:1435
msgid "Transaction Details"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1433
msgid "Heading Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1446
msgid "Sub Heading Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1448
msgid "Total payment of your product in the following:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1459
msgid "Product Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1461
msgid "Title:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1475, ../includes/Admin/Elementor/Better_Payment_Widget.php:2731
msgid "Amount Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1477, ../includes/Admin/views/better-payment-transaction-view.php:130
msgid "Amount:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1493
msgid "Form Custom Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1502
msgid "Form Title"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1517
msgid "Form Sub-Title"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1532
msgid "PayPal Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1544
msgid "Stripe Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1556
msgid "Paystack Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1572
msgid "PayPal Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1599, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:101
msgid "Button Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1629
msgid "Stripe Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1733
msgid "Paystack Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1747, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:74, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:88
msgid "Public Key"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1766, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:90, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:104
msgid "Secret Key"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1799
msgid "Email Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1809
msgid "Choose Logo"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1822
msgid "Admin"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1826, ../includes/Admin/views/better-payment-settings.php:185, ../includes/Admin/views/better-payment-settings.php:262, ../includes/Admin/views/template-email-notification.php:270
msgid "To"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1831
msgid "Email address to notify site admin after each successful transaction"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1841, ../includes/Admin/Elementor/Better_Payment_Widget.php:2008, ../includes/Admin/views/better-payment-settings.php:192, ../includes/Admin/views/better-payment-settings.php:268
msgid "Subject"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1853, ../includes/Admin/Elementor/Better_Payment_Widget.php:2020, ../includes/Admin/views/better-payment-settings.php:199, ../includes/Admin/views/better-payment-settings.php:275
msgid "Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1863
msgid "Show Greeting Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1875
msgid "Show Header Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1887
msgid "Show From Section"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1899
msgid "Show To Section"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1911
msgid "Show Transaction Summary"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1923
msgid "Show Footer Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1936, ../includes/Admin/Elementor/Better_Payment_Widget.php:2078
msgid "From email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1947, ../includes/Admin/Elementor/Better_Payment_Widget.php:2089
msgid "From name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1957, ../includes/Admin/Elementor/Better_Payment_Widget.php:2099, ../includes/Admin/views/better-payment-settings.php:223, ../includes/Admin/views/better-payment-settings.php:299
msgid "Reply-To"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1968, ../includes/Admin/Elementor/Better_Payment_Widget.php:2110, ../includes/Admin/views/better-payment-settings.php:230, ../includes/Admin/views/better-payment-settings.php:306
msgid "Cc"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1978, ../includes/Admin/Elementor/Better_Payment_Widget.php:2120, ../includes/Admin/views/better-payment-settings.php:237, ../includes/Admin/views/better-payment-settings.php:313
msgid "Bcc"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1988, ../includes/Admin/Elementor/Better_Payment_Widget.php:2130
msgid "Send as"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1993, ../includes/Admin/Elementor/Better_Payment_Widget.php:2135
msgid "HTML"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1994, ../includes/Admin/Elementor/Better_Payment_Widget.php:2136, ../includes/Admin/views/better-payment-settings.php:249, ../includes/Admin/views/better-payment-settings.php:325
msgid "Plain"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2002
msgid "Customer"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2030
msgid "PDF Attachment?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2042
msgid "Attachment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2043
msgid "Allowed file types: jpg, jpeg, png"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2058
msgid "PDF"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2059
msgid "Allowed file types: pdf"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2151
msgid "Success Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2184, ../includes/Admin/Elementor/User_Dashboard.php:213
msgid "Content"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2191
msgid "Heading"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2193, ../includes/Admin/Elementor/Better_Payment_Widget.php:2194, ../includes/Admin/Elementor/Better_Payment_Widget.php:2195
msgid "to"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2195
msgid "Use shortcode like"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2195
msgid "to customize your message."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2195
msgid "eg:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2195
msgid "for your order."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2200
msgid "Sub Heading"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2202, ../includes/Admin/Elementor/Better_Payment_Widget.php:2203, ../includes/Admin/Elementor/Better_Payment_Widget.php:2204
msgid "Payment confirmation email will be sent to"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2211
msgid "Transaction"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2228
msgid "Thank You"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2230, ../includes/Admin/Elementor/Better_Payment_Widget.php:2231
msgid "Thank you for your payment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2397
msgid "User Dashboard URL"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2399, ../includes/Admin/Elementor/Better_Payment_Widget.php:2407, ../includes/Admin/Elementor/Better_Payment_Widget.php:2468
msgid "eg. https://example.com/custom-page/"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2400
msgid "Please enter the page url where <strong>User Dashboard</strong> widget is used."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2405, ../includes/Admin/Elementor/Better_Payment_Widget.php:2466
msgid "Custom Redirect URL"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2408, ../includes/Admin/Elementor/Better_Payment_Widget.php:2469
msgid "Please note that only your current domain is allowed here to keep your site secure."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2418
msgid "Error Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2451
msgid "Heading Message Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2489
msgid "Form Sidebar Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2506, ../includes/Admin/Elementor/Better_Payment_Widget.php:2625, ../includes/Admin/Elementor/Better_Payment_Widget.php:2699, ../includes/Admin/Elementor/Better_Payment_Widget.php:2773, ../includes/Admin/Elementor/Better_Payment_Widget.php:2849, ../includes/Admin/Elementor/Better_Payment_Widget.php:2935, ../includes/Admin/Elementor/Better_Payment_Widget.php:2986, ../includes/Admin/Elementor/User_Dashboard.php:308, ../includes/Admin/Elementor/User_Dashboard.php:446, ../includes/Admin/Elementor/User_Dashboard.php:694, ../includes/Admin/Elementor/User_Dashboard.php:857, ../includes/Admin/Elementor/User_Dashboard.php:978, ../includes/Admin/Elementor/User_Dashboard.php:1131
msgid "Margin"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2522, ../includes/Admin/Elementor/Better_Payment_Widget.php:2642, ../includes/Admin/Elementor/Better_Payment_Widget.php:2715, ../includes/Admin/Elementor/Better_Payment_Widget.php:2790, ../includes/Admin/Elementor/Better_Payment_Widget.php:2866, ../includes/Admin/Elementor/Better_Payment_Widget.php:2950, ../includes/Admin/Elementor/Better_Payment_Widget.php:3001, ../includes/Admin/Elementor/Better_Payment_Widget.php:3132, ../includes/Admin/Elementor/Better_Payment_Widget.php:3752, ../includes/Admin/Elementor/User_Dashboard.php:320, ../includes/Admin/Elementor/User_Dashboard.php:458, ../includes/Admin/Elementor/User_Dashboard.php:706, ../includes/Admin/Elementor/User_Dashboard.php:869, ../includes/Admin/Elementor/User_Dashboard.php:990, ../includes/Admin/Elementor/User_Dashboard.php:1143
msgid "Padding"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2537, ../includes/Admin/Elementor/Better_Payment_Widget.php:3016, ../includes/Admin/Elementor/Better_Payment_Widget.php:3246, ../includes/Admin/Elementor/Better_Payment_Widget.php:3621, ../includes/Admin/Elementor/Better_Payment_Widget.php:3737, ../includes/Admin/Elementor/User_Dashboard.php:333, ../includes/Admin/Elementor/User_Dashboard.php:482, ../includes/Admin/Elementor/User_Dashboard.php:719, ../includes/Admin/Elementor/User_Dashboard.php:882, ../includes/Admin/Elementor/User_Dashboard.php:1003, ../includes/Admin/Elementor/User_Dashboard.php:1156, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:266, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:471
msgid "Border Radius"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2573
msgid "Sidebar Text Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2584
msgid "Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2595, ../includes/Admin/Elementor/Better_Payment_Widget.php:2671, ../includes/Admin/Elementor/Better_Payment_Widget.php:2743, ../includes/Admin/Elementor/Better_Payment_Widget.php:2819, ../includes/Admin/Elementor/Better_Payment_Widget.php:2895, ../includes/Admin/Elementor/Better_Payment_Widget.php:3355, ../includes/Admin/Elementor/User_Dashboard.php:671, ../includes/Admin/Elementor/User_Dashboard.php:749, ../includes/Admin/Elementor/User_Dashboard.php:793, ../includes/Admin/Elementor/User_Dashboard.php:1033, ../includes/Admin/Elementor/User_Dashboard.php:1078, ../includes/Admin/Elementor/User_Dashboard.php:1186, ../includes/Admin/Elementor/User_Dashboard.php:1233, ../includes/Admin/Elementor/User_Dashboard.php:1299, ../includes/Admin/Elementor/User_Dashboard.php:1327, ../includes/Admin/Elementor/User_Dashboard.php:1367, ../includes/Admin/Elementor/User_Dashboard.php:1395, ../includes/Admin/Elementor/User_Dashboard.php:1435, ../includes/Admin/Elementor/User_Dashboard.php:1463
msgid "Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2613, ../includes/Admin/Elementor/Better_Payment_Widget.php:2688, ../includes/Admin/Elementor/Better_Payment_Widget.php:2762, ../includes/Admin/Elementor/Better_Payment_Widget.php:2838, ../includes/Admin/Elementor/Better_Payment_Widget.php:3263, ../includes/Admin/Elementor/Better_Payment_Widget.php:3637, ../includes/Admin/Elementor/Better_Payment_Widget.php:3790, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:487
msgid "Typography"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2659
msgid "Sub-Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2807
msgid "Amount Summary"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2910
msgid "Font Size"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2969
msgid "Form Container Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3052
msgid "Form Fields Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3060, ../includes/Admin/Elementor/Better_Payment_Widget.php:3536, ../includes/Admin/Elementor/Better_Payment_Widget.php:3575, ../includes/Admin/Elementor/Better_Payment_Widget.php:3697, ../includes/Admin/Elementor/Better_Payment_Widget.php:3816, ../includes/Admin/Elementor/User_Dashboard.php:358, ../includes/Admin/Elementor/User_Dashboard.php:391, ../includes/Admin/Elementor/User_Dashboard.php:471, ../includes/Admin/Elementor/User_Dashboard.php:760, ../includes/Admin/Elementor/User_Dashboard.php:804, ../includes/Admin/Elementor/User_Dashboard.php:904, ../includes/Admin/Elementor/User_Dashboard.php:937, ../includes/Admin/Elementor/User_Dashboard.php:1045, ../includes/Admin/Elementor/User_Dashboard.php:1090, ../includes/Admin/Elementor/User_Dashboard.php:1200, ../includes/Admin/Elementor/User_Dashboard.php:1245, ../includes/Admin/Elementor/User_Dashboard.php:1310, ../includes/Admin/Elementor/User_Dashboard.php:1338, ../includes/Admin/Elementor/User_Dashboard.php:1378, ../includes/Admin/Elementor/User_Dashboard.php:1406, ../includes/Admin/Elementor/User_Dashboard.php:1446, ../includes/Admin/Elementor/User_Dashboard.php:1474, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:221, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:380, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:422
msgid "Background Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3075, ../includes/Admin/Elementor/Better_Payment_Widget.php:3551, ../includes/Admin/Elementor/Better_Payment_Widget.php:3590, ../includes/Admin/Elementor/Better_Payment_Widget.php:3712, ../includes/Admin/Elementor/Better_Payment_Widget.php:3832, ../includes/Admin/Elementor/User_Dashboard.php:556, ../includes/Admin/Elementor/User_Dashboard.php:573, ../includes/Admin/Elementor/User_Dashboard.php:611, ../includes/Admin/Elementor/User_Dashboard.php:628, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:190, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:395, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:437
msgid "Text Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3091
msgid "Placeholder Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3106, ../includes/Admin/Elementor/Better_Payment_Widget.php:3501, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:342
msgid "Spacing"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3147
msgid "Text Indent"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3174, ../includes/Admin/Elementor/Better_Payment_Widget.php:3457, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:298
msgid "Input Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3184
msgid "Set width for all input fields. Not applicable if the field is set to display inline (<b>Content => Form Settings => Form Fields (Repeater) => Display Inline?</b>)"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3205, ../includes/Admin/Elementor/Better_Payment_Widget.php:3479, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:320
msgid "Input Height"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3236, ../includes/Admin/Elementor/Better_Payment_Widget.php:3304, ../includes/Admin/Elementor/Better_Payment_Widget.php:3328, ../includes/Admin/Elementor/Better_Payment_Widget.php:3610, ../includes/Admin/Elementor/Better_Payment_Widget.php:3728, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:457
msgid "Border"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3293
msgid "Active"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3317
msgid "Inactive"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3343
msgid "Input Icon"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3374, ../includes/Admin/Elementor/User_Dashboard.php:523, ../includes/Admin/Elementor/User_Dashboard.php:652
msgid "Size"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3398, ../includes/Admin/Elementor/Better_Payment_Widget.php:3666
msgid "Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3421
msgid "Height"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3449
msgid "Amount Fields Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3529, ../includes/Admin/Elementor/Better_Payment_Widget.php:3690, ../includes/Admin/Elementor/User_Dashboard.php:352, ../includes/Admin/Elementor/User_Dashboard.php:550, ../includes/Admin/Elementor/User_Dashboard.php:605, ../includes/Admin/Elementor/User_Dashboard.php:743, ../includes/Admin/Elementor/User_Dashboard.php:898, ../includes/Admin/Elementor/User_Dashboard.php:1027, ../includes/Admin/Elementor/User_Dashboard.php:1180, ../includes/Admin/Elementor/User_Dashboard.php:1293, ../includes/Admin/Elementor/User_Dashboard.php:1361, ../includes/Admin/Elementor/User_Dashboard.php:1429, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:370
msgid "Normal"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3568, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:412
msgid "Selected"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3658
msgid "Form Button Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3767
msgid "Margin Top"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3809, ../includes/Admin/Elementor/User_Dashboard.php:385, ../includes/Admin/Elementor/User_Dashboard.php:567, ../includes/Admin/Elementor/User_Dashboard.php:622, ../includes/Admin/Elementor/User_Dashboard.php:787, ../includes/Admin/Elementor/User_Dashboard.php:931, ../includes/Admin/Elementor/User_Dashboard.php:1072, ../includes/Admin/Elementor/User_Dashboard.php:1227, ../includes/Admin/Elementor/User_Dashboard.php:1321, ../includes/Admin/Elementor/User_Dashboard.php:1389, ../includes/Admin/Elementor/User_Dashboard.php:1457
msgid "Hover"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3848, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:236
msgid "Border Color"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:58, ../includes/Admin/views/better-payment-settings.php:136
msgid "User Dashboard"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:98, ../includes/Admin/Elementor/User_Dashboard.php:105
msgid "Layout"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:115, ../includes/Admin/Elementor/User_Dashboard.php:426
msgid "Sidebar"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:118, ../includes/Admin/Elementor/User_Dashboard.php:130, ../includes/Admin/Elementor/User_Dashboard.php:145, ../includes/Admin/Elementor/User_Dashboard.php:160, ../includes/Admin/Elementor/User_Dashboard.php:172, ../includes/Admin/Elementor/User_Dashboard.php:184, ../includes/Admin/Elementor/User_Dashboard.php:196, ../includes/Admin/Elementor/User_Dashboard.php:1663, ../includes/Admin/Elementor/User_Dashboard.php:1675, ../includes/Admin/Elementor/User_Dashboard.php:1687, ../includes/Admin/Elementor/User_Dashboard.php:1699, ../includes/Admin/Elementor/User_Dashboard.php:1711, ../includes/Admin/Elementor/User_Dashboard.php:1862, ../includes/Admin/Elementor/User_Dashboard.php:1874, ../includes/Admin/Elementor/User_Dashboard.php:1886, ../includes/Admin/Elementor/User_Dashboard.php:1898, ../includes/Admin/Elementor/User_Dashboard.php:1910, ../includes/Admin/Elementor/User_Dashboard.php:1922, ../includes/Admin/Elementor/User_Dashboard.php:1934, ../includes/Admin/Elementor/User_Dashboard.php:1946
msgid "Hide"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:127, ../includes/Admin/Elementor/User_Dashboard.php:514
msgid "Avatar"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:142
msgid "Username"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:157, ../includes/Admin/Elementor/User_Dashboard.php:228, ../includes/Admin/Elementor/User_Dashboard.php:231, ../includes/Admin/Elementor/User_Dashboard.php:1651, ../includes/Admin/Elementor/User_Dashboard.php:1722
msgid "Dashboard"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:181, ../includes/Admin/Elementor/User_Dashboard.php:249, ../includes/Admin/Elementor/User_Dashboard.php:252
msgid "Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:193, ../includes/Admin/Elementor/User_Dashboard.php:686
msgid "Header"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:220
msgid "Label"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:259, ../includes/Admin/Elementor/User_Dashboard.php:262, ../includes/Admin/views/page-analytics.php:13
msgid "Refresh Stats"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:269
msgid "No Items"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:272, ../includes/Admin/views/better-payment-transaction-view.php:243, ../includes/Admin/views/template-transaction-list.php:161
msgid "No records found!"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:300
msgid "Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:437
msgid "Sidebar Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:588
msgid "Menu"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:840
msgid "Table"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:849
msgid "Table Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:969
msgid "Table Header"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1122
msgid "Table Body"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1276
msgid "Table Body » Buttons"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1285
msgid "Active Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1353
msgid "Inactive Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1421
msgid "Cancel Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1660
msgid "Transaction Summary"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1672
msgid "Analytics Report"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1684, ../includes/Admin/Elementor/User_Dashboard.php:1809, ../includes/Admin/Elementor/User_Dashboard.php:1812
msgid "Recent Transactions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1696
msgid "Recurring Subscription"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1708
msgid "Split Subscription"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1731, ../includes/Admin/Elementor/User_Dashboard.php:1734, ../includes/Admin/views/template-email-notification.php:255
msgid "Total Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1744, ../includes/Admin/Elementor/User_Dashboard.php:1747
msgid "Completed Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1757, ../includes/Admin/Elementor/User_Dashboard.php:1760
msgid "Incomplete Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1770, ../includes/Admin/Elementor/User_Dashboard.php:1773
msgid "Refunded Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1783, ../includes/Admin/Elementor/User_Dashboard.php:1786
msgid "View All"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1796, ../includes/Admin/Elementor/User_Dashboard.php:1799
msgid "Analytics Reports"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1822, ../includes/Admin/Elementor/User_Dashboard.php:1825
msgid "Recurring Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1835, ../includes/Admin/Elementor/User_Dashboard.php:1838
msgid "Split Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1850, ../includes/Admin/Elementor/User_Dashboard.php:1969
msgid "Transactions List"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1859, ../includes/Admin/Elementor/User_Dashboard.php:1978, ../includes/Admin/Elementor/User_Dashboard.php:1981, ../includes/Admin/views/template-transaction-list.php:32
msgid "Name"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1919, ../includes/Admin/Elementor/User_Dashboard.php:2043, ../includes/Admin/Elementor/User_Dashboard.php:2046, ../includes/Admin/views/better-payment-transaction-list.php:272, ../includes/Admin/views/better-payment-transaction-list.php:275, ../includes/Admin/views/template-transaction-list.php:47
msgid "Source"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1931, ../includes/Admin/Elementor/User_Dashboard.php:2056, ../includes/Admin/Elementor/User_Dashboard.php:2059, ../includes/Admin/views/better-payment-transaction-list.php:179, ../includes/Admin/views/better-payment-transaction-list.php:198, ../includes/Admin/views/template-transaction-list.php:50
msgid "Status"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1943, ../includes/Admin/Elementor/User_Dashboard.php:2069, ../includes/Admin/Elementor/User_Dashboard.php:2072, ../includes/Admin/views/template-transaction-list.php:53
msgid "Date"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:29
msgid "Save Changes"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:47
msgid "Go Premium"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:53
msgid "License"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:64
msgid "General"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:69
msgid "Admin Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:70
msgid "Customer Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:74
msgid "Payment"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:78, ../includes/Admin/views/better-payment-settings.php:119, ../includes/Admin/views/better-payment-transaction-list.php:278, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:29, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:45, ../includes/Admin/views/elementor/layouts/layout-1.php:98, ../includes/Admin/views/elementor/layouts/layout-2.php:66
msgid "Paystack"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:90, ../includes/Admin/views/better-payment-settings.php:106, ../includes/Admin/views/better-payment-settings.php:122, ../includes/Admin/views/better-payment-settings.php:139
msgid "See documentation."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:121
msgid "Enable Paystack if you want to accept payment via Paystack."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:138
msgid "Enable User Dashboard widget. It shows list of transactions and subscriptions for the user."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:187
msgid "Email address"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:188
msgid "Enter website admin email address here. This email will be used to send email notification for each transaction."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:194, ../includes/Admin/views/better-payment-settings.php:270
msgid "Email subject"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:195
msgid "Email subject for the admin email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:202
msgid "Email body for the admin email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:206, ../includes/Admin/views/better-payment-settings.php:282
msgid "Additional Headers"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:209, ../includes/Admin/views/better-payment-settings.php:285
msgid "From Name"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:212, ../includes/Admin/views/better-payment-settings.php:288
msgid "From name that will be used in the email headers."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:216, ../includes/Admin/views/better-payment-settings.php:292
msgid "From Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:219, ../includes/Admin/views/better-payment-settings.php:295
msgid "Email address that will be displayed in the email header as From Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:226, ../includes/Admin/views/better-payment-settings.php:302
msgid "Email address that will be displayed in the email header as Reply-To Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:233, ../includes/Admin/views/better-payment-settings.php:309
msgid "Email address that will be displayed in the email header as Cc Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:240, ../includes/Admin/views/better-payment-settings.php:316
msgid "Email address that will be displayed in the email header as Bcc Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:244, ../includes/Admin/views/better-payment-settings.php:320
msgid "Send As"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:248, ../includes/Admin/views/better-payment-settings.php:324
msgid "Select One"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:250, ../includes/Admin/views/better-payment-settings.php:326
msgid "Html"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:253, ../includes/Admin/views/better-payment-settings.php:329
msgid "Html helps to send html markup in the email body. Select plain if you just want plain text in the email body."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:264
msgid "Customer email address will be auto populated from payment form. This email will be used to send email notification for each transaction."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:271
msgid "Email subject for the customer (who make payments) email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:278
msgid "Email body for the customer email notification. "
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:364
msgid "Live Client ID"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:366
msgid "PayPal live client ID is required to do Refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:367, ../includes/Admin/views/better-payment-settings.php:379, ../includes/Admin/views/better-payment-settings.php:391, ../includes/Admin/views/better-payment-settings.php:403, ../includes/Admin/views/better-payment-settings.php:437, ../includes/Admin/views/better-payment-settings.php:449, ../includes/Admin/views/better-payment-settings.php:461, ../includes/Admin/views/better-payment-settings.php:473, ../includes/Admin/views/better-payment-settings.php:506, ../includes/Admin/views/better-payment-settings.php:518, ../includes/Admin/views/better-payment-settings.php:530, ../includes/Admin/views/better-payment-settings.php:542
msgid "see documentation."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:376
msgid "Live Secret"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:378
msgid "PayPal live secret is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:388
msgid "Test/Sandbox Client ID"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:390
msgid "PayPal test/sandbox client id is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:400
msgid "Test/Sandbox Secret"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:402
msgid "PayPal test/sandbox secret is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:488
msgid "Live mode allows you to process real transactions. It just requires live Paystack keys (public and secret keys) to accept real payments."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:505
msgid "Paystack live public key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:517
msgid "Paystack live secret key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:529
msgid "Paystack test public key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:541
msgid "Paystack test secret key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:589, ../includes/Admin/views/better-payment-transaction-list.php:123, ../includes/Admin/views/page-analytics.php:30
msgid "Total Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:598, ../includes/Admin/views/better-payment-transaction-list.php:135, ../includes/Admin/views/page-analytics.php:49
msgid "Completed Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:607, ../includes/Admin/views/better-payment-transaction-list.php:147, ../includes/Admin/views/page-analytics.php:68
msgid "Incomplete Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:619, ../includes/Admin/views/better-payment-settings.php:621
msgid "Documentation"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:620
msgid "Get started by spending some time with the documentation to get familiar with Better Payment."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:631
msgid "Contribute to Better Payment"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:632
msgid "You can contribute to make Better Payment better reporting bugs, creating issues, pull requests at "
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:633
msgid "Report a Bug"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:643
msgid "Need Help?"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:644
msgid "Stuck with something? Get help from live chat or support ticket."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:645
msgid "Initiate a Chat"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:654
msgid "Show Your Love"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:657
msgid "We love to have you in Better Payment family. We are making it more awesome everyday. Take your 2 minutes to review the plugin and spread the love to encourage us to keep it going."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:658
msgid "Leave a Review"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:38
msgid "Import Data"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:39
msgid "Export All"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:63
msgid "Choose csv file…"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:67
msgid "No file chosen"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:73
msgid "Let's Go!"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:78
msgid "Upload any csv file that is exported from another site via Better Payment."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:159, ../includes/Admin/views/better-payment-transaction-list.php:161, ../includes/Admin/Elementor/Controls/Select2.php:27
msgid "Search"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:161
msgid "Search by email, amount, transaction id, source"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:165
msgid "From Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:167
msgid "Date Range"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:172, ../includes/Admin/views/better-payment-transaction-list.php:174
msgid "To Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:182, ../includes/Admin/views/better-payment-transaction-list.php:199
msgid "All"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:200
msgid "Completed"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:201
msgid "Incomplete"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:202
msgid "Refunded"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:223, ../includes/Admin/views/better-payment-transaction-list.php:226
msgid "Sort By"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:227
msgid "Payment Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:250, ../includes/Admin/views/better-payment-transaction-list.php:253
msgid "Sort Order"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:254
msgid "Descending"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:255
msgid "Ascending"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:295
msgid "Filter"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:296
msgid "Reset"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:303
msgid "Custom Date Range"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:311
msgid "Start Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:316
msgid "Select start date of desired time period to see the analytics."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:324
msgid "End Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:329
msgid "Select end date of desired time period to see the analytics."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:337
msgid "Confirm"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:338
msgid "Cancel"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:41
msgid "Back to Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:54, ../includes/Admin/views/better-payment-transaction-view.php:55, ../includes/Admin/views/better-payment-transaction-view.php:58, ../includes/Admin/views/better-payment-transaction-view.php:62, ../includes/Admin/views/better-payment-transaction-view.php:65, ../includes/Admin/views/better-payment-transaction-view.php:67
msgid ""
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:82, ../includes/Admin/views/better-payment-transaction-view.php:96, ../includes/Admin/views/better-payment-transaction-view.php:97, ../includes/Admin/views/template-transaction-list.php:142, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:168
msgid "N/A"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:98
msgid "#"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:111
msgid "Basic Information"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:118
msgid "Name:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:120
msgid "Email Address:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:122, ../includes/Admin/views/better-payment-transaction-view.php:139, ../includes/Admin/views/better-payment-transaction-view.php:211, ../includes/Admin/views/template-transaction-list.php:104, ../includes/Admin/views/template-transaction-list.php:121, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:122, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:143
msgid "Copy"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:126
msgid "Single Amount:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:127
msgid "Quantity:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:128
msgid "Total Amount:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:135, ../includes/Admin/views/better-payment-transaction-view.php:209
msgid "Transaction ID:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:145
msgid "Source:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:147
msgid "Status:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:156
msgid "Mark as Completed"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:163
msgid "Additional Information"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:166
msgid "Order ID:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:167
msgid "Payment Date:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:168
msgid "Referer Page:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:169
msgid "Referer Widget:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:179
msgid "Payment Gateway"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:201, ../includes/Admin/views/better-payment-transaction-view.php:194, ../includes/Admin/views/better-payment-transaction-view.php:187
msgid "Payment Method:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:216
msgid "Email Activity"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:221
msgid "Email sent to"
msgstr ""

#: ../includes/Admin/views/page-analytics.php:13
msgid "We are caching all data for 1 hour. To see the live data press this button!"
msgstr ""

#: ../includes/Admin/views/page-analytics.php:87
msgid "Refund Transactions"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:166
msgid "email-tick"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:174
msgid "This is to acknowledge that we have received the payment of %s %s on %s"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:169
msgid "Great News! Admin"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:170
msgid "You have received a new transaction through"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:192
msgid "Transaction ID - "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:197
msgid "Date : "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:213
msgid "From"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:276
msgid "Payment Method : "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:308
msgid "Transaction Summary:"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:319
msgid "Description"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:322
msgid "Rate"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:325
msgid "Qty"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:361
msgid "Total:"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:376
msgid "You can also find the transaction details by visiting the link below."
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:377
msgid "View Transaction"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:381
msgid "Powered By"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:56
msgid "Action"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:99, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:116
msgid "Imported"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:152, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:184
msgid "View"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:153
msgid "Delete"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:174
msgid "10"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:175
msgid "20"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:176
msgid "50"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:177
msgid "100"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:28
msgid "Remove"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:29
msgid "Image"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:30
msgid "Title"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:31
msgid "Price"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:32
msgid "Quantity"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:33
msgid "Subtotal"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:246
msgid "Both"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:247
msgid "Input Field"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:261
msgid "Don't forget to enable the <strong>Payment Amount</strong> (& Show Amount List) field from Better Payment Section below"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:273
msgid "Placeholder"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:348
msgid "The value must be less than or equal to %s"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:352
msgid "The value must be greater than or equal %s"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:56
msgid "Don't forget to add PayPal or Stripe on <strong>Actions After Submit</strong>"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:77
msgid "Payment Amount Field"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:78
msgid "We add an extra field type <b>Payment Amount</b> which offers you to accept payment via Paypal and Stripe. Disable it if you want to hide the field type.<br><br>"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:134
msgid "Form Fields => Payment Amount => <b>Field Type</b> helps to show Amount List with or without Input field."
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:174
msgid "Field Style"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:250
msgid "Border Width"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:281
msgid "Amount List Style"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:58
msgid "Currency Symbols"
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:4
msgid "Why upgrade to Premium Version?"
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:5
msgid "Get access to Analytics, Refund, Invoice & many more features that makes your life way easier. You will also get world class support from our dedicated team 24/7."
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:6
msgid "Get Premium Version"
msgstr ""
