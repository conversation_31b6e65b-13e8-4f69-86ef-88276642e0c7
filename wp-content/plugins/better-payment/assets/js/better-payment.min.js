!function(t){"use strict";const e="payment_amount",{custom_texts:n}=betterPayment||{},r=n?.redirecting||"Redirecting";let a=n?.field_is_required||"field is required",i=n?.business_email_is_required||"Business Email is required",o=n?.payment_amount_field_is_required||"Payment Amount field is required",s=n?.minimum_amount_is_one||"Minimum amount is 1",l=n?.something_went_wrong||"Something went wrong";function m(t,e,n,r=!0){let l={status:!0,errors:[]},m=function(t,e,n){let r={status:!0,errors:[]};for(var i in e)if(e.hasOwnProperty(i)&&-1===n.indexOf(i)){let n=_(t[i],e[i]);if(!n.status)return r.errors.push(void 0!==n.error?n.error:`${i} ${a}!`),r.status=!1,r}return r}(n,e,["better_payment_page_id","better_payment_widget_id","return","action","security","cancel_return","payment_amount","primary_payment_amount"]);return l.status=void 0!==m.status?m.status:l.status,l.errors=void 0!==m.errors&&m.errors.constructor===Array?l.errors.concat(m.errors):l.errors,r&&!t.business_email&&(l.errors.push(`${i}!`),l.status=!1),void 0!==e.primary_payment_amount&&""!==e.primary_payment_amount||void 0!==e.primary_payment_amount_radio&&""!==e.primary_payment_amount_radio||(l.errors.push(`${o}!`),l.status=!1),void 0!==e.primary_payment_amount&&parseFloat(e.primary_payment_amount)<1&&(l.errors.push(`${s}!`),l.status=!1),l}function u(t){return void 0!==t.better_payment_payment_amount_enable&&"yes"===t.better_payment_payment_amount_enable}function p(t){let e="USD";return t.submit_actions.includes("paypal")?e=void 0!==t.better_payment_form_paypal_currency?t.better_payment_form_paypal_currency:e:t.submit_actions.includes("stripe")&&(e=void 0!==t.better_payment_form_stripe_currency?t.better_payment_form_stripe_currency:e),e}function d(t="USD"){let e="$",n={USD:"$",EUR:"€",GBP:"£",AUD:"$",CAD:"$",CZK:"Kč",DKK:"kr",HKD:"$",HUF:"ft",ILS:"₪",JPY:"¥",KES:"Ksh.",MXN:"$",NOK:"kr",NZD:"$",PHP:"₱",PLN:"zł",RUB:"₽",SGD:"$",SEK:"kr",CHF:"CHF",TWD:"$",THB:"฿",TRY:"₺"};return t in n&&(e=n[t]),e}function _(t,e,n=""){let r=function(t){let e=!1;return t.length>1||(e=!(null===t.getAttribute("required")||""===t.getAttribute("required")),e)}(t),i={error:"",status:!0};if(t.length>1)return i;if(r){let r=t.placeholder;r=r.replace(" *",""),r||(r=t.name,r=r.replace("primary","").replace("_"," ").replace(/\b\w/g,(t=>t.toUpperCase())));let o=n||`${r} ${a}!`;void 0!==e&&""!==e||(i.error=o,i.status=!1)}return i}t(document).on("click",".better-payment-paypal-bt",(function(e){const n=t("#"+this.form.id),a=t(n);let i=m(t(this).data("paypal-info"),a.serializeObject(),this.form);if(t(this).attr("disabled",!0),void 0!==i.status&&!i.status)return e.preventDefault(),toastr.error(i.errors.length?i.errors[0]:`${l}!`),this.disabled=!1,!1;t(this).attr("disabled",!1),t(this).html(`${r} <span class="elementor-control-spinner">&nbsp;<i class="eicon-spinner eicon-animation-spin"></i>&nbsp;</span>`)})),t(document).on("click",".better-payment-stripe-bt",(function(e){e.preventDefault();const n=t("#"+this.form.id),a=t(n),i=t(this),o=a.data("better-payment"),s=a.serializeObject();let u=m("",s,this.form,!1);if(void 0!==u.status&&!u.status)return e.preventDefault(),toastr.error(u.errors.length?u.errors[0]:`${l}!`),this.disabled=!1,!1;t(this).attr("disabled",!0),t.ajax({url:betterPayment.ajax_url,type:"post",data:{action:"better_payment_stripe_get_token",security:betterPayment.nonce,fields:s,setting_data:o},success:function(t){void 0!==t.data.stripe_data?(i.html(`${r} <span class="elementor-control-spinner">&nbsp;<i class="eicon-spinner eicon-animation-spin"></i>&nbsp;</span>`),Stripe(t.data.stripe_public_key).redirectToCheckout({sessionId:t.data.stripe_data}).then((function(t){}))):(i.html("Stripe"),toastr.error(t.data)),i.attr("disabled",!1),i.html("Proceed to Payment")},error:function(){i.attr("disabled",!1),console.log("Something went wrong!"),i.html("Proceed to Payment")}})})),t(document).on("click",".better-payment-paystack-bt",(function(e){e.preventDefault();const n=t("#"+this.form.id),a=t(n),i=t(this),o=a.data("better-payment"),s=a.serializeObject();let u=m("",s,this.form,!1);if(void 0!==u.status&&!u.status)return e.preventDefault(),toastr.error(u.errors.length?u.errors[0]:`${l}!`),this.disabled=!1,!1;t(this).attr("disabled",!0),t.ajax({url:betterPayment.ajax_url,type:"post",data:{action:"better_payment_paystack_get_token",security:betterPayment.nonce,fields:s,setting_data:o},success:function(t){void 0!==t.data.authorization_url?(i.html(`${r} <span class="elementor-control-spinner">&nbsp;<i class="eicon-spinner eicon-animation-spin"></i>&nbsp;</span>`),window.location.href=t.data.authorization_url):toastr.error(t.data),i.attr("disabled",!1)},error:function(){i.attr("disabled",!1),console.log("Something went wrong!")}})})),t(document).on("change",".bp-custom-payment-amount",(function(e){let n=t(this).closest(".elementor-form").data("id"),r=this.form&&this.form.id?this.form.id:"";r=""==r&&""!=n?".elementor-element-"+n:"#"+r,document.querySelectorAll(r+' .bp-payment-amount-wrap input[type="radio"]').forEach((t=>{t.checked=!1}));let a=parseFloat(this.value);t(".bp-custom-payment-amount-quantity").length&&(a*=parseFloat(t(".bp-custom-payment-amount-quantity").val())),t(".bp-transaction-details-amount-text").text(a)})),t(document).on("click",".bp-form_pay-radio",(function(e){const n=t(this);let r=parseFloat(n.val());if(""==r)return!1;n.closest("form").find(".bp-custom-payment-amount").val(r),t(".bp-custom-payment-amount-quantity").length&&(r*=parseInt(t(".bp-custom-payment-amount-quantity").val())),t(".bp-transaction-details-amount-text").text(r)})),t(window).on("elementor/frontend/init",(()=>{const t=elementorModules.frontend.handlers.Base.extend({getDefaultSettings:function(){return{selectors:{form:".elementor-form"}}},getDefaultElements:function(){let t=this.getSettings("selectors"),e={};return e.$form=this.$element.find(t.form),e},bindEvents:function(){this.elements.$form.on("submit_success",this.handleFormAction)},handleFormAction:function(t,e){void 0!==e.data.better_stripe_data&&Stripe(e.data.better_stripe_data.stripe_public_key).redirectToCheckout({sessionId:e.data.better_stripe_data.stripe_data}).then((function(t){})),void 0!==e.data.better_paystack_data&&(void 0!==e.data.better_paystack_data.authorization_url?window.location.href=e.data.better_paystack_data.authorization_url:toastr.error(response.data))}});elementorFrontend.hooks.addAction("frontend/element_ready/form.default",(e=>{elementorFrontend.elementsHandler.addHandler(t,{$element:e})})),"undefined"!=typeof elementor&&elementor.hooks.addAction("panel/open_editor/widget/form",(function(t,n,r){elementor.hooks.addFilter("elementor_pro/forms/content_template/item",(function(t,n,r){return void 0!==t.field_type&&t.field_type===e&&(t.field_type="html",t.field_html=function(t,n){let r=function(t,e){if(!u(t))return!1;return void 0!==t.better_payment_show_amount_list_enable&&"yes"===t.better_payment_show_amount_list_enable&&void 0!==e.bp_field_type&&("amount-list"===e.bp_field_type||"both"===e.bp_field_type)}(t,n),a=function(t,e){return!!u(t)&&void 0!==e.bp_field_type&&("input-field"===e.bp_field_type||"both"===e.bp_field_type)}(t,n),i="";return(r||a)&&(i+=`<label for="form-field-${e}" class="elementor-field-label">${n.field_label}</label>`,i+='<div class ="better-payment is-full-width">'),r&&(i+=function(t){if(void 0===t.better_payment_show_amount_list_items&&""===t.better_payment_show_amount_list_items)return;let e=d(p(t)),n=function(t){let e="left";return t.submit_actions.includes("paypal")?e=void 0!==t.better_payment_form_currency_alignment_paypal?t.better_payment_form_currency_alignment_paypal:e:t.submit_actions.includes("stripe")&&(e=void 0!==t.better_payment_form_currency_alignment_stripe?t.better_payment_form_currency_alignment_stripe:e),e="left"===e||"right"===e?e:"left",e}(t),r="left"===n?e:"",a="right"===n?e:"",i="",o="";if(Array.isArray(t.better_payment_show_amount_list_items)){let e,n=t.better_payment_show_amount_list_items,s="";n.forEach((t=>{e=function(t="",e=!1){return`${t}${(1e3*Date.now()+1e3*Math.random()).toString(16).replace(/\./g,"").padEnd(14,"0")}${e?`.${Math.trunc(1e8*Math.random())}`:""}`}(),s=void 0!==t.better_payment_amount_val?parseFloat(t.better_payment_amount_val):"",o+=`\n                        <div class="bp-form__group pb-3">\n                            <input type="radio" value="${s}"\n                                id="bp_payment_amount-${e}" class="bp-form__control bp-form_pay-radio "\n                                name="primary_payment_amount_radio">\n                            <label for="bp_payment_amount-${e}">${r}${s}${a}</label>\n                        </div>\n                    `})),i+=`\n                    <div class="payment-form-layout">\n                        <div class="bp-payment-amount-wrap">\n                            ${o}\n                        </div>\n                    </div>\n                `}return i}(t)),a&&(i+=function(t,n){let r=d(p(t)),a=void 0!==n.bp_placeholder?n.bp_placeholder:"";return`  <div class="better-payment-field-advanced-layout field-primary_payment_amount elementor-repeater-item-form-field-payment_amount">\n                                            <div class="control has-icons-left">\n                                                ${`<input id="form-field-${e}" class="input is-medium required bp-custom-payment-amount bp-custom-payment-amount-el-integration" type="number" placeholder="${a}" name="form_fields[${e}]" required="required" min="${n.bp_field_min}" max="${n.bp_field_max}" value="">`}\n                                                <span class="icon is-medium is-left">\n                                                    <span class="bp-currency-symbol">${r}</span>\n                                                </span>\n                                            </div>\n                                        </div>\n                                    `}(t,n)),(r||a)&&(i+="</div>"),i}(r,t)),t}),10,3)}))})),t.fn.serializeObject=function(){var e={},n=this.serializeArray();return t.each(n,(function(){e[this.name]?(e[this.name].push||(e[this.name]=[e[this.name]]),e[this.name].push(this.value||"")):e[this.name]=this.value||""})),e},toastr.options={timeOut:"2000",toastClass:"font-size-md",maxOpened:1}}(jQuery);